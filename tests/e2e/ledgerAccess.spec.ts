import { test, expect } from '@playwright/test'

test.describe('数据接入功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 清理本地存储
    await page.evaluate(() => {
      localStorage.clear()
    })
    
    // 导航到数据接入页面
    await page.goto('/dataSourceConfig/ledgerAccess')
    
    // 等待页面加载完成
    await page.waitForSelector('.ledger-access')
  })

  test('页面基本元素加载测试', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('.ledger-access')).toBeVisible()
    
    // 检查黄色提示框
    await expect(page.locator('.warning-tips')).toBeVisible()
    
    // 检查左侧分组导航
    await expect(page.locator('.left-sidebar')).toBeVisible()
    await expect(page.locator('.sidebar-header')).toContainText('数据源分组')
    
    // 检查右侧主内容区
    await expect(page.locator('.right-content')).toBeVisible()
    
    // 检查顶部操作按钮
    await expect(page.locator('text=新增')).toBeVisible()
    await expect(page.locator('text=日志管理')).toBeVisible()
    await expect(page.locator('text=集存管理')).toBeVisible()
    
    // 检查搜索表单
    await expect(page.locator('input[placeholder*="数据源名称"]')).toBeVisible()
    
    // 检查数据表格
    await expect(page.locator('.el-table')).toBeVisible()
    
    // 检查分页组件
    await expect(page.locator('.el-pagination')).toBeVisible()
  })

  test('黄色提示框功能测试', async ({ page }) => {
    // 检查提示框内容
    const warningTips = page.locator('.warning-tips')
    await expect(warningTips).toBeVisible()
    
    await expect(warningTips).toContainText('接入人员：应确保接入数据源的人员具备相关权限')
    await expect(warningTips).toContainText('数据源配置：应确保数据源配置正确')
    await expect(warningTips).toContainText('云端数据库：应确保云端数据库的可用性')
    
    // 测试关闭按钮
    await page.click('.warning-tips .close-btn')
    await expect(warningTips).not.toBeVisible()
  })

  test('左侧分组导航测试', async ({ page }) => {
    // 等待分组列表加载
    await page.waitForSelector('.group-list .group-item')

    // 检查分组项
    const groupItems = page.locator('.group-list .group-item')
    await expect(groupItems).toHaveCount(6) // 分组1-6

    // 检查添加分组按钮
    await expect(page.locator('button:has-text("添加分组")')).toBeVisible()

    // 测试分组选择
    await page.click('text=分组2')
    await expect(page.locator('.group-item.active')).toContainText('分组2')

    // 测试添加分组按钮
    await page.click('button:has-text("添加分组")')
    await expect(page.locator('.el-message')).toBeVisible()

    // 验证表格数据根据分组过滤
    await page.waitForTimeout(500) // 等待过滤完成
    const tableRows = page.locator('.el-table tbody tr')
    if (await tableRows.count() > 0) {
      // 如果有数据，检查是否都属于选中的分组
      const firstRowGroup = await page.locator('.el-table tbody tr:first-child td:nth-child(2)').textContent()
      // 注意：这里可能需要根据实际数据调整验证逻辑
    }
  })

  test('搜索和过滤功能测试', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table tbody tr')

    // 测试按数据源下拉选择
    await page.click('.el-select:has(.el-input__wrapper):first')
    await page.click('text=MySQL数据库1')
    await page.click('button:has-text("查询")')

    // 等待搜索结果
    await page.waitForTimeout(500)

    // 验证搜索结果 - 检查数据源列（第3列）
    const searchResults = page.locator('.el-table tbody tr')
    const count = await searchResults.count()
    if (count > 0) {
      const firstResult = await page.locator('.el-table tbody tr:first-child td:nth-child(3)').textContent()
      expect(firstResult?.toLowerCase()).toContain('mysql')
    }

    // 测试数据源名称搜索
    await page.fill('input[placeholder*="数据源名称"]', '主数据库')
    await page.click('button:has-text("查询")')

    await page.waitForTimeout(500)

    // 验证数据源名称搜索结果 - 检查数据源名称列（第4列）
    const nameResults = page.locator('.el-table tbody tr')
    const nameCount = await nameResults.count()
    if (nameCount > 0) {
      const firstNameResult = await page.locator('.el-table tbody tr:first-child td:nth-child(4)').textContent()
      expect(firstNameResult?.toLowerCase()).toContain('主数据库')
    }

    // 清空搜索条件
    await page.fill('input[placeholder*="数据源名称"]', '')
    await page.click('.el-select:has(.el-input__wrapper):first')
    await page.click('text=全部')

    // 测试状态过滤
    await page.click('.el-select:has(.el-input__wrapper):last')
    await page.click('text=已完成')
    await page.click('button:has-text("查询")')

    await page.waitForTimeout(500)

    // 验证状态过滤结果
    const statusResults = page.locator('.el-table tbody tr')
    const statusCount = await statusResults.count()
    if (statusCount > 0) {
      const firstStatus = page.locator('.el-table tbody tr:first-child .el-tag')
      await expect(firstStatus).toContainText('已完成')
    }
  })

  test('数据表格显示测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 检查表格列头
    await expect(page.locator('th:has-text("序号")')).toBeVisible()
    await expect(page.locator('th:has-text("所属分组")')).toBeVisible()
    await expect(page.locator('th:has-text("数据源")')).toBeVisible()
    await expect(page.locator('th:has-text("数据源名称")')).toBeVisible()
    await expect(page.locator('th:has-text("接入状态")')).toBeVisible()
    await expect(page.locator('th:has-text("接入进度")')).toBeVisible()
    await expect(page.locator('th:has-text("操作")')).toBeVisible()
    
    // 检查数据行
    const dataRows = page.locator('.el-table tbody tr')
    const rowCount = await dataRows.count()
    expect(rowCount).toBeGreaterThan(0)
    
    // 检查状态标签
    const statusTags = page.locator('.el-tag')
    await expect(statusTags.first()).toBeVisible()
    
    // 检查进度条
    const progressBars = page.locator('.el-progress')
    await expect(progressBars.first()).toBeVisible()
    
    // 检查操作按钮（根据状态动态显示）
    const actionButtons = page.locator('.el-table tbody tr:first-child button')
    await expect(actionButtons).toHaveCount(3) // 应该有3个按钮
    await expect(page.locator('button:has-text("删除")')).toBeVisible()
    await expect(page.locator('button:has-text("更多")')).toBeVisible()
  })

  test('新增数据接入功能测试', async ({ page }) => {
    // 点击新增按钮
    await page.click('button:has-text("新增")')

    // 等待弹窗出现
    await expect(page.locator('.el-dialog')).toBeVisible()
    await expect(page.locator('.el-dialog__title')).toContainText('新增数据接入')

    // 填写表单
    await page.click('.el-select:has-text("请选择所属分组")')
    await page.click('text=分组1')

    // 选择数据源
    await page.click('.el-select:has-text("请选择数据源")')
    await page.click('text=MySQL数据库1')

    // 输入数据源名称
    await page.fill('input[placeholder*="请输入数据源名称"]', '测试MySQL数据接入')

    await page.fill('textarea[placeholder*="接入配置"]', '{"host": "localhost", "port": 3306, "database": "test_db"}')

    // 提交表单
    await page.click('.el-dialog button:has-text("确定")')

    // 等待提交完成
    await page.waitForTimeout(1000)

    // 验证弹窗关闭
    await expect(page.locator('.el-dialog')).not.toBeVisible()

    // 验证成功消息
    await expect(page.locator('.el-message--success')).toBeVisible()

    // 验证新记录出现在表格中
    await page.waitForTimeout(500)
    await expect(page.locator('text=测试MySQL数据接入')).toBeVisible()
  })

  test('表格操作功能测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.el-table tbody tr')

    // 获取第一行的状态
    const firstRowStatus = await page.locator('.el-table tbody tr:first-child .el-tag').textContent()

    // 根据状态测试相应的操作按钮
    if (firstRowStatus?.includes('已完成')) {
      // 已完成状态：查看/删除/更多
      await expect(page.locator('.el-table tbody tr:first-child button:has-text("查看")')).toBeVisible()
      await page.click('.el-table tbody tr:first-child button:has-text("查看")')
      await expect(page.locator('.el-message')).toBeVisible()
    } else if (firstRowStatus?.includes('运行中')) {
      // 运行中状态：中断/删除/更多
      await expect(page.locator('.el-table tbody tr:first-child button:has-text("中断")')).toBeVisible()
      await page.click('.el-table tbody tr:first-child button:has-text("中断")')
      await expect(page.locator('.el-message--success')).toBeVisible()
    } else if (firstRowStatus?.includes('已暂停') || firstRowStatus?.includes('接入失败')) {
      // 已暂停或接入失败状态：重试/删除/更多
      await expect(page.locator('.el-table tbody tr:first-child button:has-text("重试")')).toBeVisible()
      await page.click('.el-table tbody tr:first-child button:has-text("重试")')
      await expect(page.locator('.el-message--success')).toBeVisible()
    }

    // 验证固定按钮存在
    await expect(page.locator('.el-table tbody tr:first-child button:has-text("删除")')).toBeVisible()
    await expect(page.locator('.el-table tbody tr:first-child button:has-text("更多")')).toBeVisible()

    // 测试删除功能
    const initialRowCount = await page.locator('.el-table tbody tr').count()
    await page.click('.el-table tbody tr:first-child button:has-text("删除")')

    // 确认删除
    await page.click('.el-popconfirm button:has-text("确定")')

    // 验证删除成功
    await page.waitForTimeout(500)
    const newRowCount = await page.locator('.el-table tbody tr').count()
    expect(newRowCount).toBe(initialRowCount - 1)
  })

  test('分页功能测试', async ({ page }) => {
    // 等待表格和分页组件加载
    await page.waitForSelector('.el-pagination')
    
    // 检查分页信息
    const pagination = page.locator('.el-pagination')
    await expect(pagination).toBeVisible()
    
    // 如果有多页数据，测试翻页
    const nextButton = page.locator('.el-pagination .btn-next')
    if (await nextButton.isEnabled()) {
      await nextButton.click()
      await page.waitForTimeout(500)
      
      // 验证页码变化
      await expect(page.locator('.el-pagination .number.is-active')).toContainText('2')
    }
    
    // 测试每页显示数量变更
    const pageSizeSelect = page.locator('.el-pagination .el-select')
    if (await pageSizeSelect.count() > 0) {
      await pageSizeSelect.click()
      await page.click('text=20')
      await page.waitForTimeout(500)
      
      // 验证表格数据更新
      const rows = await page.locator('.el-table tbody tr').count()
      expect(rows).toBeLessThanOrEqual(20)
    }
  })

  test('数据持久化测试', async ({ page }) => {
    // 添加一条新记录
    await page.click('button:has-text("新增")')
    await page.waitForSelector('.el-dialog')
    
    await page.click('.el-select:has-text("请选择所属分组")')
    await page.click('text=分组1')
    
    await page.click('.el-select:has-text("请选择数据源类型")')
    await page.click('text=数据库数据源')
    
    await page.fill('input[placeholder*="数据源名称"]', '持久化测试数据源')
    await page.fill('textarea[placeholder*="接入配置"]', '{"host": "localhost", "port": 3306}')
    
    await page.click('.el-dialog button:has-text("确定")')
    await page.waitForTimeout(1000)
    
    // 刷新页面
    await page.reload()
    await page.waitForSelector('.ledger-access')
    await page.waitForSelector('.el-table tbody tr')
    
    // 验证数据仍然存在
    await expect(page.locator('text=持久化测试数据源')).toBeVisible()
  })

  test('状态显示和按钮逻辑测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.el-table tbody tr')

    // 检查状态标签是否包含图标和中文文本
    const statusTags = page.locator('.el-table .el-tag')
    const firstTag = statusTags.first()
    await expect(firstTag).toBeVisible()

    // 检查状态标签是否包含图标
    const statusIcon = firstTag.locator('i')
    await expect(statusIcon).toBeVisible()

    // 验证不同状态对应的按钮
    const tableRows = page.locator('.el-table tbody tr')
    const rowCount = await tableRows.count()

    for (let i = 0; i < Math.min(rowCount, 5); i++) {
      const row = tableRows.nth(i)
      const statusText = await row.locator('.el-tag').textContent()
      const buttons = row.locator('button')

      // 验证每行都有3个按钮
      await expect(buttons).toHaveCount(3)

      // 验证固定按钮存在
      await expect(row.locator('button:has-text("删除")')).toBeVisible()
      await expect(row.locator('button:has-text("更多")')).toBeVisible()

      // 根据状态验证第一个按钮
      if (statusText?.includes('已完成')) {
        await expect(row.locator('button:has-text("查看")')).toBeVisible()
      } else if (statusText?.includes('运行中')) {
        await expect(row.locator('button:has-text("中断")')).toBeVisible()
      } else if (statusText?.includes('已暂停') || statusText?.includes('接入失败')) {
        await expect(row.locator('button:has-text("重试")')).toBeVisible()
      }
    }
  })

  test('响应式布局测试', async ({ page }) => {
    // 测试不同屏幕尺寸
    await page.setViewportSize({ width: 1200, height: 800 })
    await expect(page.locator('.left-sidebar')).toBeVisible()

    await page.setViewportSize({ width: 768, height: 600 })
    await page.waitForTimeout(500)

    // 在小屏幕下，左侧边栏应该仍然可见但可能布局有变化
    await expect(page.locator('.left-sidebar')).toBeVisible()

    // 恢复正常尺寸
    await page.setViewportSize({ width: 1920, height: 1080 })
  })

  test('认证配置功能测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.el-table tbody tr')

    // 点击第一行的"更多"按钮
    await page.click('.el-table tbody tr:first-child button:has-text("更多")')

    // 等待下拉菜单出现
    await page.waitForSelector('.el-dropdown-menu')

    // 点击"认证配置"选项
    await page.click('.el-dropdown-item:has-text("认证配置")')

    // 等待认证配置弹窗出现
    await expect(page.locator('.el-dialog')).toBeVisible()
    await expect(page.locator('.el-dialog__title')).toContainText('数据接入安全认证规则设置')

    // 检查弹窗中的数据源信息提示
    await expect(page.locator('.el-alert')).toBeVisible()
    await expect(page.locator('.el-alert .el-alert__title')).toContainText('正在为数据源')

    // 检查认证方式选项
    await expect(page.locator('input[value="apiKey"]')).toBeChecked()
    await expect(page.locator('text=API Key')).toBeVisible()
    await expect(page.locator('text=OAuth 2.0')).toBeVisible()

    // 检查API Key配置项
    await expect(page.locator('input[placeholder*="请输入"]').first()).toBeVisible() // API Key名称
    await expect(page.locator('input[placeholder*="请输入"]').nth(1)).toBeVisible() // 访问密钥
    await expect(page.locator('input[type="password"]')).toBeVisible() // 密钥

    // 检查有效期设置
    await expect(page.locator('.el-select')).toBeVisible()

    // 检查过期提醒方式
    await expect(page.locator('text=站内信')).toBeVisible()
    await expect(page.locator('text=邮快证')).toBeVisible()
    await expect(page.locator('text=邮快证DING')).toBeVisible()

    // 检查备注输入框
    await expect(page.locator('textarea[placeholder*="请输入"]')).toBeVisible()

    // 关闭弹窗
    await page.click('.el-dialog button:has-text("取消")')
    await expect(page.locator('.el-dialog')).not.toBeVisible()
  })

  test('认证配置表单验证测试', async ({ page }) => {
    // 等待表格加载并打开认证配置弹窗
    await page.waitForSelector('.el-table tbody tr')
    await page.click('.el-table tbody tr:first-child button:has-text("更多")')
    await page.waitForSelector('.el-dropdown-menu')
    await page.click('.el-dropdown-item:has-text("认证配置")')
    await page.waitForSelector('.el-dialog')

    // 直接点击确认按钮，测试表单验证
    await page.click('.el-dialog button:has-text("确认")')

    // 验证API Key名称必填提示
    await expect(page.locator('.el-message--warning')).toBeVisible()
    await expect(page.locator('text=请输入API Key名称')).toBeVisible()

    // 填写API Key名称
    await page.fill('input[placeholder*="请输入"]', 'test-api-key')
    await page.click('.el-dialog button:has-text("确认")')

    // 验证访问密钥必填提示
    await expect(page.locator('text=请输入访问密钥')).toBeVisible()

    // 填写访问密钥
    await page.fill('input[placeholder*="请输入"]', 'test-access-key')
    await page.click('.el-dialog button:has-text("确认")')

    // 验证密钥必填提示
    await expect(page.locator('text=请输入密钥')).toBeVisible()

    // 填写密钥
    await page.fill('input[type="password"]', 'test-secret-key')
    await page.click('.el-dialog button:has-text("确认")')

    // 验证有效期设置必填提示
    await expect(page.locator('text=请选择有效期设置')).toBeVisible()

    // 选择有效期
    await page.click('.el-select')
    await page.click('text=30天')
    await page.click('.el-dialog button:has-text("确认")')

    // 验证过期提醒方式必填提示
    await expect(page.locator('text=请至少选择一个过期提醒方式')).toBeVisible()

    // 选择过期提醒方式
    await page.check('text=站内信')
    await page.click('.el-dialog button:has-text("确认")')

    // 验证保存成功
    await expect(page.locator('.el-message--success')).toBeVisible()
    await expect(page.locator('.el-dialog')).not.toBeVisible()
  })

  test('认证配置数据隔离测试', async ({ page }) => {
    // 为第一个数据源配置认证
    await page.waitForSelector('.el-table tbody tr')
    const firstRowName = await page.locator('.el-table tbody tr:first-child td:nth-child(4)').textContent()

    await page.click('.el-table tbody tr:first-child button:has-text("更多")')
    await page.waitForSelector('.el-dropdown-menu')
    await page.click('.el-dropdown-item:has-text("认证配置")')
    await page.waitForSelector('.el-dialog')

    // 填写第一个数据源的认证配置
    await page.fill('input[placeholder*="请输入"]', 'first-api-key')
    await page.fill('input[placeholder*="请输入"]', 'first-access-key')
    await page.fill('input[type="password"]', 'first-secret-key')
    await page.click('.el-select')
    await page.click('text=30天')
    await page.check('text=站内信')
    await page.fill('textarea[placeholder*="请输入"]', '第一个数据源的认证配置')
    await page.click('.el-dialog button:has-text("确认")')

    await page.waitForTimeout(1000)

    // 为第二个数据源配置认证
    const secondRow = page.locator('.el-table tbody tr').nth(1)
    if (await secondRow.count() > 0) {
      const secondRowName = await secondRow.locator('td:nth-child(4)').textContent()

      await secondRow.locator('button:has-text("更多")').click()
      await page.waitForSelector('.el-dropdown-menu')
      await page.click('.el-dropdown-item:has-text("认证配置")')
      await page.waitForSelector('.el-dialog')

      // 验证弹窗显示的是第二个数据源的信息
      await expect(page.locator('.el-alert .el-alert__title')).toContainText(secondRowName || '')

      // 验证表单是空的（证明配置是隔离的）
      await expect(page.locator('input[placeholder*="请输入"]').first()).toHaveValue('')
      await expect(page.locator('input[placeholder*="请输入"]').nth(1)).toHaveValue('')
      await expect(page.locator('input[type="password"]')).toHaveValue('')
      await expect(page.locator('textarea[placeholder*="请输入"]')).toHaveValue('')

      // 填写第二个数据源的不同配置
      await page.fill('input[placeholder*="请输入"]', 'second-api-key')
      await page.fill('input[placeholder*="请输入"]', 'second-access-key')
      await page.fill('input[type="password"]', 'second-secret-key')
      await page.click('.el-select')
      await page.click('text=60天')
      await page.check('text=邮快证')
      await page.fill('textarea[placeholder*="请输入"]', '第二个数据源的认证配置')
      await page.click('.el-dialog button:has-text("确认")')

      await page.waitForTimeout(1000)

      // 重新打开第一个数据源的认证配置，验证配置被保存且隔离
      await page.click('.el-table tbody tr:first-child button:has-text("更多")')
      await page.waitForSelector('.el-dropdown-menu')
      await page.click('.el-dropdown-item:has-text("认证配置")')
      await page.waitForSelector('.el-dialog')

      // 验证第一个数据源的配置仍然存在
      await expect(page.locator('input[placeholder*="请输入"]').first()).toHaveValue('first-api-key')
      await expect(page.locator('input[placeholder*="请输入"]').nth(1)).toHaveValue('first-access-key')
      await expect(page.locator('input[type="password"]')).toHaveValue('first-secret-key')
      await expect(page.locator('textarea[placeholder*="请输入"]')).toHaveValue('第一个数据源的认证配置')

      await page.click('.el-dialog button:has-text("取消")')
    }
  })

  test('认证配置持久化测试', async ({ page }) => {
    // 配置认证信息
    await page.waitForSelector('.el-table tbody tr')
    await page.click('.el-table tbody tr:first-child button:has-text("更多")')
    await page.waitForSelector('.el-dropdown-menu')
    await page.click('.el-dropdown-item:has-text("认证配置")')
    await page.waitForSelector('.el-dialog')

    await page.fill('input[placeholder*="请输入"]', 'persistent-api-key')
    await page.fill('input[placeholder*="请输入"]', 'persistent-access-key')
    await page.fill('input[type="password"]', 'persistent-secret-key')
    await page.click('.el-select')
    await page.click('text=90天')
    await page.check('text=站内信')
    await page.check('text=邮快证')
    await page.fill('textarea[placeholder*="请输入"]', '持久化测试配置')
    await page.click('.el-dialog button:has-text("确认")')

    await page.waitForTimeout(1000)

    // 刷新页面
    await page.reload()
    await page.waitForSelector('.ledger-access')
    await page.waitForSelector('.el-table tbody tr')

    // 重新打开认证配置，验证数据持久化
    await page.click('.el-table tbody tr:first-child button:has-text("更多")')
    await page.waitForSelector('.el-dropdown-menu')
    await page.click('.el-dropdown-item:has-text("认证配置")')
    await page.waitForSelector('.el-dialog')

    // 验证配置被正确保存和加载
    await expect(page.locator('input[placeholder*="请输入"]').first()).toHaveValue('persistent-api-key')
    await expect(page.locator('input[placeholder*="请输入"]').nth(1)).toHaveValue('persistent-access-key')
    await expect(page.locator('input[type="password"]')).toHaveValue('persistent-secret-key')
    await expect(page.locator('textarea[placeholder*="请输入"]')).toHaveValue('持久化测试配置')

    // 验证复选框状态
    await expect(page.locator('input[type="checkbox"]').first()).toBeChecked() // 站内信
    await expect(page.locator('input[type="checkbox"]').nth(1)).toBeChecked() // 邮快证
    await expect(page.locator('input[type="checkbox"]').nth(2)).not.toBeChecked() // 邮快证DING

    await page.click('.el-dialog button:has-text("取消")')
  })
})
