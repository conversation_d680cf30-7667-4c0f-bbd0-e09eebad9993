<script setup lang="ts" name="DataAccess">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 数据源分组实体（独立实体）
interface DataSourceGroup {
  id: string // 分组ID
  name: string // 分组名称
  description?: string // 分组描述
  createTime: string
  createUser: string
  updateTime?: string
  updateUser?: string
}

// 数据源分组关联实体（核心关联表）
interface DataSourceGroupRelation {
  id: string // 关联ID
  groupId: string // 分组ID
  dataSourceId: string // 数据源ID
  accessName: string // 接入任务名称
  accessType: string // 接入类型：extract（抽取）、sync（同步）等
  accessConfig?: any // 接入配置参数
  createTime: string
  createUser: string
  updateTime?: string
  updateUser?: string
}

// 数据接入记录类型定义（基于关联实体）
interface DataAccessRecord {
  id: string // 接入记录ID
  relationId: string // 关联实体ID（关键：指向DataSourceGroupRelation）
  index: number
  status: '已完成' | '接入失败' | '运行中' | '已暂停'
  progress: number
  speed?: string
  startTime?: string // 开始时间
  endTime?: string // 结束时间
  errorMessage?: string // 错误信息
  createTime: string
  createUser: string
  // 运行时计算的字段（不存储）
  relationInfo?: DataSourceGroupRelation
  dataSourceInfo?: {
    name: string
    sourceType: string
    description?: string
    status?: boolean
  }
  groupInfo?: {
    name: string
  }
}

// 认证配置实体（独立实体）
interface AuthConfig {
  id: string // 认证配置ID
  relationId: string // 关联实体ID（指向DataSourceGroupRelation）
  authMethod: 'apiKey' | 'oauth2'
  apiKeyName?: string
  accessKey?: string
  secretKey?: string
  validityPeriod: string
  expireWakeupMethods: {
    siteMessage: boolean
    email: boolean
    dingTalk: boolean
  }
  remarks?: string
  createTime: string
  createUser: string
  updateTime?: string
  updateUser?: string
}

// 数据接入日志实体
interface DataAccessLog {
  id: string // 日志ID
  logId: string // 日志编号（如：10001, 10002）
  dataSource: string // 数据源名称
  accessType: string // 接入方式（数据抽样接入、全量数据接入、增量数据接入等）
  operationTime: string // 操作时间
  createTime: string
  createUser: string
}

// 分组类型定义
interface GroupItem {
  id: string
  name: string
  type: 'dataSource' | 'environment'
  children?: GroupItem[]
}

// 数据订阅相关接口定义
// 触发条件配置
interface TriggerCondition {
  accessStatus: string[] // 接入状态：['成功', '失败']
  accessCount?: {
    operator: string // 操作符：'请选择'
    value: string // 数值：如'1000'
  }
  timeRange?: {
    startTime: string // 开始时间
    endTime: string // 结束时间
  }
}

// 通知方式配置
interface NotificationMethods {
  siteMessage: boolean // 站内信
  email: boolean // 愉快证
  dingTalk: boolean // 愉快DING
}

// 数据订阅规则实体
interface DataSubscriptionRule {
  id: string // 订阅规则ID
  dataSourceId: string // 关联数据源ID
  dataSourceName: string // 数据源名称（冗余字段，便于显示）
  triggerCondition: TriggerCondition // 触发条件
  subscriptionFrequency: 'realtime' | 'weekly' | 'monthly' // 订阅频率：实时/每周/每月
  notificationMethods: NotificationMethods // 通知方式
  createTime: string
  createUser: string
  updateTime?: string
  updateUser?: string
}

// 数据订阅表单数据
interface SubscriptionFormData {
  dataSourceId: string // 关联数据源
  triggerCondition: {
    accessStatus: string[] // 接入状态多选
    accessCount: {
      operator: string // 下拉选择
      value: string // 输入框数值
    }
    timeRange: {
      startTime: string // 开始时间
      endTime: string // 结束时间
    }
  }
  subscriptionFrequency: string // 订阅频率
  notificationMethods: {
    siteMessage: boolean
    email: boolean
    dingTalk: boolean
  }
}

// 模型分析数据接口
interface ModelAnalysisData {
  id: string // 分析记录ID
  recordId: string // 关联的数据接入记录ID
  accuracy: number // 准确率（0-1，保留3位小数）
  recall: number // 召回率（0-1，保留3位小数）
  f1Score: number // F1分数（根据准确率和召回率计算）
  createTime: string // 创建时间
  createUser: string // 创建用户
}

// 深度学习模型数据分析接口
interface DLModelDataAnalysis {
  id: string // 分析记录ID
  modelId: string // 关联的深度学习模型ID
  performanceMetrics: {
    epochs: number[] // epoch数组 [1, 10, 20, 30, 40, 50]
    accuracy: number[] // 准确率数组，对应每个epoch
    recall: number[] // 召回率数组，对应每个epoch
  }
  lossCurves: {
    epochs: number[] // epoch数组 [1, 10, 20, 30, 40, 50]
    trainLoss: number[] // 训练损失数组，对应每个epoch
    validationLoss: number[] // 验证损失数组，对应每个epoch
  }
  createTime: string // 创建时间
  createUser: string // 创建用户
}

// 本地存储键名
const STORAGE_KEY = 'dataAccess_records'
const RELATION_STORAGE_KEY = 'dataSourceGroup_relations' // 数据源分组关联
const AUTH_CONFIG_STORAGE_KEY = 'dataAccess_authConfigs' // 认证配置
const GROUPS_STORAGE_KEY = 'dataSourceGroups' // 数据源分组
const LOGS_STORAGE_KEY = 'dataAccess_logs' // 数据接入日志
const SUBSCRIPTION_STORAGE_KEY = 'dataAccess_subscriptions' // 数据订阅规则
const MODEL_ANALYSIS_STORAGE_KEY = 'dataAccess_modelAnalysis' // 模型分析数据
const DL_MODEL_DATA_ANALYSIS_STORAGE_KEY = 'dataAccess_dlModelDataAnalysis' // 深度学习模型数据分析

// 数据源选项
const dataSourceOptions = ref<Array<{label: string, value: string}>>([])

// 搜索表单
const searchFormProp = computed(() => [
  {
    label: '数据源',
    prop: 'dataSource',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      ...dataSourceOptions.value
    ]
  },
  {
    label: '数据源名称',
    prop: 'dataSourceName',
    type: 'text'
  },
  {
    label: '接入状态',
    prop: 'status',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '已完成', value: '已完成' },
      { label: '接入失败', value: '接入失败' },
      { label: '运行中', value: '运行中' },
      { label: '已暂停', value: '已暂停' }
    ]
  }
])
const searchForm = ref({ dataSource: '', dataSourceName: '', status: '' })

// 加载状态
const loading = ref(false)

// 表格ref和高度
const tableRef = ref()
const tableHeight = ref(400)
const currentRow = ref<DataAccessRecord | null>(null)

// 动态操作按钮配置
const getActionButtons = (record: DataAccessRecord) => {
  const buttons = []

  // 第一个按钮：根据状态变化
  if (record.status === '已完成') {
    buttons.push({ label: '查看', type: 'info' as const, code: 'view' })
  } else if (record.status === '已暂停') {
    buttons.push({ label: '重试', type: 'success' as const, code: 'retry' })
  } else if (record.status === '运行中') {
    buttons.push({ label: '中断', type: 'warning' as const, code: 'interrupt' })
  } else if (record.status === '接入失败') {
    buttons.push({ label: '重试', type: 'success' as const, code: 'retry' })
  }

  // 固定的后三个按钮
  buttons.push({ label: '删除', type: 'danger' as const, code: 'delete', popconfirm: '确认删除吗?' })
  buttons.push({ label: '模型分析', type: 'primary' as const, code: 'model-analysis' })
  buttons.push({ label: '更多', type: 'primary' as const, code: 'more', dropdown: true })

  return buttons
}

// 更多按钮的下拉菜单选项
const getMoreMenuOptions = () => [
  { label: '认证配置', code: 'auth-config' },
  { label: '数据加密', code: 'data-encrypt' },
  { label: '接口参数调整', code: 'api-params' },
  { label: '质量评估', code: 'quality-assess' },
  { label: '清洗规则配置', code: 'clean-rules' },
  { label: '清洗效果评估', code: 'clean-effect' },
  { label: '数据归档', code: 'data-archive' },
  { label: '备份恢复规则', code: 'backup-rules' },
  { label: '数据脱敏', code: 'data-mask' },
  { label: '数据版本', code: 'data-version' },
  { label: '存储加密与解密', code: 'storage-encrypt' },
  { label: '权限管理', code: 'permission' },
  { label: '故障诊断', code: 'fault-diagnosis' }
]

// 表头配置（基于ID关联显示）
const columns = [
  { prop: 'index', label: '序号', width: 80 },
  {
    prop: 'groupInfo',
    label: '所属分组',
    width: 120,
    formatter: (row: DataAccessRecord) => {
      return row.groupInfo?.name || '未知分组'
    }
  },
  {
    prop: 'dataSourceInfo',
    label: '数据源',
    width: 120,
    formatter: (row: DataAccessRecord) => {
      return row.dataSourceInfo?.name || '数据源已删除'
    }
  },
  {
    prop: 'relationInfo',
    label: '接入任务名称',
    minWidth: 150,
    formatter: (row: DataAccessRecord) => {
      return row.relationInfo?.accessName || '未知任务'
    }
  },
  { prop: 'status', label: '接入状态', width: 120 },
  { prop: 'progress', label: '接入进度', width: 150 }
]

// 分页配置
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 列表请求参数
const reqParams = reactive({
  dataSource: '',
  dataSourceName: '',
  status: '',
  groupId: '',
  skipCount: 0,
  maxResultCount: 10
})

// 表格数据
const tableData = ref<DataAccessRecord[]>([])
const allRecords = ref<DataAccessRecord[]>([])

// 分组数据
const groupList = ref<DataSourceGroup[]>([])
const selectedGroupId = ref('')

// 分组编辑状态
const editingGroupId = ref('')
const editingGroupName = ref('')
const originalGroupName = ref('') // 保存原始名称，用于取消编辑时恢复

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()

// 分步骤弹窗状态
const currentStep = ref(1)
const maxStep = 2

// 第一步：数据接入配置
const step1Form = ref({
  groupId: '',
  accessMethod: 'auto', // 'auto' | 'manual'
  selectedDataSources: [] as string[],
  accessType: 'extract', // 'extract' | 'full' | 'increment'
  version: ''
})

// 第二步：接入顺序
const step2Form = ref({
  dataSourceOrder: [] as Array<{
    id: string
    name: string
    description: string
    order: number
  }>
})

// 稳定性检测相关
const showStabilityTest = ref(false)
const stabilityTestResult = ref('')

// 日志清理规则弹窗相关
const showLogCleanDialog = ref(false)
const logCleanForm = ref({
  cleanPeriod: '请选择',           // 清理周期
  retentionDays: '',              // 保留时长（天）
  cleanTime: '请选择',            // 清理时间
  cleanRangeSettings: {           // 清理范围设置
    cleanSuccessLogs: false,      // 清理成功的日志记录
    cleanErrorLogs: false,        // 清理错误的日志记录
    cleanFormatConvertLogs: false // 清理格式转换日志
  }
})

// 清理周期选项
const cleanPeriodOptions = [
  { label: '每天', value: 'daily' },
  { label: '每周', value: 'weekly' },
  { label: '每月', value: 'monthly' },
  { label: '自定义', value: 'custom' }
]

// 清理时间选项（时分秒）
const cleanTimeOptions = [
  { label: '00:00', value: '00:00' },
  { label: '01:00', value: '01:00' },
  { label: '02:00', value: '02:00' },
  { label: '03:00', value: '03:00' },
  { label: '04:00', value: '04:00' },
  { label: '05:00', value: '05:00' },
  { label: '06:00', value: '06:00' }
]

// 缓存查看弹窗相关
const showCacheViewDialog = ref(false)
const cacheSearchForm = ref({
  dataSourceName: ''  // 数据源名称搜索
})

// 缓存数据列表
const cacheDataList = ref([
  {
    id: 'CACHE-2025-001',
    cacheId: 'CACHE-2025',
    dataSourceName: '数据源名称1',
    size: '2.4MB',
    createTime: '2025.7.17 14:30',
    expireTime: '2023-11-27 14:30',
    status: 'normal' // normal: 正常, expired: 已过期
  },
  {
    id: 'CACHE-2025-002',
    cacheId: 'CACHE-2025',
    dataSourceName: '数据源名称1',
    size: '2.4MB',
    createTime: '2025.7.17 14:30',
    expireTime: '',
    status: 'expired'
  },
  {
    id: 'CACHE-2025-003',
    cacheId: 'CACHE-2025',
    dataSourceName: '数据源名称1',
    size: '2.4MB',
    createTime: '2025.7.17 14:30',
    expireTime: '',
    status: 'expired'
  },
  {
    id: 'CACHE-2025-004',
    cacheId: 'CACHE-2025',
    dataSourceName: '数据源名称1',
    size: '2.4MB',
    createTime: '2025.7.17 14:30',
    expireTime: '2023-11-27 14:30',
    status: 'normal'
  },
  {
    id: 'CACHE-2025-005',
    cacheId: 'CACHE-2025',
    dataSourceName: '数据源名称1',
    size: '2.4MB',
    createTime: '2025.7.17 14:30',
    expireTime: '2023-11-27 14:30',
    status: 'normal'
  }
])

// 过滤后的缓存数据
const filteredCacheData = computed(() => {
  if (!cacheSearchForm.value.dataSourceName) {
    return cacheDataList.value
  }

  return cacheDataList.value.filter(item =>
    item.dataSourceName.includes(cacheSearchForm.value.dataSourceName)
  )
})

// 自动检测到的数据源列表
const detectedDataSources = ref([
  { id: '1', name: '数据源1', description: 'MySQL@*************:3306' },
  { id: '2', name: '数据源2', description: 'MySQL@*************:3306' },
  { id: '3', name: '数据源1', description: 'MySQL@*************:3306' },
  { id: '4', name: '数据源2', description: 'MySQL@*************:3306' }
])

// 黄色提示框显示状态
const showWarningTips = ref(true)

// 获取当前步骤的表单配置
const getCurrentStepFormProps = computed(() => {
  if (currentStep.value === 1) {
    return [
      {
        label: '所属分组',
        prop: 'groupId',
        type: 'select',
        options: groupList.value
          .map((g: DataSourceGroup) => ({ label: g.name, value: g.id }))
      }
    ]
  }
  return []
})

// 表单验证规则
const dialogFormRules = {
  groupId: [{ required: true, message: '请选择所属分组', trigger: 'change' }],
  dataSource: [{ required: true, message: '请选择数据源类型', trigger: 'change' }],
  dataSourceName: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
  config: [{ required: true, message: '请输入接入配置', trigger: 'blur' }]
}



// 获取实际数据源列表
const getActualDataSources = () => {
  try {
    const cached = localStorage.getItem('dataSourceManagement_data')
    console.log('从localStorage读取的数据源数据:', cached)
    if (cached) {
      const dataSourceList = JSON.parse(cached)
      console.log('解析后的数据源列表:', dataSourceList)
      // 返回所有数据源，不过滤状态，让用户看到完整的数据源列表
      return dataSourceList
    }
  } catch (error) {
    console.error('获取数据源列表失败:', error)
  }

  // 如果没有数据源管理的数据，返回空数组
  console.log('没有找到数据源管理数据，返回空数组')
  return []
}

// 根据数据源ID获取数据源信息
const getDataSourceById = (dataSourceId: string) => {
  const actualDataSources = getActualDataSources()
  return actualDataSources.find((ds: any) => ds.id === dataSourceId)
}

// 关联实体管理函数
const allRelations = ref<DataSourceGroupRelation[]>([])

// 获取所有关联关系
const getRelations = (): DataSourceGroupRelation[] => {
  try {
    const cached = localStorage.getItem(RELATION_STORAGE_KEY)
    if (cached) {
      return JSON.parse(cached)
    }
  } catch (error) {
    console.error('获取关联关系失败:', error)
  }
  return []
}

// 保存关联关系
const saveRelations = () => {
  localStorage.setItem(RELATION_STORAGE_KEY, JSON.stringify(allRelations.value))
}

// 根据关联ID获取关联信息
const getRelationById = (relationId: string): DataSourceGroupRelation | undefined => {
  return allRelations.value.find(rel => rel.id === relationId)
}

// 分组管理函数
const getGroups = (): DataSourceGroup[] => {
  try {
    const cached = localStorage.getItem(GROUPS_STORAGE_KEY)
    if (cached) {
      return JSON.parse(cached)
    }
  } catch (error) {
    console.error('获取分组列表失败:', error)
  }
  return []
}

const saveGroups = () => {
  localStorage.setItem(GROUPS_STORAGE_KEY, JSON.stringify(groupList.value))
}

// 初始化分组
const initializeGroups = (): DataSourceGroup[] => {
  const savedGroups = getGroups()
  if (savedGroups.length > 0) {
    return savedGroups
  }

  // 创建默认分组
  const defaultGroups: DataSourceGroup[] = [
    {
      id: 'group_1',
      name: '分组1',
      description: '默认数据源分组',
      createTime: new Date().toISOString(),
      createUser: '系统'
    }
  ]

  localStorage.setItem(GROUPS_STORAGE_KEY, JSON.stringify(defaultGroups))
  return defaultGroups
}

// 添加新分组
const addGroup = () => {
  const newGroup: DataSourceGroup = {
    id: `group_${Date.now()}`,
    name: `分组${groupList.value.length + 1}`,
    createTime: new Date().toISOString(),
    createUser: '当前用户'
  }

  groupList.value.push(newGroup)
  saveGroups()

  // 自动进入编辑模式
  startEditGroup(newGroup.id, newGroup.name)
}

// 开始编辑分组名称
const startEditGroup = (groupId: string, currentName: string) => {
  editingGroupId.value = groupId
  editingGroupName.value = currentName
  originalGroupName.value = currentName // 保存原始名称

  // 添加全局点击监听器
  nextTick(() => {
    document.addEventListener('click', handleGlobalClick)
  })
}

// 全局点击处理器
const handleGlobalClick = (event: Event) => {
  const target = event.target as HTMLElement
  // 如果点击的不是编辑输入框，则完成编辑
  if (!target.closest('.el-input')) {
    finishEditGroup()
    document.removeEventListener('click', handleGlobalClick)
  }
}

// 完成编辑分组名称（失去焦点或按Enter时自动保存）
const finishEditGroup = () => {
  if (editingGroupId.value && editingGroupName.value.trim()) {
    const group = groupList.value.find(g => g.id === editingGroupId.value)
    if (group && editingGroupName.value.trim() !== originalGroupName.value) {
      // 只有名称真正改变时才保存
      group.name = editingGroupName.value.trim()
      group.updateTime = new Date().toISOString()
      group.updateUser = '当前用户'
      saveGroups()
      ElMessage.success('分组名称已更新')
    }
  }
  // 清空编辑状态
  editingGroupId.value = ''
  editingGroupName.value = ''
  originalGroupName.value = ''
  // 移除全局监听器
  document.removeEventListener('click', handleGlobalClick)
}

// 取消编辑分组名称（按Esc时恢复原名称）
const cancelEditGroup = () => {
  editingGroupName.value = originalGroupName.value // 恢复原始名称
  editingGroupId.value = ''
  editingGroupName.value = ''
  originalGroupName.value = ''
  // 移除全局监听器
  document.removeEventListener('click', handleGlobalClick)
}

// 删除分组
const deleteGroup = (groupId: string) => {
  // 检查是否有关联的数据源
  const hasRelations = allRelations.value.some(rel => rel.groupId === groupId)
  if (hasRelations) {
    ElMessage.warning('该分组下还有数据源，无法删除')
    return
  }

  const index = groupList.value.findIndex(g => g.id === groupId)
  if (index > -1) {
    groupList.value.splice(index, 1)
    saveGroups()

    // 如果删除的是当前选中的分组，清空选择
    if (selectedGroupId.value === groupId) {
      selectedGroupId.value = ''
      reqParams.groupId = ''
      loadTableData()
    }

    ElMessage.success('分组删除成功')
  }
}

// 为数据接入记录补充关联信息和数据源信息
const enrichRecordsWithDataSourceInfo = (records: DataAccessRecord[]): DataAccessRecord[] => {
  return records.map(record => {
    // 获取关联信息
    const relationInfo = getRelationById(record.relationId)
    if (!relationInfo) {
      return {
        ...record,
        relationInfo: undefined,
        dataSourceInfo: undefined,
        groupInfo: undefined
      }
    }

    // 获取数据源信息
    const dataSourceInfo = getDataSourceById(relationInfo.dataSourceId)

    // 获取分组信息
    const groupInfo = groupList.value.find(g => g.id === relationInfo.groupId)

    return {
      ...record,
      relationInfo,
      dataSourceInfo: dataSourceInfo ? {
        name: dataSourceInfo.name,
        sourceType: dataSourceInfo.sourceType,
        description: dataSourceInfo.description,
        status: dataSourceInfo.status
      } : undefined,
      groupInfo: groupInfo ? {
        name: groupInfo.name
      } : undefined
    }
  })
}

// 初始化数据记录（只加载已保存的记录，不自动生成）
const initializeRecords = (): DataAccessRecord[] => {
  console.log('开始初始化数据记录...')

  // 只加载已保存的记录，不自动生成新记录
  const savedRecords = localStorage.getItem(STORAGE_KEY)
  if (savedRecords) {
    try {
      const records = JSON.parse(savedRecords)
      console.log('找到已保存的接入记录:', records.length, '条')
      return records
    } catch (error) {
      console.error('解析保存的记录失败:', error)
    }
  }

  console.log('没有找到已保存的接入记录，返回空数组')
  return []
}

// 从localStorage获取数据源列表
const loadDataSourceOptions = () => {
  console.log('开始加载数据源选项...')
  const actualDataSources = getActualDataSources()
  console.log('获取到的实际数据源:', actualDataSources)

  if (actualDataSources.length > 0) {
    // 基于实际数据源生成选项
    dataSourceOptions.value = actualDataSources.map((item: any) => ({
      label: item.name,
      value: item.name
    }))

    // 同时更新检测到的数据源列表，基于实际数据源
    detectedDataSources.value = actualDataSources.map((item: any) => ({
      id: item.id,
      name: item.name,
      description: `${item.sourceType}@${item.description || '数据库连接'}`
    }))

    console.log('数据源选项已更新:', dataSourceOptions.value)
    console.log('检测到的数据源已更新:', detectedDataSources.value)
  } else {
    // 如果没有实际数据源，清空选项
    dataSourceOptions.value = []
    detectedDataSources.value = []
    console.log('没有找到数据源，已清空选项')
  }
}

// 保存记录到本地存储
const saveRecords = () => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(allRecords.value))
}

// 同步数据源变化（基于关联实体，只处理删除，不自动创建新记录）
const syncWithDataSources = () => {
  console.log('开始同步数据源变化...')
  const actualDataSources = getActualDataSources()
  console.log('当前实际数据源:', actualDataSources)

  if (actualDataSources.length === 0) {
    console.log('没有实际数据源，清空所有关联和接入记录')
    allRelations.value = []
    allRecords.value = []
    saveRelations()
    saveRecords()
    return
  }

  const currentDataSourceIds = actualDataSources.map((ds: any) => ds.id)
  console.log('当前数据源ID列表:', currentDataSourceIds)

  // 移除已删除数据源的关联关系
  const originalRelationCount = allRelations.value.length
  allRelations.value = allRelations.value.filter(relation =>
    currentDataSourceIds.includes(relation.dataSourceId)
  )
  const removedRelationCount = originalRelationCount - allRelations.value.length

  // 移除已删除关联的接入记录
  const validRelationIds = allRelations.value.map(rel => rel.id)
  const originalRecordCount = allRecords.value.length
  allRecords.value = allRecords.value.filter(record =>
    validRelationIds.includes(record.relationId)
  )
  const removedRecordCount = originalRecordCount - allRecords.value.length

  if (removedRelationCount > 0 || removedRecordCount > 0) {
    console.log(`移除了 ${removedRelationCount} 条关联关系，${removedRecordCount} 条接入记录`)
    saveRelations()
    saveRecords()
  }

  console.log('数据源同步完成，当前关联数量:', allRelations.value.length, '接入记录数量:', allRecords.value.length)
}

// 加载表格数据
const loadTableData = () => {
  loading.value = true

  setTimeout(() => {
    // 先为记录补充数据源信息
    let filteredData = enrichRecordsWithDataSourceInfo([...allRecords.value])

    // 按分组过滤（基于关联信息中的分组ID）
    if (reqParams.groupId) {
      filteredData = filteredData.filter(record =>
        record.relationInfo?.groupId === reqParams.groupId
      )
    }

    // 按数据源过滤（基于数据源名称，从dataSourceInfo中获取）
    if (reqParams.dataSource) {
      filteredData = filteredData.filter(record =>
        record.dataSourceInfo?.name === reqParams.dataSource
      )
    }

    // 按数据源名称搜索（从关联信息的accessName和dataSourceInfo中搜索）
    if (reqParams.dataSourceName) {
      const searchTerm = reqParams.dataSourceName.toLowerCase()
      filteredData = filteredData.filter(record =>
        (record.relationInfo?.accessName && record.relationInfo.accessName.toLowerCase().includes(searchTerm)) ||
        (record.dataSourceInfo?.name && record.dataSourceInfo.name.toLowerCase().includes(searchTerm))
      )
    }

    // 按状态过滤
    if (reqParams.status) {
      filteredData = filteredData.filter(record => record.status === reqParams.status)
    }

    pagination.total = filteredData.length

    // 分页处理
    const start = reqParams.skipCount
    const end = start + reqParams.maxResultCount
    tableData.value = filteredData.slice(start, end)

    loading.value = false
  }, 300) // 模拟加载延迟
}

// 搜索功能
const onSearch = () => {
  pagination.page = 1
  reqParams.skipCount = 0
  reqParams.maxResultCount = pagination.size
  reqParams.dataSource = searchForm.value.dataSource
  reqParams.dataSourceName = searchForm.value.dataSourceName
  reqParams.status = searchForm.value.status
  loadTableData()
}

// 分组选择变化
const onGroupChange = (groupId: string) => {
  selectedGroupId.value = groupId
  reqParams.groupId = groupId
  pagination.page = 1
  reqParams.skipCount = 0
  loadTableData()
}

// 表格操作点击事件
const onTableClickButton = ({ row, btn }: any) => {
  if (btn.code === 'view') {
    ElMessage.info(`查看数据源: ${row.dataSourceName}`)
  } else if (btn.code === 'interrupt') {
    // 中断运行中的任务
    if (row.status === '运行中') {
      console.log('中断任务:', row.id)

      // 找到实际存储的记录并修改
      const actualRecord = allRecords.value.find(record => record.id === row.id)
      if (!actualRecord) {
        ElMessage.error('未找到对应的接入记录')
        return
      }

      actualRecord.status = '已暂停'
      actualRecord.speed = ''

      // 同时更新显示的行数据
      row.status = '已暂停'
      row.speed = ''

      saveRecords()
      ElMessage.success('已暂停数据接入')

      // 刷新表格数据
      loadTableData()
    } else {
      ElMessage.warning('只能中断运行中的任务')
    }
  } else if (btn.code === 'retry') {
    // 重试暂停或失败的任务
    if (row.status === '已暂停' || row.status === '接入失败') {
      console.log('开始重试任务:', row.id, '当前状态:', row.status)

      // 找到实际存储的记录并修改
      const actualRecord = allRecords.value.find(record => record.id === row.id)
      if (!actualRecord) {
        ElMessage.error('未找到对应的接入记录')
        return
      }

      const originalStatus = actualRecord.status
      actualRecord.status = '运行中'
      // 失败的任务重试时进度重置为0，暂停的任务保持原进度
      actualRecord.progress = originalStatus === '接入失败' ? 0 : actualRecord.progress
      actualRecord.speed = ''
      actualRecord.startTime = new Date().toISOString()
      actualRecord.errorMessage = undefined

      // 同时更新显示的行数据
      row.status = '运行中'
      row.progress = actualRecord.progress
      row.speed = ''

      saveRecords()
      // 重新开始模拟接入过程
      simulateDataAccess(row.id)
      ElMessage.success('已重新开始数据接入')

      // 刷新表格数据以确保显示正确
      loadTableData()
    } else {
      ElMessage.warning('只能重试暂停或失败的任务')
    }
  } else if (btn.code === 'delete') {
    handleDelete(row.id)
  } else if (btn.code === 'model-analysis') {
    // 模型分析按钮点击处理
    const analysisData = getOrGenerateAnalysisData(row.id)
    currentAnalysisData.value = analysisData
    showModelAnalysisDialog.value = true
  } else if (btn.code === 'more') {
    ElMessage.info('更多操作功能')
  }
}

// 删除记录
const handleDelete = (id: string) => {
  const index = allRecords.value.findIndex(record => record.id === id)
  if (index > -1) {
    allRecords.value.splice(index, 1)
    saveRecords()
    loadTableData()
    ElMessage.success('删除成功')
  }
}

// 更多菜单点击事件
const onMoreMenuClick = (row: DataAccessRecord, command: string) => {
  const menuLabels: Record<string, string> = {
    'auth-config': '认证配置',
    'data-encrypt': '数据加密',
    'api-params': '接口参数调整',
    'quality-assess': '质量评估',
    'clean-rules': '清洗规则配置',
    'clean-effect': '清洗效果评估',
    'data-archive': '数据归档',
    'backup-rules': '备份恢复规则',
    'data-mask': '数据脱敏',
    'data-version': '数据版本',
    'storage-encrypt': '存储加密与解密',
    'permission': '权限管理',
    'fault-diagnosis': '故障诊断'
  }

  const label = menuLabels[command] || command
  const displayName = row.dataSourceInfo?.name || row.relationInfo?.accessName || '未知任务'
  ElMessage.info(`${label} - ${displayName}`)

  // 这里可以根据不同的命令执行不同的操作
  switch (command) {
    case 'auth-config':
      // 打开认证配置弹窗，传递关联信息
      currentRelationId.value = row.relationId
      showAuthConfigDialog.value = true
      break
    case 'data-encrypt':
      // 打开数据加密配置
      console.log('打开数据加密配置:', row)
      break
    case 'quality-assess':
      // 打开质量评估页面
      console.log('打开质量评估:', row)
      break
    case 'fault-diagnosis':
      // 打开故障诊断
      console.log('打开故障诊断:', row)
      break
    default:
      console.log('更多菜单操作:', command, row)
  }
}

// 日志管理菜单点击事件
const onLogManageClick = (command: string) => {
  switch (command) {
    case 'log-record':
      openLogDialog()
      break
    case 'log-clean':
      // 打开日志清理规则弹窗
      showLogCleanDialog.value = true
      break
    default:
      console.log('日志管理操作:', command)
  }
}

// 缓存管理菜单点击事件
const onCacheManageClick = (command: string) => {
  switch (command) {
    case 'cache-config':
      ElMessage.info('缓存配置功能')
      console.log('打开缓存配置页面')
      break
    case 'cache-view':
      // 打开缓存查看弹窗
      showCacheViewDialog.value = true
      break
    default:
      console.log('缓存管理操作:', command)
  }
}

// 顶部更多菜单点击事件
const onMoreTopMenuClick = (command: string) => {
  const menuLabels: Record<string, string> = {
    'data-monitor': '数据接入监控指标',
    'data-alert': '数据接入报警',
    'quality-config': '质量评估指标设置',
    'feedback-manage': '反馈管理',
    'rule-config': '规则设置',
    'training': '文档与培训',
    'process-control': '过程控制',
    'interface-rules': '数据接入接口规则',
    'task-progress': '接入任务调度',
    'data-read': '错误数据',
    'operation-system': '兼容操作系统',
    'data-push': '数据推送',
    'data-index': '数据索引',
    'cloud-resource': '云资源管理与部署'
  }

  const label = menuLabels[command] || command
  ElMessage.info(`${label}功能`)

  switch (command) {
    case 'data-monitor':
      console.log('打开数据接入监控指标页面')
      break
    case 'data-alert':
      console.log('打开数据接入报警配置')
      break
    case 'quality-config':
      console.log('打开质量评估指标设置')
      break
    default:
      console.log('顶部更多菜单操作:', command)
  }
}

// 新增按钮点击
const onClickAdd = () => {
  console.log('点击新增按钮，刷新数据源信息...')

  // 刷新最新的数据源信息
  loadDataSourceOptions()

  currentRow.value = null
  currentStep.value = 1
  step1Form.value = {
    groupId: '',
    accessMethod: 'auto',
    selectedDataSources: [],
    accessType: 'extract',
    version: ''
  }
  step2Form.value = {
    dataSourceOrder: []
  }

  console.log('当前可用数据源数量:', dataSourceOptions.value.length)
  console.log('检测到的数据源:', detectedDataSources.value)

  showDialogForm.value = true
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
    reqParams.skipCount = (val - 1) * pagination.size
  } else {
    pagination.size = val
    reqParams.maxResultCount = pagination.size
  }
  loadTableData()
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 120
}

// 下一步
const onNextStep = () => {
  if (currentStep.value === 1) {
    // 验证第一步表单
    if (!step1Form.value.groupId) {
      ElMessage.warning('请选择所属分组')
      return
    }

    if (step1Form.value.accessMethod === 'auto') {
      // 自动检测模式，检查是否选择了数据源
      if (step1Form.value.selectedDataSources.length === 0) {
        ElMessage.warning('请选择要接入的数据源')
        return
      }
    } else {
      // 指定数据源模式，检查是否选择了数据源
      if (step1Form.value.selectedDataSources.length === 0) {
        ElMessage.warning('请选择数据源')
        return
      }
    }

    if (!step1Form.value.accessType) {
      ElMessage.warning('请选择数据接入类型')
      return
    }

    // 准备第二步数据
    prepareStep2Data()
    currentStep.value = 2
  }
}

// 上一步
const onPrevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// 准备第二步数据
const prepareStep2Data = () => {
  const selectedSources = step1Form.value.selectedDataSources

  if (step1Form.value.accessMethod === 'auto') {
    // 自动检测模式
    const sourceList = detectedDataSources.value.filter(ds => selectedSources.includes(ds.id))
    step2Form.value.dataSourceOrder = sourceList.map((source, index) => ({
      id: source.id,
      name: source.name,
      description: source.description,
      order: index + 1
    }))
  } else {
    // 指定数据源模式
    const sourceList = dataSourceOptions.value.filter(ds => selectedSources.includes(ds.value))
    step2Form.value.dataSourceOrder = sourceList.map((source, index) => ({
      id: source.value,
      name: source.label,
      description: source.label,
      order: index + 1
    }))
  }
}

// 稳定性检测
const onStabilityTest = () => {
  showStabilityTest.value = true

  // 模拟检测过程
  setTimeout(() => {
    stabilityTestResult.value = '当前稳定性状态良好！'
  }, 2000)
}

// 关闭稳定性检测弹窗
const closeStabilityTest = () => {
  showStabilityTest.value = false
  stabilityTestResult.value = ''
}

// 最终提交
const onFinalSubmit = async () => {
  if (loading.value) return

  loading.value = true

  setTimeout(() => {
    const selectedGroup = groupList.value.find(g => g.id === step1Form.value.groupId)

    // 为每个选中的数据源创建关联实体和接入记录
    step2Form.value.dataSourceOrder.forEach((dataSource, index) => {
      const relationId = `rel_${Date.now()}_${index}`

      // 创建关联实体
      const newRelation: DataSourceGroupRelation = {
        id: relationId,
        groupId: step1Form.value.groupId,
        dataSourceId: dataSource.id,
        accessName: `${dataSource.name}接入任务`,
        accessType: step1Form.value.accessType || 'extract',
        createTime: new Date().toISOString(),
        createUser: '当前用户'
      }

      // 创建接入记录
      const newRecord: DataAccessRecord = {
        id: `rec_${Date.now()}_${index}`,
        relationId: relationId,
        index: allRecords.value.length + index + 1,
        status: '运行中',
        progress: 0,
        speed: '',
        startTime: new Date().toISOString(),
        createTime: new Date().toISOString(),
        createUser: '当前用户'
      }

      allRelations.value.push(newRelation)
      allRecords.value.push(newRecord)

      // 添加日志记录
      const accessTypeMap: Record<string, string> = {
        'extract': '数据抽样接入',
        'sync': '全量数据接入',
        'increment': '增量数据接入'
      }
      const accessTypeName = accessTypeMap[newRelation.accessType] || newRelation.accessType
      addLog(dataSource.name, accessTypeName)

      // 开始模拟接入过程
      simulateDataAccess(newRecord.id)
    })

    saveRelations()
    saveRecords()
    loading.value = false
    showDialogForm.value = false
    ElMessage.success(`成功创建${step2Form.value.dataSourceOrder.length}个数据接入任务`)

    // 刷新表格数据
    loadTableData()
  }, 1000)
}

// 上移数据源
const moveUp = (index: number) => {
  if (index > 0) {
    const temp = step2Form.value.dataSourceOrder[index]
    step2Form.value.dataSourceOrder[index] = step2Form.value.dataSourceOrder[index - 1]
    step2Form.value.dataSourceOrder[index - 1] = temp
  }
}

// 下移数据源
const moveDown = (index: number) => {
  if (index < step2Form.value.dataSourceOrder.length - 1) {
    const temp = step2Form.value.dataSourceOrder[index]
    step2Form.value.dataSourceOrder[index] = step2Form.value.dataSourceOrder[index + 1]
    step2Form.value.dataSourceOrder[index + 1] = temp
  }
}

// 日志清理规则弹窗相关函数
const onLogCleanDialogOpen = () => {
  // 弹窗打开时加载已保存的配置
  const savedConfig = localStorage.getItem('logCleanConfig')
  if (savedConfig) {
    try {
      const config = JSON.parse(savedConfig)
      logCleanForm.value = {
        cleanPeriod: config.cleanPeriod || '请选择',
        retentionDays: config.retentionDays || '',
        cleanTime: config.cleanTime || '请选择',
        cleanRangeSettings: {
          cleanSuccessLogs: config.cleanRangeSettings?.cleanSuccessLogs || false,
          cleanErrorLogs: config.cleanRangeSettings?.cleanErrorLogs || false,
          cleanFormatConvertLogs: config.cleanRangeSettings?.cleanFormatConvertLogs || false
        }
      }
    } catch (error) {
      console.error('加载日志清理配置失败:', error)
      // 使用默认配置
      logCleanForm.value = {
        cleanPeriod: '请选择',
        retentionDays: '',
        cleanTime: '请选择',
        cleanRangeSettings: {
          cleanSuccessLogs: false,
          cleanErrorLogs: false,
          cleanFormatConvertLogs: false
        }
      }
    }
  } else {
    // 使用默认配置
    logCleanForm.value = {
      cleanPeriod: '请选择',
      retentionDays: '',
      cleanTime: '请选择',
      cleanRangeSettings: {
        cleanSuccessLogs: false,
        cleanErrorLogs: false,
        cleanFormatConvertLogs: false
      }
    }
  }
}

const onLogCleanDialogClose = () => {
  // 弹窗关闭时重置表单
  showLogCleanDialog.value = false
}

const onLogCleanSubmit = () => {
  // 验证表单
  if (logCleanForm.value.cleanPeriod === '请选择') {
    ElMessage.warning('请选择清理周期')
    return
  }

  if (!logCleanForm.value.retentionDays) {
    ElMessage.warning('请输入保留时长')
    return
  }

  if (logCleanForm.value.cleanTime === '请选择') {
    ElMessage.warning('请选择清理时间')
    return
  }

  // 检查是否至少选择了一个清理范围
  const { cleanSuccessLogs, cleanErrorLogs, cleanFormatConvertLogs } = logCleanForm.value.cleanRangeSettings
  if (!cleanSuccessLogs && !cleanErrorLogs && !cleanFormatConvertLogs) {
    ElMessage.warning('请至少选择一个清理范围')
    return
  }

  // 保存配置到本地存储
  const logCleanConfig = {
    ...logCleanForm.value,
    updateTime: new Date().toISOString(),
    updateUser: '当前用户'
  }

  localStorage.setItem('logCleanConfig', JSON.stringify(logCleanConfig))

  ElMessage.success('日志清理规则配置已保存')
  showLogCleanDialog.value = false

  console.log('日志清理规则配置:', logCleanConfig)
}

// 缓存查看弹窗相关函数
const onCacheViewDialogOpen = () => {
  // 弹窗打开时重置搜索条件
  cacheSearchForm.value.dataSourceName = ''
}

const onCacheViewDialogClose = () => {
  // 弹窗关闭时重置状态
  showCacheViewDialog.value = false
  cacheSearchForm.value.dataSourceName = ''
}

const onCacheSearch = () => {
  // 搜索功能已通过computed实现，这里可以添加额外的搜索逻辑
  console.log('搜索缓存数据:', cacheSearchForm.value.dataSourceName)
}

const onCacheReset = () => {
  // 重置搜索条件
  cacheSearchForm.value.dataSourceName = ''
  ElMessage.info('搜索条件已重置')
}

// 格式化缓存大小显示
const formatCacheSize = (size: string) => {
  return size
}

// 格式化过期时间显示
const formatExpireTime = (expireTime: string, status: string) => {
  if (status === 'expired') {
    return '已过期'
  }
  return expireTime || '已过期'
}

// 获取过期时间的样式类
const getExpireTimeClass = (status: string) => {
  return status === 'expired' ? 'expired-text' : ''
}

// 认证配置弹窗相关函数
const onAuthConfigDialogOpen = () => {
  // 获取当前关联信息
  const currentRelation = getRelationById(currentRelationId.value)
  if (!currentRelation) {
    ElMessage.error('未找到关联信息')
    return
  }

  // 获取数据源信息
  const dataSourceInfo = getDataSourceById(currentRelation.dataSourceId)
  const displayName = dataSourceInfo?.name || currentRelation.accessName

  // 弹窗打开时加载该关联的已保存配置
  const savedConfig = localStorage.getItem(`authConfig_${currentRelationId.value}`)
  if (savedConfig) {
    try {
      const config = JSON.parse(savedConfig)
      authConfigForm.value = {
        dataSourceId: currentRelation.dataSourceId,
        dataSourceName: displayName,
        authMethod: config.authMethod || 'apiKey',
        apiKeyName: config.apiKeyName || '',
        accessKey: config.accessKey || '',
        secretKey: config.secretKey || '',
        validityPeriod: config.validityPeriod || '请选择密钥过期时间',
        expireWakeupMethods: {
          siteMessage: config.expireWakeupMethods?.siteMessage || false,
          email: config.expireWakeupMethods?.email || false,
          dingTalk: config.expireWakeupMethods?.dingTalk || false
        },
        remarks: config.remarks || ''
      }
    } catch (error) {
      console.error('加载认证配置失败:', error)
      resetAuthConfigForm(currentRelation, displayName)
    }
  } else {
    resetAuthConfigForm(currentRelation, displayName)
  }
}

const resetAuthConfigForm = (relation?: DataSourceGroupRelation, displayName?: string) => {
  const currentRelation = relation || getRelationById(currentRelationId.value)
  const name = displayName || (currentRelation ? getDataSourceById(currentRelation.dataSourceId)?.name || currentRelation.accessName : '')

  authConfigForm.value = {
    dataSourceId: currentRelation?.dataSourceId || '',
    dataSourceName: name,
    authMethod: 'apiKey',
    apiKeyName: '',
    accessKey: '',
    secretKey: '',
    validityPeriod: '请选择密钥过期时间',
    expireWakeupMethods: {
      siteMessage: false,
      email: false,
      dingTalk: false
    },
    remarks: ''
  }
}

const onAuthConfigDialogClose = () => {
  // 弹窗关闭时重置状态
  showAuthConfigDialog.value = false
}

const onAuthConfigSubmit = () => {
  // 验证表单
  if (authConfigForm.value.authMethod === 'apiKey') {
    if (!authConfigForm.value.apiKeyName) {
      ElMessage.warning('请输入API Key名称')
      return
    }
    if (!authConfigForm.value.accessKey) {
      ElMessage.warning('请输入访问密钥')
      return
    }
    if (!authConfigForm.value.secretKey) {
      ElMessage.warning('请输入密钥')
      return
    }
  }

  if (authConfigForm.value.validityPeriod === '请选择密钥过期时间') {
    ElMessage.warning('请选择有效期设置')
    return
  }

  // 检查是否至少选择了一个过期提醒方式
  const { siteMessage, email, dingTalk } = authConfigForm.value.expireWakeupMethods
  if (!siteMessage && !email && !dingTalk) {
    ElMessage.warning('请至少选择一个过期提醒方式')
    return
  }

  // 保存配置到本地存储，使用数据源ID作为键
  const authConfig = {
    ...authConfigForm.value,
    updateTime: new Date().toISOString(),
    updateUser: '当前用户'
  }

  localStorage.setItem(`authConfig_${currentRelationId.value}`, JSON.stringify(authConfig))

  ElMessage.success(`数据源"${authConfigForm.value.dataSourceName}"的认证配置已保存`)
  showAuthConfigDialog.value = false

  console.log('认证配置:', authConfig)
}

// 认证配置弹窗相关
const showAuthConfigDialog = ref(false)
const currentRelationId = ref('')  // 当前配置的关联ID

// 日志管理相关
const showLogDialog = ref(false)
const allLogs = ref<DataAccessLog[]>([])
const logSearchForm = ref({
  dataSource: '',
  operationTime: ''
})
const logTableData = ref<DataAccessLog[]>([])
const logTableLoading = ref(false)

// 日志管理函数
const getLogs = (): DataAccessLog[] => {
  try {
    const cached = localStorage.getItem(LOGS_STORAGE_KEY)
    if (cached) {
      return JSON.parse(cached)
    }
  } catch (error) {
    console.error('获取日志列表失败:', error)
  }
  return []
}

const saveLogs = () => {
  localStorage.setItem(LOGS_STORAGE_KEY, JSON.stringify(allLogs.value))
}

// 添加日志记录
const addLog = (dataSourceName: string, accessType: string) => {
  const logId = (allLogs.value.length + 10001).toString() // 从10001开始编号
  const newLog: DataAccessLog = {
    id: `log_${Date.now()}`,
    logId: logId,
    dataSource: dataSourceName,
    accessType: accessType,
    operationTime: new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).replace(/\//g, '.'),
    createTime: new Date().toISOString(),
    createUser: '当前用户'
  }

  allLogs.value.unshift(newLog) // 新日志插入到最前面
  saveLogs()
  console.log('添加日志记录:', newLog)
}

// 打开日志管理弹窗
const openLogDialog = () => {
  allLogs.value = getLogs()
  logTableData.value = [...allLogs.value]
  showLogDialog.value = true
}

// 搜索日志
const searchLogs = () => {
  logTableLoading.value = true

  setTimeout(() => {
    let filteredLogs = [...allLogs.value]

    // 按数据源过滤
    if (logSearchForm.value.dataSource.trim()) {
      filteredLogs = filteredLogs.filter(log =>
        log.dataSource.includes(logSearchForm.value.dataSource.trim())
      )
    }

    // 按操作时间过滤
    if (logSearchForm.value.operationTime.trim()) {
      filteredLogs = filteredLogs.filter(log =>
        log.operationTime.includes(logSearchForm.value.operationTime.trim())
      )
    }

    logTableData.value = filteredLogs
    logTableLoading.value = false
  }, 300)
}

// 重置搜索
const resetLogSearch = () => {
  logSearchForm.value = {
    dataSource: '',
    operationTime: ''
  }
  logTableData.value = [...allLogs.value]
}
const authConfigForm = ref({
  dataSourceId: '',               // 数据源ID
  dataSourceName: '',             // 数据源名称
  authMethod: 'apiKey',           // 认证方式: apiKey | oauth2
  apiKeyName: '',                 // API Key 名称
  accessKey: '',                  // 访问密钥
  secretKey: '',                  // 密钥
  validityPeriod: '请选择密钥过期时间',  // 有效期设置
  expireWakeupMethods: {          // 过期提醒方式
    siteMessage: false,           // 站内信
    email: false,                 // 邮快证
    dingTalk: false              // 邮快证DING
  },
  remarks: ''                     // 备注
})

// 有效期选项
const validityPeriodOptions = [
  { label: '30天', value: '30' },
  { label: '60天', value: '60' },
  { label: '90天', value: '90' },
  { label: '180天', value: '180' },
  { label: '365天', value: '365' },
  { label: '永不过期', value: 'never' }
]

// 数据订阅弹窗相关
const showSubscriptionDialog = ref(false)
const subscriptionLoading = ref(false)
const subscriptionFormRef = ref()
const allSubscriptions = ref<DataSubscriptionRule[]>([])

// 触发条件控制变量
const enableAccessCount = ref(false)
const enableTimeRange = ref(false)

// 数据订阅表单数据
const subscriptionForm = ref<SubscriptionFormData>({
  dataSourceId: '',
  triggerCondition: {
    accessStatus: [],
    accessCount: {
      operator: '请选择',
      value: ''
    },
    timeRange: {
      startTime: '',
      endTime: ''
    }
  },
  subscriptionFrequency: 'realtime',
  notificationMethods: {
    siteMessage: false,
    email: false,
    dingTalk: false
  }
})

// 接入状态选项
const accessStatusOptions = [
  { label: '成功', value: '成功' },
  { label: '失败', value: '失败' }
]

// 接入数量操作符选项
const accessCountOperatorOptions = [
  { label: '请选择', value: '请选择' },
  { label: '大于', value: '大于' },
  { label: '小于', value: '小于' },
  { label: '等于', value: '等于' }
]

// 订阅频率选项
const subscriptionFrequencyOptions = [
  { label: '实时', value: 'realtime' },
  { label: '每周', value: 'weekly' },
  { label: '每月', value: 'monthly' }
]

// 模拟数据接入过程
const simulateDataAccess = (recordId: string) => {
  console.log('开始模拟数据接入:', recordId)
  const record = allRecords.value.find(r => r.id === recordId)
  if (!record) {
    console.error('未找到接入记录:', recordId)
    return
  }
  if (record.status !== '运行中') {
    console.log('记录状态不是运行中:', record.status)
    return
  }

  console.log('开始接入模拟，当前进度:', record.progress)
  const interval = setInterval(() => {
    if (record.status !== '运行中') {
      console.log('接入状态已改变，停止模拟:', record.status)
      clearInterval(interval)
      return
    }

    const progressIncrement = Math.floor(Math.random() * 10) + 5
    record.progress = Math.min(record.progress + progressIncrement, 100)
    record.speed = `${Math.floor(Math.random() * 5) + 1}M/s`

    console.log(`接入进度更新: ${record.progress}%, 速度: ${record.speed}`)

    if (record.progress >= 100) {
      record.progress = 100
      record.status = Math.random() > 0.2 ? '已完成' : '接入失败' // 80%成功率
      record.speed = ''
      record.endTime = new Date().toISOString()
      if (record.status === '接入失败') {
        record.errorMessage = '模拟接入失败：网络连接超时'
      }
      console.log('接入完成，最终状态:', record.status)
      clearInterval(interval)
    }

    saveRecords()
    // 如果当前记录在显示的表格中，更新显示
    const displayRecord = tableData.value.find(r => r.id === recordId)
    if (displayRecord) {
      displayRecord.progress = record.progress
      displayRecord.status = record.status
      displayRecord.speed = record.speed
      console.log('更新显示记录:', displayRecord.progress, displayRecord.status)
    }
  }, 1000)
}

// 模型分析相关函数
// 生成随机的模型分析数据
const generateModelAnalysisData = (recordId: string): ModelAnalysisData => {
  // 生成0-1范围内的随机数，保留3位小数
  const accuracy = Math.round(Math.random() * 1000) / 1000
  const recall = Math.round(Math.random() * 1000) / 1000

  // 计算F1分数：F1 = 2 * (准确率 * 召回率) / (准确率 + 召回率)
  const f1Score = accuracy + recall === 0 ? 0 : Math.round((2 * accuracy * recall) / (accuracy + recall) * 1000) / 1000

  return {
    id: `analysis_${Date.now()}`,
    recordId: recordId,
    accuracy: accuracy,
    recall: recall,
    f1Score: f1Score,
    createTime: new Date().toISOString(),
    createUser: '当前用户'
  }
}

// 获取模型分析数据
const getModelAnalysisData = (): ModelAnalysisData[] => {
  try {
    const cached = localStorage.getItem(MODEL_ANALYSIS_STORAGE_KEY)
    if (cached) {
      return JSON.parse(cached)
    }
  } catch (error) {
    console.error('获取模型分析数据失败:', error)
  }
  return []
}

// 保存模型分析数据
const saveModelAnalysisData = (analysisData: ModelAnalysisData[]) => {
  localStorage.setItem(MODEL_ANALYSIS_STORAGE_KEY, JSON.stringify(analysisData))
}

// 根据记录ID获取或生成模型分析数据
const getOrGenerateAnalysisData = (recordId: string): ModelAnalysisData => {
  const allAnalysisData = getModelAnalysisData()

  // 查找是否已有该记录的分析数据
  let existingData = allAnalysisData.find(data => data.recordId === recordId)

  if (!existingData) {
    // 如果没有，生成新的分析数据
    existingData = generateModelAnalysisData(recordId)
    allAnalysisData.push(existingData)
    saveModelAnalysisData(allAnalysisData)
  }

  return existingData
}

// 深度学习模型数据分析相关函数
// 生成随机的深度学习模型数据分析数据
const generateDLModelDataAnalysis = (modelId: string): DLModelDataAnalysis => {
  const epochs = [1, 10, 20, 30, 40, 50]

  // 生成性能指标数据（准确率和召回率随epoch递增，但有较大随机波动）
  const accuracy: number[] = []
  const recall: number[] = []

  let baseAccuracy = 0.45 + Math.random() * 0.15 // 起始准确率 0.45-0.6
  let baseRecall = 0.4 + Math.random() * 0.15 // 起始召回率 0.4-0.55

  epochs.forEach((epoch, index) => {
    // 总体趋势向上，但每个点都有较大的随机波动
    const trendFactor = index * 0.04 // 总体上升趋势
    const randomFactor = (Math.random() - 0.5) * 0.08 // 较大的随机波动 ±0.04
    const noiseFactor = (Math.random() - 0.5) * 0.03 // 额外的噪声

    let currentAccuracy = baseAccuracy + trendFactor + randomFactor + noiseFactor
    let currentRecall = baseRecall + trendFactor + randomFactor * 0.8 + noiseFactor * 0.7

    // 确保数值在合理范围内，但允许一些波动
    currentAccuracy = Math.max(0.3, Math.min(0.85, currentAccuracy))
    currentRecall = Math.max(0.25, Math.min(0.82, currentRecall))

    // 有时候性能会暂时下降（过拟合等情况）
    if (Math.random() < 0.2 && index > 1) {
      currentAccuracy *= (0.95 + Math.random() * 0.08) // 偶尔下降5-13%
      currentRecall *= (0.93 + Math.random() * 0.1) // 偶尔下降7-17%
    }

    accuracy.push(Math.round(currentAccuracy * 1000) / 1000)
    recall.push(Math.round(currentRecall * 1000) / 1000)
  })

  // 生成损失曲线数据（训练损失和验证损失随epoch递减，但有波动）
  const trainLoss: number[] = []
  const validationLoss: number[] = []

  let baseTrainLoss = 1.1 + Math.random() * 0.4 // 起始训练损失 1.1-1.5
  let baseValidationLoss = baseTrainLoss + 0.05 + Math.random() * 0.15 // 验证损失略高于训练损失

  epochs.forEach((epoch, index) => {
    // 总体趋势向下，但有较大随机波动
    const trendFactor = index * 0.12 // 总体下降趋势
    const randomFactor = (Math.random() - 0.5) * 0.1 // 较大的随机波动
    const noiseFactor = (Math.random() - 0.5) * 0.05 // 额外的噪声

    let currentTrainLoss = baseTrainLoss - trendFactor + randomFactor + noiseFactor
    let currentValidationLoss = baseValidationLoss - trendFactor + randomFactor * 1.2 + noiseFactor * 1.1

    // 确保损失值在合理范围内
    currentTrainLoss = Math.max(0.08, Math.min(1.8, currentTrainLoss))
    currentValidationLoss = Math.max(0.1, Math.min(2.0, currentValidationLoss))

    // 验证损失有时会突然上升（过拟合）
    if (Math.random() < 0.15 && index > 2) {
      currentValidationLoss *= (1.1 + Math.random() * 0.2) // 偶尔上升10-30%
    }

    // 训练损失偶尔也会有小幅波动
    if (Math.random() < 0.1) {
      currentTrainLoss *= (1.02 + Math.random() * 0.06) // 偶尔小幅上升2-8%
    }

    trainLoss.push(Math.round(currentTrainLoss * 1000) / 1000)
    validationLoss.push(Math.round(currentValidationLoss * 1000) / 1000)
  })

  return {
    id: `dl_analysis_${Date.now()}`,
    modelId: modelId,
    performanceMetrics: {
      epochs: epochs,
      accuracy: accuracy,
      recall: recall
    },
    lossCurves: {
      epochs: epochs,
      trainLoss: trainLoss,
      validationLoss: validationLoss
    },
    createTime: new Date().toISOString(),
    createUser: '当前用户'
  }
}

// 获取深度学习模型数据分析数据
const getDLModelDataAnalysis = (): DLModelDataAnalysis[] => {
  try {
    const cached = localStorage.getItem(DL_MODEL_DATA_ANALYSIS_STORAGE_KEY)
    if (cached) {
      return JSON.parse(cached)
    }
  } catch (error) {
    console.error('获取深度学习模型数据分析数据失败:', error)
  }
  return []
}

// 保存深度学习模型数据分析数据
const saveDLModelDataAnalysis = (analysisData: DLModelDataAnalysis[]) => {
  localStorage.setItem(DL_MODEL_DATA_ANALYSIS_STORAGE_KEY, JSON.stringify(analysisData))
}

// 根据模型ID获取或生成深度学习模型数据分析数据
const getOrGenerateDLModelDataAnalysis = (modelId: string): DLModelDataAnalysis => {
  const allAnalysisData = getDLModelDataAnalysis()

  // 查找是否已有该模型的分析数据
  let existingData = allAnalysisData.find(data => data.modelId === modelId)

  if (!existingData) {
    // 如果没有，生成新的分析数据
    existingData = generateDLModelDataAnalysis(modelId)
    allAnalysisData.push(existingData)
    saveDLModelDataAnalysis(allAnalysisData)
  }

  return existingData
}

// 深度学习模型数据分析图表相关函数
// 悬浮提示相关状态
const tooltipVisible = ref(false)
const tooltipX = ref(0)
const tooltipY = ref(0)
const tooltipEpoch = ref(0)
const tooltipLabel = ref('')
const tooltipValue = ref('')

// 获取X轴位置
const getXPosition = (index: number) => {
  const chartWidth = 300 // SVG图表宽度
  const padding = 20
  const stepWidth = (chartWidth - padding * 2) / 5 // 6个点，5个间隔
  return padding + index * stepWidth
}

// 获取性能指标Y轴位置（0-1范围映射到200-0像素）
const getYPosition = (value: number) => {
  const chartHeight = 200
  const padding = 10
  return chartHeight - padding - (value * (chartHeight - padding * 2))
}

// 获取损失Y轴位置（0-1.5范围映射到200-0像素）
const getLossYPosition = (value: number) => {
  const chartHeight = 200
  const padding = 10
  const maxLoss = 1.5
  return chartHeight - padding - ((value / maxLoss) * (chartHeight - padding * 2))
}

// 获取性能指标折线点坐标
const getPerformanceLinePoints = (values: number[], type: string) => {
  return values.map((value, index) => {
    const x = getXPosition(index)
    const y = getYPosition(value)
    return `${x},${y}`
  }).join(' ')
}

// 获取损失曲线折线点坐标
const getLossLinePoints = (values: number[], type: string) => {
  return values.map((value, index) => {
    const x = getXPosition(index)
    const y = getLossYPosition(value)
    return `${x},${y}`
  }).join(' ')
}

// 显示悬浮提示
const showTooltip = (event: MouseEvent, type: string, epoch: number, value: number) => {
  tooltipVisible.value = true
  tooltipX.value = event.clientX + 10
  tooltipY.value = event.clientY - 10
  tooltipEpoch.value = epoch

  // 根据类型设置标签和值
  switch (type) {
    case 'accuracy':
      tooltipLabel.value = '准确率'
      tooltipValue.value = (value * 100).toFixed(1) + '%'
      break
    case 'recall':
      tooltipLabel.value = '召回率'
      tooltipValue.value = (value * 100).toFixed(1) + '%'
      break
    case 'train':
      tooltipLabel.value = '训练损失'
      tooltipValue.value = value.toFixed(3)
      break
    case 'validation':
      tooltipLabel.value = '验证损失'
      tooltipValue.value = value.toFixed(3)
      break
  }
}

// 隐藏悬浮提示
const hideTooltip = () => {
  tooltipVisible.value = false
}

// 获取状态配置
const getStatusConfig = (status: string) => {
  const statusConfigMap = {
    '已完成': {
      text: '已完成',
      color: '#67C23A',
      icon: 'el-icon-success',
      type: 'success' as const
    },
    '接入失败': {
      text: '接入失败',
      color: '#F56C6C',
      icon: 'el-icon-error',
      type: 'danger' as const
    },
    '运行中': {
      text: '运行中',
      color: '#409EFF',
      icon: 'el-icon-loading',
      type: 'primary' as const
    },
    '已暂停': {
      text: '已暂停',
      color: '#E6A23C',
      icon: 'el-icon-warning',
      type: 'warning' as const
    }
  }
  return statusConfigMap[status as keyof typeof statusConfigMap] || {
    text: status,
    color: '#909399',
    icon: 'el-icon-info',
    type: 'info' as const
  }
}
// 数据订阅相关函数
// 获取订阅数据
const getSubscriptions = (): DataSubscriptionRule[] => {
  try {
    const cached = localStorage.getItem(SUBSCRIPTION_STORAGE_KEY)
    if (cached) {
      return JSON.parse(cached)
    }
  } catch (error) {
    console.error('获取订阅数据失败:', error)
  }
  return []
}

// 保存订阅数据
const saveSubscriptions = () => {
  localStorage.setItem(SUBSCRIPTION_STORAGE_KEY, JSON.stringify(allSubscriptions.value))
}

// 打开数据订阅弹窗
const openSubscriptionDialog = () => {
  // 刷新数据源选项
  loadDataSourceOptions()

  // 加载已有订阅数据
  allSubscriptions.value = getSubscriptions()

  // 重置表单
  resetSubscriptionForm()

  // 显示弹窗
  showSubscriptionDialog.value = true
}

// 重置订阅表单
const resetSubscriptionForm = () => {
  subscriptionForm.value = {
    dataSourceId: '',
    triggerCondition: {
      accessStatus: [],
      accessCount: {
        operator: '请选择',
        value: ''
      },
      timeRange: {
        startTime: '',
        endTime: ''
      }
    },
    subscriptionFrequency: 'realtime',
    notificationMethods: {
      siteMessage: false,
      email: false,
      dingTalk: false
    }
  }

  // 重置触发条件控制变量
  enableAccessCount.value = false
  enableTimeRange.value = false
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-')
}

// 获取频率标签
const getFrequencyLabel = (frequency: string) => {
  const frequencyMap: Record<string, string> = {
    'realtime': '实时',
    'weekly': '每周',
    'monthly': '每月'
  }
  return frequencyMap[frequency] || frequency
}

// 获取通知方式标签
const getNotificationMethodsLabel = (methods: NotificationMethods) => {
  const labels = []
  if (methods.siteMessage) labels.push('站内信')
  if (methods.email) labels.push('愉快证')
  if (methods.dingTalk) labels.push('愉快DING')
  return labels.join('、')
}

// 删除订阅记录
const deleteSubscription = (index: number) => {
  const subscription = allSubscriptions.value[index]
  if (!subscription) {
    ElMessage.error('未找到要删除的订阅记录')
    return
  }

  ElMessageBox.confirm(
    `确认删除数据源"${subscription.dataSourceName}"的订阅规则吗？`,
    '删除确认',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    allSubscriptions.value.splice(index, 1)
    saveSubscriptions()
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 添加订阅记录
const addSubscription = () => {
  // 表单验证
  if (!subscriptionFormRef.value) {
    ElMessage.error('表单引用未找到')
    return
  }

  subscriptionFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      ElMessage.warning('请完善表单信息')
      return
    }

    // 开始Loading
    subscriptionLoading.value = true

    // 检查是否至少选择了一个通知方式
    const { siteMessage, email, dingTalk } = subscriptionForm.value.notificationMethods
    if (!siteMessage && !email && !dingTalk) {
      ElMessage.warning('请至少选择一个通知方式')
      return
    }

    // 获取数据源名称
    const selectedDataSource = dataSourceOptions.value.find(
      option => option.value === subscriptionForm.value.dataSourceId
    )
    if (!selectedDataSource) {
      ElMessage.error('未找到选中的数据源')
      return
    }

    // 创建新的订阅规则
    const newSubscription: DataSubscriptionRule = {
      id: `sub_${Date.now()}`,
      dataSourceId: subscriptionForm.value.dataSourceId,
      dataSourceName: selectedDataSource.label,
      triggerCondition: {
        accessStatus: [...subscriptionForm.value.triggerCondition.accessStatus],
        accessCount: enableAccessCount.value ? {
          operator: subscriptionForm.value.triggerCondition.accessCount.operator,
          value: subscriptionForm.value.triggerCondition.accessCount.value
        } : undefined,
        timeRange: enableTimeRange.value ? {
          startTime: subscriptionForm.value.triggerCondition.timeRange.startTime,
          endTime: subscriptionForm.value.triggerCondition.timeRange.endTime
        } : undefined
      },
      subscriptionFrequency: subscriptionForm.value.subscriptionFrequency as 'realtime' | 'weekly' | 'monthly',
      notificationMethods: {
        siteMessage: subscriptionForm.value.notificationMethods.siteMessage,
        email: subscriptionForm.value.notificationMethods.email,
        dingTalk: subscriptionForm.value.notificationMethods.dingTalk
      },
      createTime: new Date().toISOString(),
      createUser: '当前用户'
    }

    // 模拟异步操作
    setTimeout(() => {
      // 添加到订阅列表
      allSubscriptions.value.push(newSubscription)

      // 保存到本地存储
      saveSubscriptions()

      // 重置表单
      resetSubscriptionForm()

      // 清空表单验证状态
      subscriptionFormRef.value.clearValidate()

      // 结束Loading
      subscriptionLoading.value = false

      ElMessage.success('添加订阅成功')
    }, 300)
  })
}

// 取消订阅弹窗
const cancelSubscriptionDialog = () => {
  // 重置表单
  resetSubscriptionForm()

  // 清空表单验证状态
  if (subscriptionFormRef.value) {
    subscriptionFormRef.value.clearValidate()
  }

  // 关闭弹窗
  showSubscriptionDialog.value = false
}

// 确认订阅弹窗
const confirmSubscriptionDialog = () => {
  subscriptionLoading.value = true

  // 模拟保存过程
  setTimeout(() => {
    // 数据已经在添加订阅时保存到localStorage了
    // 这里只需要确认保存成功
    subscriptionLoading.value = false
    showSubscriptionDialog.value = false

    const subscriptionCount = allSubscriptions.value.length
    ElMessage.success(`数据订阅配置已保存，共${subscriptionCount}条订阅规则`)
  }, 500)
}

// 模型分析弹窗相关
const showModelAnalysisDialog = ref(false)
const currentAnalysisData = ref<ModelAnalysisData | null>(null)

// 深度学习模型数据分析弹窗相关
const showDLModelDataAnalysisDialog = ref(false)
const currentDLModelAnalysisData = ref<DLModelDataAnalysis | null>(null)

// 模型管理弹窗相关
const showModelManagementDialog = ref(false)
const modelManagementLoading = ref(false)
const activeModelTab = ref('dataAccess') // 当前激活的标签页
const tableLoading = ref(false)

// 模型管理数据结构
interface AlgorithmRule {
  id: string
  name: string
  algorithmType: string
  description: string
  status: boolean
  createTime: string
}

interface MLModel {
  id: string
  name: string
  description: string
  dataSource: string
  modelSelection: string
  trainingData: string
  scheduledTrainingTime: string
  deploymentEnvironment: string
  serviceAddress: string
  deploymentVersion: string
  status: string
  creator: string
}

interface DLModel {
  id: string
  name: string
  description: string
  dataSource: string
  modelAlgorithm: string
  trainingData: string
  scheduledTrainingTime: string
  trainingIterations: string
  deploymentEnvironment: string
  serviceAddress: string
  deploymentVersion: string
  status: string
  algorithm: string
}

interface NLPConfig {
  id: string
  name: string
  algorithmType: string
  ruleConfiguration: string
  languageModel: string
  creator: string
}

// 各模块的数据存储
const algorithmRules = ref<AlgorithmRule[]>([])
const mlModels = ref<MLModel[]>([])
const dlModels = ref<DLModel[]>([])
const nlpConfigs = ref<NLPConfig[]>([])

// 模型管理存储键
const MODEL_STORAGE_KEYS = {
  ALGORITHM_RULES: 'modelManagement_algorithmRules',
  ML_MODELS: 'modelManagement_mlModels',
  DL_MODELS: 'modelManagement_dlModels',
  NLP_CONFIGS: 'modelManagement_nlpConfigs'
}

// 打开模型管理弹窗
const openModelManagementDialog = () => {
  loadModelManagementData()
  showModelManagementDialog.value = true
}

// 切换标签页时的Loading效果
const switchModelTab = (tabName: string) => {
  tableLoading.value = true
  activeModelTab.value = tabName

  // 模拟加载过程
  setTimeout(() => {
    tableLoading.value = false
  }, 300)
}

// 加载模型管理数据
const loadModelManagementData = () => {
  // 从localStorage加载数据
  const savedAlgorithmRules = localStorage.getItem(MODEL_STORAGE_KEYS.ALGORITHM_RULES)
  const savedMLModels = localStorage.getItem(MODEL_STORAGE_KEYS.ML_MODELS)
  const savedDLModels = localStorage.getItem(MODEL_STORAGE_KEYS.DL_MODELS)
  const savedNLPConfigs = localStorage.getItem(MODEL_STORAGE_KEYS.NLP_CONFIGS)

  if (savedAlgorithmRules) {
    algorithmRules.value = JSON.parse(savedAlgorithmRules)
  } else {
    // 初始化默认数据
    algorithmRules.value = [
      { id: '1', name: '规则名称1', algorithmType: '去重', description: '用于数据去重处理', status: true, createTime: '2025.7.21' },
      { id: '2', name: '规则名称2', algorithmType: '去重', description: '用于数据去重处理', status: false, createTime: '2025.7.21' },
      { id: '3', name: '规则名称3', algorithmType: '去重', description: '用于数据去重处理', status: true, createTime: '2025.7.21' }
    ]
  }

  if (savedMLModels) {
    mlModels.value = JSON.parse(savedMLModels)
  } else {
    // 初始化默认数据
    mlModels.value = [
      {
        id: '1',
        name: '线性回归模型',
        description: '用于预测数值型数据...',
        dataSource: '数据源1',
        modelSelection: '线性回归',
        trainingData: '',
        scheduledTrainingTime: '每日',
        deploymentEnvironment: '生产环境',
        serviceAddress: 'http://localhost:8080',
        deploymentVersion: 'v1.0',
        status: '待训练',
        creator: '张三'
      },
      {
        id: '2',
        name: '线性回归模型',
        description: '用于预测数值型数据...',
        dataSource: '数据源2',
        modelSelection: '线性回归',
        trainingData: '',
        scheduledTrainingTime: '每日',
        deploymentEnvironment: '生产环境',
        serviceAddress: 'http://localhost:8080',
        deploymentVersion: 'v1.0',
        status: '已训练',
        creator: '张三'
      },
      {
        id: '3',
        name: '线性回归模型',
        description: '用于预测数值型数据...',
        dataSource: '数据源3',
        modelSelection: '线性回归',
        trainingData: '',
        scheduledTrainingTime: '每日',
        deploymentEnvironment: '生产环境',
        serviceAddress: 'http://localhost:8080',
        deploymentVersion: 'v1.0',
        status: '已部署',
        creator: '张三'
      }
    ]
  }

  if (savedDLModels) {
    dlModels.value = JSON.parse(savedDLModels)
  } else {
    // 初始化默认数据
    dlModels.value = [
      {
        id: '1',
        name: '模型名称',
        description: '用于预测数值型数据...',
        dataSource: '数据源1',
        modelAlgorithm: 'CNN',
        trainingData: '',
        scheduledTrainingTime: '每日',
        trainingIterations: '100',
        deploymentEnvironment: '生产环境',
        serviceAddress: 'http://localhost:8080',
        deploymentVersion: 'v1.0',
        status: '待训练',
        algorithm: 'ResNet-50'
      },
      {
        id: '2',
        name: '模型名称',
        description: '用于预测数值型数据...',
        dataSource: '数据源2',
        modelAlgorithm: 'CNN',
        trainingData: '',
        scheduledTrainingTime: '每日',
        trainingIterations: '100',
        deploymentEnvironment: '生产环境',
        serviceAddress: 'http://localhost:8080',
        deploymentVersion: 'v1.0',
        status: '已训练',
        algorithm: 'ResNet-50'
      },
      {
        id: '3',
        name: '模型名称',
        description: '用于预测数值型数据...',
        dataSource: '数据源3',
        modelAlgorithm: 'CNN',
        trainingData: '',
        scheduledTrainingTime: '每日',
        trainingIterations: '100',
        deploymentEnvironment: '生产环境',
        serviceAddress: 'http://localhost:8080',
        deploymentVersion: 'v1.0',
        status: '已部署',
        algorithm: 'ResNet-50'
      }
    ]
  }

  if (savedNLPConfigs) {
    nlpConfigs.value = JSON.parse(savedNLPConfigs)
  } else {
    // 初始化默认数据
    nlpConfigs.value = [
      { id: '1', name: '规则名称1', algorithmType: '分词', ruleConfiguration: '默认分词规则配置', languageModel: 'BERT', creator: '张三' },
      { id: '2', name: '规则名称2', algorithmType: '分词', ruleConfiguration: '默认分词规则配置', languageModel: 'BERT', creator: '李四' },
      { id: '3', name: '规则名称3', algorithmType: '分词', ruleConfiguration: '默认分词规则配置', languageModel: 'BERT', creator: '王五' }
    ]
  }
}

// 保存模型管理数据
const saveModelManagementData = () => {
  localStorage.setItem(MODEL_STORAGE_KEYS.ALGORITHM_RULES, JSON.stringify(algorithmRules.value))
  localStorage.setItem(MODEL_STORAGE_KEYS.ML_MODELS, JSON.stringify(mlModels.value))
  localStorage.setItem(MODEL_STORAGE_KEYS.DL_MODELS, JSON.stringify(dlModels.value))
  localStorage.setItem(MODEL_STORAGE_KEYS.NLP_CONFIGS, JSON.stringify(nlpConfigs.value))
}

// 确认模型管理弹窗
const confirmModelManagement = () => {
  modelManagementLoading.value = true

  // 模拟保存过程
  setTimeout(() => {
    saveModelManagementData()
    modelManagementLoading.value = false
    showModelManagementDialog.value = false

    ElMessage.success('模型管理配置已保存')
  }, 500)
}

// 新增算法规则相关
const showAddAlgorithmDialog = ref(false)
const algorithmFormRef = ref()
const algorithmForm = ref({
  name: '',
  algorithmType: '',
  description: '',
  status: true
})

const algorithmFormRules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  algorithmType: [{ required: true, message: '请选择算法类型', trigger: 'change' }]
}

const algorithmTypeOptions = [
  { label: '去重', value: '去重' },
  { label: '清洗', value: '清洗' },
  { label: '转换', value: '转换' },
  { label: '验证', value: '验证' }
]

// 打开新增算法规则弹窗
const openAddAlgorithmDialog = () => {
  algorithmForm.value = {
    name: '',
    algorithmType: '',
    description: '',
    status: true
  }
  showAddAlgorithmDialog.value = true
}

// 确认新增/编辑算法规则
const confirmAddAlgorithm = () => {
  algorithmFormRef.value.validate((valid: boolean) => {
    if (valid) {
      if (currentEditingItem.value && currentEditingType.value === 'algorithm') {
        // 编辑模式
        const index = algorithmRules.value.findIndex(item => item.id === currentEditingItem.value.id)
        if (index > -1) {
          algorithmRules.value[index] = {
            ...algorithmRules.value[index],
            name: algorithmForm.value.name,
            algorithmType: algorithmForm.value.algorithmType,
            description: algorithmForm.value.description,
            status: algorithmForm.value.status
          }
          ElMessage.success('算法规则编辑成功')
        }
      } else {
        // 新增模式
        const newRule: AlgorithmRule = {
          id: Date.now().toString(),
          name: algorithmForm.value.name,
          algorithmType: algorithmForm.value.algorithmType,
          description: algorithmForm.value.description,
          status: algorithmForm.value.status,
          createTime: new Date().toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'numeric',
            day: 'numeric'
          }).replace(/\//g, '.')
        }
        algorithmRules.value.push(newRule)
        ElMessage.success('算法规则添加成功')
      }

      saveModelManagementData()
      showAddAlgorithmDialog.value = false
      currentEditingItem.value = null
      currentEditingType.value = ''
    }
  })
}

// 新增机器学习模型相关
const showAddMLModelDialog = ref(false)
const mlModelFormRef = ref()
const mlModelForm = ref({
  name: '',
  description: '',
  dataSource: '',
  modelSelection: '',
  trainingData: '',
  scheduledTrainingTime: '',
  deploymentEnvironment: '',
  serviceAddress: '',
  deploymentVersion: '',
  status: '待训练',
  creator: ''
})

// 机器学习模型文件上传处理
const handleMLModelFileUpload = (file: any) => {
  // 模拟文件上传处理
  mlModelForm.value.trainingData = file.name
  ElMessage.success(`文件 ${file.name} 上传成功`)
  return false // 阻止自动上传
}

const mlModelFormRules = {
  name: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入模型描述', trigger: 'blur' }],
  dataSource: [{ required: true, message: '请选择数据源', trigger: 'change' }],
  creator: [{ required: true, message: '请输入创建人', trigger: 'blur' }]
}

const mlModelStatusOptions = [
  { label: '待训练', value: '待训练' },
  { label: '已训练', value: '已训练' },
  { label: '已部署', value: '已部署' }
]

// 打开新增机器学习模型弹窗
const openAddMLModelDialog = () => {
  // 刷新数据源选项
  loadDataSourceOptions()

  mlModelForm.value = {
    name: '',
    description: '',
    dataSource: '',
    modelSelection: '',
    trainingData: '',
    scheduledTrainingTime: '',
    deploymentEnvironment: '',
    serviceAddress: '',
    deploymentVersion: '',
    status: '待训练',
    creator: ''
  }
  showAddMLModelDialog.value = true
}

// 确认新增/编辑机器学习模型
const confirmAddMLModel = () => {
  mlModelFormRef.value.validate((valid: boolean) => {
    if (valid) {
      if (currentEditingItem.value && currentEditingType.value === 'mlModel') {
        // 编辑模式
        const index = mlModels.value.findIndex(item => item.id === currentEditingItem.value.id)
        if (index > -1) {
          mlModels.value[index] = {
            ...mlModels.value[index],
            name: mlModelForm.value.name,
            description: mlModelForm.value.description,
            dataSource: mlModelForm.value.dataSource,
            status: mlModelForm.value.status,
            creator: mlModelForm.value.creator
          }
          ElMessage.success('机器学习模型编辑成功')
        }
      } else {
        // 新增模式
        const newModel: MLModel = {
          id: Date.now().toString(),
          name: mlModelForm.value.name,
          description: mlModelForm.value.description,
          dataSource: mlModelForm.value.dataSource,
          modelSelection: mlModelForm.value.modelSelection,
          trainingData: mlModelForm.value.trainingData,
          scheduledTrainingTime: mlModelForm.value.scheduledTrainingTime,
          deploymentEnvironment: mlModelForm.value.deploymentEnvironment,
          serviceAddress: mlModelForm.value.serviceAddress,
          deploymentVersion: mlModelForm.value.deploymentVersion,
          status: mlModelForm.value.status,
          creator: mlModelForm.value.creator
        }
        mlModels.value.push(newModel)
        ElMessage.success('机器学习模型添加成功')
      }

      saveModelManagementData()
      showAddMLModelDialog.value = false
      currentEditingItem.value = null
      currentEditingType.value = ''
    }
  })
}

// 新增深度学习模型相关
const showAddDLModelDialog = ref(false)
const dlModelFormRef = ref()
const dlModelForm = ref({
  name: '',
  description: '',
  dataSource: '',
  modelAlgorithm: '',
  trainingData: '',
  scheduledTrainingTime: '',
  trainingIterations: '',
  deploymentEnvironment: '',
  serviceAddress: '',
  deploymentVersion: '',
  status: '待训练',
  algorithm: ''
})

// 深度学习模型文件上传处理
const handleDLModelFileUpload = (file: any) => {
  // 模拟文件上传处理
  dlModelForm.value.trainingData = file.name
  ElMessage.success(`文件 ${file.name} 上传成功`)
  return false // 阻止自动上传
}

const dlModelFormRules = {
  name: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入模型描述', trigger: 'blur' }],
  dataSource: [{ required: true, message: '请选择数据源', trigger: 'change' }],
  algorithm: [{ required: true, message: '请选择模型算法', trigger: 'change' }]
}

const dlModelAlgorithmOptions = [
  { label: 'ResNet-50', value: 'ResNet-50' },
  { label: 'VGG-16', value: 'VGG-16' },
  { label: 'LSTM', value: 'LSTM' },
  { label: 'CNN', value: 'CNN' }
]

// 打开新增深度学习模型弹窗
const openAddDLModelDialog = () => {
  // 刷新数据源选项
  loadDataSourceOptions()

  dlModelForm.value = {
    name: '',
    description: '',
    dataSource: '',
    modelAlgorithm: '',
    trainingData: '',
    scheduledTrainingTime: '',
    trainingIterations: '',
    deploymentEnvironment: '',
    serviceAddress: '',
    deploymentVersion: '',
    status: '待训练',
    algorithm: ''
  }
  showAddDLModelDialog.value = true
}

// 确认新增/编辑深度学习模型
const confirmAddDLModel = () => {
  dlModelFormRef.value.validate((valid: boolean) => {
    if (valid) {
      if (currentEditingItem.value && currentEditingType.value === 'dlModel') {
        // 编辑模式
        const index = dlModels.value.findIndex(item => item.id === currentEditingItem.value.id)
        if (index > -1) {
          dlModels.value[index] = {
            ...dlModels.value[index],
            name: dlModelForm.value.name,
            description: dlModelForm.value.description,
            dataSource: dlModelForm.value.dataSource,
            status: dlModelForm.value.status,
            algorithm: dlModelForm.value.algorithm
          }
          ElMessage.success('深度学习模型编辑成功')
        }
      } else {
        // 新增模式
        const newModel: DLModel = {
          id: Date.now().toString(),
          name: dlModelForm.value.name,
          description: dlModelForm.value.description,
          dataSource: dlModelForm.value.dataSource,
          modelAlgorithm: dlModelForm.value.modelAlgorithm,
          trainingData: dlModelForm.value.trainingData,
          scheduledTrainingTime: dlModelForm.value.scheduledTrainingTime,
          trainingIterations: dlModelForm.value.trainingIterations,
          deploymentEnvironment: dlModelForm.value.deploymentEnvironment,
          serviceAddress: dlModelForm.value.serviceAddress,
          deploymentVersion: dlModelForm.value.deploymentVersion,
          status: dlModelForm.value.status,
          algorithm: dlModelForm.value.algorithm
        }
        dlModels.value.push(newModel)
        ElMessage.success('深度学习模型添加成功')
      }

      saveModelManagementData()
      showAddDLModelDialog.value = false
      currentEditingItem.value = null
      currentEditingType.value = ''
    }
  })
}

// 新增自然语言处理配置相关
const showAddNLPConfigDialog = ref(false)
const nlpConfigFormRef = ref()
const nlpConfigForm = ref({
  name: '',
  algorithmType: '',
  ruleConfiguration: '',
  languageModel: '',
  creator: ''
})

const nlpConfigFormRules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  algorithmType: [{ required: true, message: '请选择算法类型', trigger: 'change' }],
  languageModel: [{ required: true, message: '请选择语言模型', trigger: 'change' }],
  creator: [{ required: true, message: '请输入创建人', trigger: 'blur' }]
}

const nlpAlgorithmTypeOptions = [
  { label: '分词', value: '分词' },
  { label: '词性标注', value: '词性标注' },
  { label: '命名实体识别', value: '命名实体识别' },
  { label: '情感分析', value: '情感分析' }
]

const nlpLanguageModelOptions = [
  { label: 'BERT', value: 'BERT' },
  { label: 'GPT', value: 'GPT' },
  { label: 'RoBERTa', value: 'RoBERTa' },
  { label: 'ELECTRA', value: 'ELECTRA' }
]

// 打开新增自然语言处理配置弹窗
const openAddNLPConfigDialog = () => {
  nlpConfigForm.value = {
    name: '',
    algorithmType: '',
    ruleConfiguration: '',
    languageModel: '',
    creator: ''
  }
  showAddNLPConfigDialog.value = true
}

// 确认新增/编辑自然语言处理配置
const confirmAddNLPConfig = () => {
  nlpConfigFormRef.value.validate((valid: boolean) => {
    if (valid) {
      if (currentEditingItem.value && currentEditingType.value === 'nlpConfig') {
        // 编辑模式
        const index = nlpConfigs.value.findIndex(item => item.id === currentEditingItem.value.id)
        if (index > -1) {
          nlpConfigs.value[index] = {
            ...nlpConfigs.value[index],
            name: nlpConfigForm.value.name,
            algorithmType: nlpConfigForm.value.algorithmType,
            ruleConfiguration: nlpConfigForm.value.ruleConfiguration,
            languageModel: nlpConfigForm.value.languageModel,
            creator: nlpConfigForm.value.creator
          }
          ElMessage.success('自然语言处理配置编辑成功')
        }
      } else {
        // 新增模式
        const newConfig: NLPConfig = {
          id: Date.now().toString(),
          name: nlpConfigForm.value.name,
          algorithmType: nlpConfigForm.value.algorithmType,
          ruleConfiguration: nlpConfigForm.value.ruleConfiguration,
          languageModel: nlpConfigForm.value.languageModel,
          creator: nlpConfigForm.value.creator
        }
        nlpConfigs.value.push(newConfig)
        ElMessage.success('自然语言处理配置添加成功')
      }

      saveModelManagementData()
      showAddNLPConfigDialog.value = false
      currentEditingItem.value = null
      currentEditingType.value = ''
    }
  })
}

// 编辑和删除功能
const currentEditingItem = ref<any>(null)
const currentEditingType = ref('')

// 编辑算法规则
const editAlgorithmRule = (row: AlgorithmRule) => {
  currentEditingItem.value = row
  currentEditingType.value = 'algorithm'
  algorithmForm.value = { ...row }
  showAddAlgorithmDialog.value = true
}

// 删除算法规则
const deleteAlgorithmRule = (row: AlgorithmRule) => {
  ElMessageBox.confirm('确认删除该算法规则吗？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = algorithmRules.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      algorithmRules.value.splice(index, 1)
      saveModelManagementData()
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 编辑机器学习模型
const editMLModel = (row: MLModel) => {
  currentEditingItem.value = row
  currentEditingType.value = 'mlModel'
  mlModelForm.value = { ...row }
  showAddMLModelDialog.value = true
}

// 删除机器学习模型
const deleteMLModel = (row: MLModel) => {
  ElMessageBox.confirm('确认删除该机器学习模型吗？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = mlModels.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      mlModels.value.splice(index, 1)
      saveModelManagementData()
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 打开机器学习模型分析
const openMLModelAnalysis = (row: any) => {
  // 为机器学习模型获取或生成分析数据（确保数据持久化）
  const analysisData = getOrGenerateAnalysisData(row.id)
  currentAnalysisData.value = analysisData
  showModelAnalysisDialog.value = true
}

// 打开深度学习模型数据分析
const openDLModelDataAnalysis = (row: any) => {
  // 为深度学习模型获取或生成数据分析数据（确保数据持久化）
  const analysisData = getOrGenerateDLModelDataAnalysis(row.id)
  currentDLModelAnalysisData.value = analysisData
  showDLModelDataAnalysisDialog.value = true
}

// 编辑深度学习模型
const editDLModel = (row: DLModel) => {
  currentEditingItem.value = row
  currentEditingType.value = 'dlModel'
  dlModelForm.value = { ...row }
  showAddDLModelDialog.value = true
}

// 删除深度学习模型
const deleteDLModel = (row: DLModel) => {
  ElMessageBox.confirm('确认删除该深度学习模型吗？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = dlModels.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      dlModels.value.splice(index, 1)
      saveModelManagementData()
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 编辑自然语言处理配置
const editNLPConfig = (row: NLPConfig) => {
  currentEditingItem.value = row
  currentEditingType.value = 'nlpConfig'
  nlpConfigForm.value = { ...row }
  showAddNLPConfigDialog.value = true
}

// 删除自然语言处理配置
const deleteNLPConfig = (row: NLPConfig) => {
  ElMessageBox.confirm('确认删除该自然语言处理配置吗？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = nlpConfigs.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      nlpConfigs.value.splice(index, 1)
      saveModelManagementData()
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}





// 重新初始化（不清除已有记录）
const reinitialize = () => {
  console.log('重新初始化数据...')
  // 重新加载分组、数据源选项、关联关系、日志和已有记录
  groupList.value = initializeGroups()
  loadDataSourceOptions()
  allRelations.value = getRelations()
  allLogs.value = getLogs()
  allRecords.value = initializeRecords()
  syncWithDataSources() // 只清理已删除数据源的记录
  loadTableData()
}

// 实时同步数据源变化
const handleDataSourceChange = () => {
  console.log('检测到数据源变化，开始同步...')
  loadDataSourceOptions()
  syncWithDataSources()
  loadTableData()
  ElMessage.info('数据源已同步更新')
}

// localStorage变化监听器
const handleStorageChange = (event: StorageEvent) => {
  if (event.key === 'dataSourceManagement_data') {
    console.log('localStorage中数据源管理数据发生变化')
    handleDataSourceChange()
  }
}

// 页面可见性变化监听器
const handleVisibilityChange = () => {
  if (!document.hidden) {
    console.log('页面变为可见，检查数据源变化')
    // 页面变为可见时，检查数据源是否有变化
    setTimeout(() => {
      handleDataSourceChange()
    }, 100)
  }
}

// 定时检查数据源变化
let checkInterval: NodeJS.Timeout | null = null
const startPeriodicCheck = () => {
  checkInterval = setInterval(() => {
    const currentDataSources = localStorage.getItem('dataSourceManagement_data')
    const lastKnownDataSources = localStorage.getItem('dataAccess_lastKnownDataSources')

    if (currentDataSources !== lastKnownDataSources) {
      console.log('定时检查发现数据源变化')
      localStorage.setItem('dataAccess_lastKnownDataSources', currentDataSources || '')
      handleDataSourceChange()
    }
  }, 5000) // 每5秒检查一次
}

const stopPeriodicCheck = () => {
  if (checkInterval) {
    clearInterval(checkInterval)
    checkInterval = null
  }
}

// 初始化
onMounted(() => {
  console.log('组件挂载，开始初始化...')
  groupList.value = initializeGroups()

  // 检查是否需要清除旧数据
  const actualDataSources = getActualDataSources()
  console.log('检查到的实际数据源数量:', actualDataSources.length)

  if (actualDataSources.length === 0) {
    console.log('没有找到数据源管理数据，可能需要先在数据源管理页面添加数据源')
  }

  // 重新初始化
  reinitialize()

  // 继续未完成的接入任务
  allRecords.value.forEach(record => {
    if (record.status === '运行中' && record.progress < 100) {
      simulateDataAccess(record.id)
    }
  })

  // 启用实时监听
  console.log('启用数据源变化监听...')
  window.addEventListener('storage', handleStorageChange)
  document.addEventListener('visibilitychange', handleVisibilityChange)
  startPeriodicCheck()

  // 初始化数据源状态记录
  const currentDataSources = localStorage.getItem('dataSourceManagement_data')
  localStorage.setItem('dataAccess_lastKnownDataSources', currentDataSources || '')

  // 初始化故障处理规则
  const savedFaultRules = getFaultHandlingRules()
  if (savedFaultRules && savedFaultRules.length > 0) {
    faultPreventionRules.value = savedFaultRules
    console.log('加载已保存的故障处理规则，数量:', savedFaultRules.length)
  } else {
    // 如果没有保存的规则，使用默认规则并保存
    saveFaultHandlingRules()
    console.log('使用默认故障处理规则，数量:', faultPreventionRules.value.length)
  }
})

// 故障处理弹窗相关
const showFaultHandlingDialog = ref(false)
const faultHandlingActiveTab = ref('prevention') // 'prevention' | 'config'
const faultHandlingLoading = ref(false)

// 新增预防规则弹窗相关
const showAddPreventionRuleDialog = ref(false)
const preventionRuleFormRef = ref()
const preventionRuleForm = ref({
  name: '',
  triggerCondition: '',
  actionSteps: '',
  status: true
})

// 新增预防规则表单验证规则
const preventionRuleRules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 1, max: 50, message: '规则名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  triggerCondition: [
    { required: true, message: '请输入触发条件', trigger: 'blur' },
    { min: 1, max: 200, message: '触发条件长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  actionSteps: [
    { required: true, message: '请输入处理/修复/预防步骤', trigger: 'blur' },
    { min: 1, max: 500, message: '处理步骤长度在 1 到 500 个字符', trigger: 'blur' }
  ]
}

// 故障处理预防规则数据
const faultPreventionRules = ref([
  {
    id: 'rule_1',
    name: '网络连接检查',
    triggerCondition: '数据源状态为"断开"',
    status: '启用',
    createTime: new Date().toISOString(),
    createUser: '系统'
  },
  {
    id: 'rule_2',
    name: '网络连接检查',
    triggerCondition: '数据源状态为"断开"',
    status: '禁用',
    createTime: new Date().toISOString(),
    createUser: '系统'
  },
  {
    id: 'rule_3',
    name: '网络连接检查',
    triggerCondition: '数据源状态为"断开"',
    status: '',
    createTime: new Date().toISOString(),
    createUser: '系统'
  }
])

// 故障规则配置表单
const faultRuleConfigForm = ref({
  triggerCondition: {
    continuousFailure: false, // 连续3次连接失败
    errorCodes: false, // 错误码包含
    errorCodeList: ['ECONNREFUSED', 'ETIMEDOUT']
  },
  executeAction: 'autoRetry', // 'autoRetry' | 'notify'
  autoRetryConfig: {
    interval: 30, // 回调间隔(秒)
    maxTimes: 3 // 最大次数
  },
  notifyConfig: {
    responsible: '请选择' // 负责人
  }
})

// 故障处理本地存储键
const FAULT_HANDLING_STORAGE_KEY = 'dataAccess_faultHandling'

// 获取故障处理规则
const getFaultHandlingRules = () => {
  try {
    const cached = localStorage.getItem(FAULT_HANDLING_STORAGE_KEY)
    if (cached) {
      return JSON.parse(cached)
    }
  } catch (error) {
    console.error('获取故障处理规则失败:', error)
  }
  return faultPreventionRules.value
}

// 保存故障处理规则
const saveFaultHandlingRules = () => {
  localStorage.setItem(FAULT_HANDLING_STORAGE_KEY, JSON.stringify(faultPreventionRules.value))
}

// 打开故障处理弹窗
const openFaultHandlingDialog = () => {
  // 加载已保存的规则数据
  const savedRules = getFaultHandlingRules()
  if (savedRules && savedRules.length > 0) {
    faultPreventionRules.value = savedRules
  }
  faultHandlingActiveTab.value = 'prevention'
  showFaultHandlingDialog.value = true
  console.log('打开故障处理弹窗，当前规则数量:', faultPreventionRules.value.length)
}

// 切换故障处理导航
const switchFaultHandlingTab = (tab: string) => {
  faultHandlingActiveTab.value = tab
}

// 打开新增预防规则弹窗
const openAddPreventionRuleDialog = () => {
  // 重置表单
  preventionRuleForm.value = {
    name: '',
    triggerCondition: '',
    actionSteps: '',
    status: true
  }

  // 清除表单验证
  if (preventionRuleFormRef.value) {
    preventionRuleFormRef.value.clearValidate()
  }

  showAddPreventionRuleDialog.value = true
}

// 取消新增预防规则
const cancelAddPreventionRule = () => {
  showAddPreventionRuleDialog.value = false
}

// 确认新增预防规则
const confirmAddPreventionRule = () => {
  if (!preventionRuleFormRef.value) return

  preventionRuleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      // 生成新的规则ID
      const newRuleId = `rule_${Date.now()}`

      // 创建新规则对象
      const newRule = {
        id: newRuleId,
        name: preventionRuleForm.value.name,
        triggerCondition: preventionRuleForm.value.triggerCondition,
        actionSteps: preventionRuleForm.value.actionSteps,
        status: preventionRuleForm.value.status ? '启用' : '禁用',
        createTime: new Date().toISOString(),
        createUser: '当前用户'
      }

      // 添加到规则列表
      faultPreventionRules.value.push(newRule)

      // 保存到localStorage
      saveFaultHandlingRules()

      // 显示成功消息
      ElMessage.success('预防规则添加成功')

      // 关闭弹窗
      showAddPreventionRuleDialog.value = false

      console.log('新增预防规则:', newRule)
    } else {
      ElMessage.error('请检查表单填写是否正确')
    }
  })
}

// 切换规则状态
const toggleRuleStatus = (ruleId: string, newValue?: string | number | boolean) => {
  const rule = faultPreventionRules.value.find(r => r.id === ruleId)
  if (rule) {
    if (typeof newValue === 'boolean') {
      rule.status = newValue ? '启用' : '禁用'
    } else {
      rule.status = rule.status === '启用' ? '禁用' : '启用'
    }
    saveFaultHandlingRules()
    ElMessage.success(`规则已${rule.status}`)
  }
}

// 编辑规则
const editRule = (ruleId: string) => {
  // 查找要编辑的规则
  const rule = faultPreventionRules.value.find(r => r.id === ruleId)
  if (rule) {
    // 加载规则配置到表单中
    const savedConfig = localStorage.getItem(`faultRuleConfig_${ruleId}`)
    if (savedConfig) {
      try {
        const config = JSON.parse(savedConfig)
        faultRuleConfigForm.value = { ...config }
      } catch (error) {
        console.error('加载规则配置失败:', error)
      }
    }

    // 切换到配置页面
    faultHandlingActiveTab.value = 'config'
    ElMessage.info(`编辑规则: ${rule.name}`)
  }
}

// 删除规则
const deleteRule = (ruleId: string) => {
  ElMessageBox.confirm('确认删除该规则吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = faultPreventionRules.value.findIndex(r => r.id === ruleId)
    if (index > -1) {
      faultPreventionRules.value.splice(index, 1)
      saveFaultHandlingRules()
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 提交故障规则配置
const submitFaultRuleConfig = () => {
  // 验证表单
  if (faultRuleConfigForm.value.executeAction === 'notify' &&
      faultRuleConfigForm.value.notifyConfig.responsible === '请选择') {
    ElMessage.warning('请选择负责人')
    return
  }

  // 验证触发条件
  if (!faultRuleConfigForm.value.triggerCondition.continuousFailure &&
      !faultRuleConfigForm.value.triggerCondition.errorCodes) {
    ElMessage.warning('请至少选择一个触发条件')
    return
  }

  // 验证自动重试配置
  if (faultRuleConfigForm.value.executeAction === 'autoRetry') {
    if (faultRuleConfigForm.value.autoRetryConfig.interval < 1 ||
        faultRuleConfigForm.value.autoRetryConfig.interval > 300) {
      ElMessage.warning('回调间隔应在1-300秒之间')
      return
    }
    if (faultRuleConfigForm.value.autoRetryConfig.maxTimes < 1 ||
        faultRuleConfigForm.value.autoRetryConfig.maxTimes > 10) {
      ElMessage.warning('重试次数应在1-10次之间')
      return
    }
  }

  // 显示Loading
  faultHandlingLoading.value = true

  // 模拟保存过程
  setTimeout(() => {
    // 保存配置
    const configData = {
      ...faultRuleConfigForm.value,
      updateTime: new Date().toISOString(),
      updateUser: '当前用户'
    }

    localStorage.setItem('faultRuleConfig', JSON.stringify(configData))
    ElMessage.success('故障规则配置已保存')

    console.log('保存的故障规则配置:', configData)

    // 隐藏Loading
    faultHandlingLoading.value = false

    // 返回预防规则页面
    faultHandlingActiveTab.value = 'prevention'
  }, 800) // 模拟保存延迟
}

// 组件卸载时清理监听器
onUnmounted(() => {
  console.log('组件卸载，清理监听器...')
  window.removeEventListener('storage', handleStorageChange)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  document.removeEventListener('click', handleGlobalClick)
  stopPeriodicCheck()
})
</script>

<template>
  <div class="ledger-access">
    <!-- 黄色提示框 -->
    <div v-if="showWarningTips" class="warning-tips">
      <div class="tip-item">
        <i class="el-icon-warning"></i>
        <span>1、接入人员：应确保接入数据源的人员具备相关权限，仅授权中心有权限上传数据到数据库中，其他人员不得上传数据到数据库中。</span>
      </div>
      <div class="tip-item">
        <span>2、数据源配置：应确保数据源配置正确，包括：数据源地址、端口、数据库名称等。</span>
      </div>
      <div class="tip-item">
        <span>3、云端数据库：应确保云端数据库的可用性，中断数据库连接可能导致数据丢失。</span>
      </div>
      <el-button
        type="text"
        class="close-btn"
        @click="showWarningTips = false"
      >
        ×
      </el-button>
    </div>

    <div class="main-content">
      <!-- 左侧分组导航 -->
      <div class="left-sidebar">
        <div class="sidebar-header">
          <span class="active">数据源分组</span>
          <el-button size="small" type="primary" @click="addGroup">
            <i class="el-icon-plus"></i> 添加分组
          </el-button>
        </div>
        <div class="group-list">
          <div
            v-for="group in groupList"
            :key="group.id"
            :class="['group-item', { active: selectedGroupId === group.id }]"
          >
            <!-- 编辑模式 -->
            <template v-if="editingGroupId === group.id">
              <el-input
                v-model="editingGroupName"
                size="small"
                @blur="finishEditGroup"
                @keyup.enter="finishEditGroup"
                @keyup.esc="cancelEditGroup"
                @change="finishEditGroup"
                ref="editInput"
                style="flex: 1;"
                autofocus
              />
            </template>

            <!-- 显示模式 -->
            <template v-else>
              <span
                class="group-name"
                @click="onGroupChange(group.id)"
                @dblclick="startEditGroup(group.id, group.name)"
              >
                {{ group.name }}
              </span>
              <el-button
                size="small"
                type="danger"
                text
                @click.stop="deleteGroup(group.id)"
                class="delete-btn"
              >
                ×
              </el-button>
            </template>
          </div>
        </div>
      </div>

      <!-- 右侧主内容区 -->
      <div class="right-content">
        <Block title="数据接入" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
          <template #topRight>
            <div class="top-actions">
              <el-button size="small" type="primary" @click="onClickAdd">新增</el-button>
              <el-button size="small" type="info" @click="handleDataSourceChange">
                <el-icon><Refresh /></el-icon>
                刷新数据源
              </el-button>

              <!-- 日志管理下拉菜单 -->
              <el-dropdown trigger="click" @command="onLogManageClick">
                <el-button size="small">
                  日志管理<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="log-record">日志记录</el-dropdown-item>
                    <el-dropdown-item command="log-clean">日志清理规则</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <!-- 缓存管理下拉菜单 -->
              <el-dropdown trigger="click" @command="onCacheManageClick">
                <el-button size="small">
                  缓存管理<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="cache-config">缓存配置</el-dropdown-item>
                    <el-dropdown-item command="cache-view">缓存查看</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <el-button size="small" @click="openSubscriptionDialog">数据订阅</el-button>
              <el-button size="small" @click="openModelManagementDialog">模型管理</el-button>
              <el-button size="small" @click="openFaultHandlingDialog">数据接入故障处理</el-button>

              <!-- 更多下拉菜单 -->
              <el-dropdown trigger="click" @command="onMoreTopMenuClick">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="data-monitor">数据接入监控指标</el-dropdown-item>
                    <el-dropdown-item command="data-alert">数据接入报警</el-dropdown-item>
                    <el-dropdown-item command="quality-config">质量评估指标设置</el-dropdown-item>
                    <el-dropdown-item command="feedback-manage">反馈管理</el-dropdown-item>
                    <el-dropdown-item command="rule-config">规则设置</el-dropdown-item>
                    <el-dropdown-item command="training">文档与培训</el-dropdown-item>
                    <el-dropdown-item command="process-control">过程控制</el-dropdown-item>
                    <el-dropdown-item command="interface-rules">数据接入接口规则</el-dropdown-item>
                    <el-dropdown-item command="task-progress">接入任务调度</el-dropdown-item>
                    <el-dropdown-item command="data-read">错误数据</el-dropdown-item>
                    <el-dropdown-item command="operation-system">兼容操作系统</el-dropdown-item>
                    <el-dropdown-item command="data-push">数据推送</el-dropdown-item>
                    <el-dropdown-item command="data-index">数据索引</el-dropdown-item>
                    <el-dropdown-item command="cloud-resource">云资源管理与部署</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>

          <template #expand>
            <!-- 搜索区域 -->
            <div class="search">
              <Form
                :props="searchFormProp"
                v-model="searchForm"
                :column-count="3"
                :label-width="80"
                :enable-reset="false"
                confirm-text="查询"
                button-vertical="flowing"
                @submit="onSearch"
              />
            </div>
          </template>

          <!-- 数据表格 -->
          <el-table
            ref="tableRef"
            :data="tableData"
            :height="tableHeight"
            v-loading="loading"
            stripe
            border
          >
            <el-table-column
              v-for="column in columns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              :min-width="column.minWidth"
            >
              <template #default="{ row }">
                <!-- 使用formatter格式化显示 -->
                <template v-if="column.formatter">
                  {{ column.formatter(row) }}
                </template>
                <!-- 状态列特殊处理 -->
                <template v-else-if="column.prop === 'status'">
                  <el-tag
                    :type="getStatusConfig(row.status).type"
                    effect="light"
                    size="small"
                  >
                    <i :class="getStatusConfig(row.status).icon" style="margin-right: 4px;"></i>
                    {{ getStatusConfig(row.status).text }}
                  </el-tag>
                </template>
                <!-- 进度列特殊处理 -->
                <template v-else-if="column.prop === 'progress'">
                  <div class="progress-cell">
                    <el-progress
                      :percentage="row.progress"
                      :status="row.status === '已完成' ? 'success' : row.status === '接入失败' ? 'exception' : ''"
                      :stroke-width="8"
                      :show-text="false"
                    />
                    <span class="progress-text">
                      {{ row.progress }}%
                      <span v-if="row.speed" class="speed">{{ row.speed }}</span>
                    </span>
                  </div>
                </template>
                <!-- 默认显示 -->
                <template v-else>
                  {{ row[column.prop] }}
                </template>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <template v-for="btn in getActionButtons(row)" :key="btn.code">
                  <!-- 普通按钮 -->
                  <el-button
                    v-if="!btn.dropdown"
                    :type="btn.type"
                    size="small"
                    @click="onTableClickButton({ row, btn })"
                  >
                    {{ btn.label }}
                  </el-button>

                  <!-- 下拉菜单按钮 -->
                  <el-dropdown
                    v-else
                    trigger="click"
                    @command="(command) => onMoreMenuClick(row, command)"
                  >
                    <el-button :type="btn.type" size="small">
                      {{ btn.label }}
                      <el-icon class="el-icon--right"><arrow-down /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          v-for="option in getMoreMenuOptions()"
                          :key="option.code"
                          :command="option.code"
                        >
                          {{ option.label }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <Pagination
            :total="pagination.total"
            :current-page="pagination.page"
            :page-size="pagination.size"
            @current-change="onPaginationChange($event, 'page')"
            @size-change="onPaginationChange($event, 'size')"
          />
        </Block>
      </div>
    </div>

    <!-- 新增数据接入弹窗 -->
    <el-dialog
      v-model="showDialogForm"
      title="数据接入"
      width="800px"
      :close-on-click-modal="false"
      :show-close="true"
    >
      <!-- 步骤指示器 -->
      <div class="step-indicator">
        <div class="step-item" :class="{ active: currentStep === 1, completed: currentStep > 1 }">
          <div class="step-number">1</div>
          <div class="step-title">数据接入</div>
        </div>
        <div class="step-line"></div>
        <div class="step-item" :class="{ active: currentStep === 2 }">
          <div class="step-number">2</div>
          <div class="step-title">接入顺序</div>
        </div>
      </div>

      <!-- 第一步：数据接入配置 -->
      <div v-if="currentStep === 1" class="step-content">
        <el-form :model="step1Form" label-width="120px">
          <!-- 所属分组 -->
          <el-form-item label="所属分组" required>
            <el-select
              v-model="step1Form.groupId"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="group in groupList"
                :key="group.id"
                :label="group.name"
                :value="group.id"
              />
            </el-select>
          </el-form-item>

          <!-- 接入方式 -->
          <el-form-item label="接入方式" required>
            <el-radio-group v-model="step1Form.accessMethod">
              <el-radio label="auto">数据接入自动检测</el-radio>
              <el-radio label="manual">指定数据源</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 自动检测模式 -->
          <div v-if="step1Form.accessMethod === 'auto'" class="auto-detect-section">
            <div class="detect-tip">
              扫描发现{{ detectedDataSources.length }}个可接入的数据源：
            </div>
            <div v-if="detectedDataSources.length === 0" class="no-data-tip">
              <el-alert
                title="未发现可接入的数据源"
                description="请先在数据源管理页面添加数据源"
                type="warning"
                :closable="false"
              />
            </div>
            <div v-else class="data-source-list">
              <el-checkbox-group v-model="step1Form.selectedDataSources">
                <div
                  v-for="source in detectedDataSources"
                  :key="source.id"
                  class="data-source-item"
                >
                  <el-checkbox :label="source.id">
                    {{ source.name }} ({{ source.description }})
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </div>

          <!-- 指定数据源模式 -->
          <div v-if="step1Form.accessMethod === 'manual'">
            <el-form-item label="数据源" required>
              <div v-if="dataSourceOptions.length === 0" class="no-data-tip">
                <el-alert
                  title="暂无可选择的数据源"
                  description="请先在数据源管理页面添加数据源"
                  type="warning"
                  :closable="false"
                />
              </div>
              <el-select
                v-else
                v-model="step1Form.selectedDataSources"
                placeholder="请选择（可多选）"
                multiple
                style="width: 100%"
              >
                <el-option
                  v-for="option in dataSourceOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <!-- 数据接入类型 -->
          <el-form-item label="数据接入类型" required>
            <el-radio-group v-model="step1Form.accessType">
              <el-radio label="extract">数据抽样接入</el-radio>
              <el-radio label="full">全量数据接入</el-radio>
              <el-radio label="increment">增量数据接入</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 版本号 -->
          <el-form-item label="版本号" required>
            <el-input
              v-model="step1Form.version"
              placeholder="请输入"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 第二步：接入顺序 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="order-list">
          <div
            v-for="(item, index) in step2Form.dataSourceOrder"
            :key="item.id"
            class="order-item"
          >
            <span class="order-number">{{ index + 1 }}、</span>
            <span class="order-name">{{ item.name }} ({{ item.description }})</span>
            <div class="order-actions">
              <el-button
                v-if="index > 0"
                size="small"
                type="text"
                @click="moveUp(index)"
              >
                ↑
              </el-button>
              <el-button
                v-if="index < step2Form.dataSourceOrder.length - 1"
                size="small"
                type="text"
                @click="moveDown(index)"
              >
                ↓
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <!-- 第一步按钮 -->
          <template v-if="currentStep === 1">
            <el-button @click="showDialogForm = false">取消</el-button>
            <el-button type="primary" @click="onNextStep">下一步</el-button>
          </template>

          <!-- 第二步按钮 -->
          <template v-if="currentStep === 2">
            <el-button @click="onPrevStep">上一步</el-button>
            <el-button type="info" @click="onStabilityTest">稳定性检测</el-button>
            <el-button type="primary" :loading="loading" @click="onFinalSubmit">提交</el-button>
          </template>
        </div>
      </template>
    </el-dialog>

    <!-- 稳定性检测弹窗 -->
    <el-dialog
      v-model="showStabilityTest"
      title="稳定性检测"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="stability-test-content">
        <div v-if="!stabilityTestResult" class="loading-content">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>正在检测稳定性...</span>
        </div>
        <div v-else class="result-content">
          <div class="success-icon">
            <el-icon color="#67C23A" size="24"><SuccessFilled /></el-icon>
          </div>
          <div class="result-text">{{ stabilityTestResult }}</div>
        </div>
      </div>

      <template #footer>
        <el-button @click="closeStabilityTest">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 日志清理规则设置弹窗 -->
    <el-dialog
      v-model="showLogCleanDialog"
      title="日志清理设置"
      width="600px"
      :close-on-click-modal="false"
      @open="onLogCleanDialogOpen"
      @close="onLogCleanDialogClose"
    >
      <el-form :model="logCleanForm" label-width="120px">
        <!-- 清理周期 -->
        <el-form-item label="清理周期" required>
          <el-select
            v-model="logCleanForm.cleanPeriod"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="option in cleanPeriodOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 保留时长 -->
        <el-form-item label="保留时长（天）" required>
          <el-input
            v-model="logCleanForm.retentionDays"
            placeholder="请输入（单位天）"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 清理时间 -->
        <el-form-item label="清理时间" required>
          <el-select
            v-model="logCleanForm.cleanTime"
            placeholder="请选择（时分秒）"
            style="width: 100%"
          >
            <el-option
              v-for="option in cleanTimeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 清理范围设置 -->
        <el-form-item label="清理范围设置" required>
          <div class="clean-range-settings">
            <el-checkbox v-model="logCleanForm.cleanRangeSettings.cleanSuccessLogs">
              清理成功的日志记录
            </el-checkbox>
            <el-checkbox v-model="logCleanForm.cleanRangeSettings.cleanErrorLogs">
              清理错误的日志记录
            </el-checkbox>
            <el-checkbox v-model="logCleanForm.cleanRangeSettings.cleanFormatConvertLogs">
              清理格式转换日志
            </el-checkbox>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onLogCleanDialogClose">取消</el-button>
          <el-button type="primary" @click="onLogCleanSubmit">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据缓存查看弹窗 -->
    <el-dialog
      v-model="showCacheViewDialog"
      title="数据缓存查看"
      width="1000px"
      :close-on-click-modal="false"
      @open="onCacheViewDialogOpen"
      @close="onCacheViewDialogClose"
    >
      <!-- 搜索区域 -->
      <div class="cache-search-area">
        <el-input
          v-model="cacheSearchForm.dataSourceName"
          placeholder="数据源名称"
          style="width: 200px; margin-right: 12px;"
        />
        <el-button type="primary" @click="onCacheSearch">查询</el-button>
        <el-button @click="onCacheReset">重置</el-button>
      </div>

      <!-- 缓存数据表格 -->
      <div class="cache-table-container">
        <el-table
          :data="filteredCacheData"
          border
          style="width: 100%"
          max-height="400px"
        >
          <el-table-column prop="cacheId" label="缓存id" width="120" align="center" />
          <el-table-column prop="dataSourceName" label="数据源名称" width="150" align="center" />
          <el-table-column prop="size" label="大小" width="100" align="center" />
          <el-table-column prop="createTime" label="创建时间" width="150" align="center" />
          <el-table-column label="过期时间" width="150" align="center">
            <template #default="{ row }">
              <span :class="getExpireTimeClass(row.status)">
                {{ formatExpireTime(row.expireTime, row.status) }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onCacheViewDialogClose">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据接入安全认证规则设置弹窗 -->
    <el-dialog
      v-model="showAuthConfigDialog"
      title="数据接入安全认证规则设置"
      width="700px"
      :close-on-click-modal="false"
      @open="onAuthConfigDialogOpen"
      @close="onAuthConfigDialogClose"
    >
      <!-- 显示当前配置的数据源信息 -->
      <div class="auth-config-header" v-if="authConfigForm.dataSourceName">
        <el-alert
          :title="`正在为数据源「${authConfigForm.dataSourceName}」配置认证规则`"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 20px;"
        />
      </div>

      <el-form :model="authConfigForm" label-width="140px">
        <!-- 认证方式 -->
        <el-form-item label="认证方式" required>
          <el-radio-group v-model="authConfigForm.authMethod">
            <el-radio value="apiKey">API Key</el-radio>
            <el-radio value="oauth2">OAuth 2.0</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- API Key 配置 -->
        <template v-if="authConfigForm.authMethod === 'apiKey'">
          <!-- API Key 名称 -->
          <el-form-item label="API Key 名称" required>
            <el-input
              v-model="authConfigForm.apiKeyName"
              placeholder="请输入"
              style="width: 100%"
            />
          </el-form-item>

          <!-- 访问密钥 -->
          <el-form-item label="访问密钥(Access Key)" required>
            <el-input
              v-model="authConfigForm.accessKey"
              placeholder="请输入"
              style="width: 100%"
            />
          </el-form-item>

          <!-- 密钥 -->
          <el-form-item label="密钥(Secret Key)" required>
            <el-input
              v-model="authConfigForm.secretKey"
              placeholder="请输入"
              type="password"
              show-password
              style="width: 100%"
            />
          </el-form-item>
        </template>

        <!-- 有效期设置 -->
        <el-form-item label="有效期设置" required>
          <el-select
            v-model="authConfigForm.validityPeriod"
            placeholder="请选择密钥过期时间"
            style="width: 100%"
          >
            <el-option
              v-for="option in validityPeriodOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 过期提醒方式 -->
        <el-form-item label="过期提醒方式" required>
          <div class="expire-wakeup-methods">
            <el-checkbox v-model="authConfigForm.expireWakeupMethods.siteMessage">
              站内信
            </el-checkbox>
            <el-checkbox v-model="authConfigForm.expireWakeupMethods.email">
              邮快证
            </el-checkbox>
            <el-checkbox v-model="authConfigForm.expireWakeupMethods.dingTalk">
              邮快证DING
            </el-checkbox>
          </div>
        </el-form-item>

        <!-- 备注 -->
        <el-form-item label="备注">
          <el-input
            v-model="authConfigForm.remarks"
            type="textarea"
            :rows="4"
            placeholder="请输入"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onAuthConfigDialogClose">取消</el-button>
          <el-button type="primary" @click="onAuthConfigSubmit">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据接入日志记录弹窗 -->
    <el-dialog
      v-model="showLogDialog"
      title="数据接入日志"
      width="900px"
      :close-on-click-modal="false"
    >
      <!-- 搜索区域 -->
      <div class="log-search-area">
        <el-form :model="logSearchForm" inline>
          <el-form-item>
            <el-input
              v-model="logSearchForm.dataSource"
              placeholder="数据源"
              style="width: 200px;"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="logSearchForm.operationTime"
              placeholder="操作时间"
              style="width: 200px;"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchLogs">查询</el-button>
            <el-button @click="resetLogSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 日志表格 -->
      <div class="log-table-area">
        <el-table
          :data="logTableData"
          v-loading="logTableLoading"
          border
          stripe
          style="width: 100%"
          max-height="400px"
        >
          <el-table-column label="序号" width="80" align="center" type="index" :index="(index) => index + 1" />
          <el-table-column prop="logId" label="日志id" width="100" align="center" />
          <el-table-column prop="dataSource" label="数据源" width="150" />
          <el-table-column prop="accessType" label="接入方式" width="150" />
          <el-table-column prop="operationTime" label="操作时间" width="180" />
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showLogDialog = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据订阅弹窗 -->
    <el-dialog
      v-model="showSubscriptionDialog"
      title="数据订阅"
      width="800px"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <div class="subscription-dialog-content">
        <!-- 表单区域 -->
        <div class="subscription-form-area">
          <el-form :model="subscriptionForm" label-width="120px" ref="subscriptionFormRef">
            <!-- 关联数据源 -->
            <el-form-item
              label="关联数据源"
              prop="dataSourceId"
              :rules="[{ required: true, message: '请选择关联数据源', trigger: 'change' }]"
            >
              <el-select
                v-model="subscriptionForm.dataSourceId"
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option
                  v-for="option in dataSourceOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <!-- 触发条件 -->
            <el-form-item label="触发条件">
              <div class="trigger-condition-area">
                <!-- 接入状态 -->
                <div class="condition-item">
                  <span class="condition-label">接入状态</span>
                  <el-checkbox-group v-model="subscriptionForm.triggerCondition.accessStatus">
                    <el-checkbox label="成功">成功</el-checkbox>
                    <el-checkbox label="失败">失败</el-checkbox>
                  </el-checkbox-group>
                  <span class="condition-hint">可多选</span>
                </div>

                <!-- 接入数量 -->
                <div class="condition-item">
                  <el-checkbox v-model="enableAccessCount">接入数量</el-checkbox>
                  <el-select
                    v-model="subscriptionForm.triggerCondition.accessCount.operator"
                    :disabled="!enableAccessCount"
                    style="width: 120px; margin-left: 10px; margin-right: 10px;"
                  >
                    <el-option
                      v-for="option in accessCountOperatorOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                  <el-input
                    v-model="subscriptionForm.triggerCondition.accessCount.value"
                    :disabled="!enableAccessCount"
                    placeholder="请输入（例如1000条）"
                    style="width: 200px;"
                  />
                </div>

                <!-- 时间范围 -->
                <div class="condition-item">
                  <el-checkbox v-model="enableTimeRange">时间范围</el-checkbox>
                  <el-date-picker
                    v-model="subscriptionForm.triggerCondition.timeRange.startTime"
                    :disabled="!enableTimeRange"
                    type="datetime"
                    placeholder="开始时间"
                    style="width: 150px; margin-left: 10px; margin-right: 10px;"
                  />
                  <span>至</span>
                  <el-date-picker
                    v-model="subscriptionForm.triggerCondition.timeRange.endTime"
                    :disabled="!enableTimeRange"
                    type="datetime"
                    placeholder="结束时间"
                    style="width: 150px; margin-left: 10px;"
                  />
                </div>
              </div>
            </el-form-item>

            <!-- 订阅频率 -->
            <el-form-item label="订阅频率">
              <el-radio-group v-model="subscriptionForm.subscriptionFrequency">
                <el-radio value="realtime">实时</el-radio>
                <el-radio value="weekly">每周</el-radio>
                <el-radio value="monthly">每月</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 通知方式 -->
            <el-form-item label="通知方式">
              <div class="notification-methods">
                <el-checkbox v-model="subscriptionForm.notificationMethods.siteMessage">站内信</el-checkbox>
                <el-checkbox v-model="subscriptionForm.notificationMethods.email">愉快证</el-checkbox>
                <el-checkbox v-model="subscriptionForm.notificationMethods.dingTalk">愉快DING</el-checkbox>
              </div>
            </el-form-item>
          </el-form>

          <!-- 添加订阅按钮 -->
          <div class="add-subscription-btn">
            <el-button type="primary" :loading="subscriptionLoading" @click="addSubscription">添加订阅</el-button>
          </div>
        </div>

        <!-- 订阅记录表格 -->
        <div class="subscription-table-area">
          <el-table :data="allSubscriptions" style="width: 100%" max-height="300">
            <el-table-column label="序号" width="60" type="index" :index="1" />
            <el-table-column prop="dataSourceName" label="关联数据源" width="120" />
            <el-table-column label="触发条件" width="200">
              <template #default="{ row }">
                <div class="trigger-condition-display">
                  <div v-if="row.triggerCondition.accessStatus.length > 0">
                    接入状态：{{ row.triggerCondition.accessStatus.join('、') }}
                  </div>
                  <div v-if="row.triggerCondition.accessCount?.value">
                    接入数量{{ row.triggerCondition.accessCount.operator }}{{ row.triggerCondition.accessCount.value }}条
                  </div>
                  <div v-if="row.triggerCondition.timeRange?.startTime">
                    时间范围：{{ formatDateTime(row.triggerCondition.timeRange.startTime) }} 至 {{ formatDateTime(row.triggerCondition.timeRange.endTime) }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="订阅频率" width="80">
              <template #default="{ row }">
                {{ getFrequencyLabel(row.subscriptionFrequency) }}
              </template>
            </el-table-column>
            <el-table-column label="通知方式" width="120">
              <template #default="{ row }">
                {{ getNotificationMethodsLabel(row.notificationMethods) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ $index }">
                <el-button type="danger" size="small" text @click="deleteSubscription($index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelSubscriptionDialog">取消</el-button>
          <el-button type="primary" :loading="subscriptionLoading" @click="confirmSubscriptionDialog">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模型分析弹窗 -->
    <el-dialog
      v-model="showModelAnalysisDialog"
      title="模型分析"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentAnalysisData" class="model-analysis-content">
        <!-- 第一行：分析结果标题 -->
        <div class="analysis-title">
          <h3>分析结果</h3>
        </div>

        <!-- 第二行：准确率和召回率（50%-50%布局） -->
        <div class="analysis-metrics">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="metric-card">
                <div class="metric-label">准确率</div>
                <div class="metric-value">{{ currentAnalysisData.accuracy.toFixed(3) }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="metric-card">
                <div class="metric-label">召回率</div>
                <div class="metric-value">{{ currentAnalysisData.recall.toFixed(3) }}</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 第三行：图表区域 -->
        <div class="analysis-chart">
          <div class="chart-title">性能指标图表</div>
          <div class="chart-container">
            <!-- 简单的柱状图展示 -->
            <div class="chart-bars">
              <div class="chart-bar">
                <div class="bar-label">准确率</div>
                <div class="bar-container">
                  <div
                    class="bar-fill accuracy-bar"
                    :style="{ height: (currentAnalysisData.accuracy * 100) + '%' }"
                  ></div>
                </div>
                <div class="bar-value">{{ (currentAnalysisData.accuracy * 100).toFixed(1) }}%</div>
              </div>
              <div class="chart-bar">
                <div class="bar-label">召回率</div>
                <div class="bar-container">
                  <div
                    class="bar-fill recall-bar"
                    :style="{ height: (currentAnalysisData.recall * 100) + '%' }"
                  ></div>
                </div>
                <div class="bar-value">{{ (currentAnalysisData.recall * 100).toFixed(1) }}%</div>
              </div>
              <div class="chart-bar">
                <div class="bar-label">F1分数</div>
                <div class="bar-container">
                  <div
                    class="bar-fill f1-bar"
                    :style="{ height: (currentAnalysisData.f1Score * 100) + '%' }"
                  ></div>
                </div>
                <div class="bar-value">{{ (currentAnalysisData.f1Score * 100).toFixed(1) }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showModelAnalysisDialog = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 深度学习模型数据分析弹窗 -->
    <el-dialog
      v-model="showDLModelDataAnalysisDialog"
      title="模型分析"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div v-if="currentDLModelAnalysisData" class="dl-model-analysis-content">
        <!-- 性能指标和损失曲线 50%-50%布局 -->
        <el-row :gutter="20">
          <!-- 性能指标图表 -->
          <el-col :span="12">
            <div class="chart-container">
              <h4 class="chart-title">性能指标</h4>
              <div class="chart-area" id="performance-chart">
                <!-- 图例 -->
                <div class="chart-legend">
                  <div class="legend-item">
                    <span class="legend-color accuracy-color"></span>
                    <span class="legend-text">准确率</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color recall-color"></span>
                    <span class="legend-text">召回率</span>
                  </div>
                </div>

                <!-- 简单的折线图实现 -->
                <div class="simple-line-chart">
                  <div class="chart-y-axis">
                    <div class="y-label">100%</div>
                    <div class="y-label">80%</div>
                    <div class="y-label">60%</div>
                    <div class="y-label">40%</div>
                    <div class="y-label">20%</div>
                    <div class="y-label">0%</div>
                  </div>
                  <div class="chart-content">
                    <svg width="100%" height="200" class="line-chart-svg">
                      <!-- 网格线 -->
                      <defs>
                        <pattern id="grid" width="60" height="40" patternUnits="userSpaceOnUse">
                          <path d="M 60 0 L 0 0 0 40" fill="none" stroke="#e0e0e0" stroke-width="1"/>
                        </pattern>
                      </defs>
                      <rect width="100%" height="100%" fill="url(#grid)" />

                      <!-- 准确率折线 -->
                      <polyline
                        :points="getPerformanceLinePoints(currentDLModelAnalysisData.performanceMetrics.accuracy, 'accuracy')"
                        fill="none"
                        stroke="#409eff"
                        stroke-width="2"
                      />

                      <!-- 召回率折线 -->
                      <polyline
                        :points="getPerformanceLinePoints(currentDLModelAnalysisData.performanceMetrics.recall, 'recall')"
                        fill="none"
                        stroke="#67c23a"
                        stroke-width="2"
                      />

                      <!-- 数据点 -->
                      <g v-for="(accuracy, index) in currentDLModelAnalysisData.performanceMetrics.accuracy" :key="`acc-${index}`">
                        <circle
                          :cx="getXPosition(index)"
                          :cy="getYPosition(accuracy)"
                          r="4"
                          fill="#409eff"
                          @mouseover="showTooltip($event, 'accuracy', currentDLModelAnalysisData.performanceMetrics.epochs[index], accuracy)"
                          @mouseout="hideTooltip"
                        />
                      </g>

                      <g v-for="(recall, index) in currentDLModelAnalysisData.performanceMetrics.recall" :key="`rec-${index}`">
                        <circle
                          :cx="getXPosition(index)"
                          :cy="getYPosition(recall)"
                          r="4"
                          fill="#67c23a"
                          @mouseover="showTooltip($event, 'recall', currentDLModelAnalysisData.performanceMetrics.epochs[index], recall)"
                          @mouseout="hideTooltip"
                        />
                      </g>
                    </svg>

                    <!-- X轴标签 -->
                    <div class="chart-x-axis">
                      <div v-for="epoch in currentDLModelAnalysisData.performanceMetrics.epochs" :key="epoch" class="x-label">
                        Epoch {{ epoch }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 损失曲线图表 -->
          <el-col :span="12">
            <div class="chart-container">
              <h4 class="chart-title">损失曲线</h4>
              <div class="chart-area" id="loss-chart">
                <!-- 图例 -->
                <div class="chart-legend">
                  <div class="legend-item">
                    <span class="legend-color train-loss-color"></span>
                    <span class="legend-text">训练损失</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color validation-loss-color"></span>
                    <span class="legend-text">验证损失</span>
                  </div>
                </div>

                <!-- 简单的折线图实现 -->
                <div class="simple-line-chart">
                  <div class="chart-y-axis">
                    <div class="y-label">1.5</div>
                    <div class="y-label">1.2</div>
                    <div class="y-label">0.9</div>
                    <div class="y-label">0.6</div>
                    <div class="y-label">0.3</div>
                    <div class="y-label">0</div>
                  </div>
                  <div class="chart-content">
                    <svg width="100%" height="200" class="line-chart-svg">
                      <!-- 网格线 -->
                      <rect width="100%" height="100%" fill="url(#grid)" />

                      <!-- 训练损失折线 -->
                      <polyline
                        :points="getLossLinePoints(currentDLModelAnalysisData.lossCurves.trainLoss, 'train')"
                        fill="none"
                        stroke="#e6a23c"
                        stroke-width="2"
                      />

                      <!-- 验证损失折线 -->
                      <polyline
                        :points="getLossLinePoints(currentDLModelAnalysisData.lossCurves.validationLoss, 'validation')"
                        fill="none"
                        stroke="#f56c6c"
                        stroke-width="2"
                      />

                      <!-- 数据点 -->
                      <g v-for="(loss, index) in currentDLModelAnalysisData.lossCurves.trainLoss" :key="`train-${index}`">
                        <circle
                          :cx="getXPosition(index)"
                          :cy="getLossYPosition(loss)"
                          r="4"
                          fill="#e6a23c"
                          @mouseover="showTooltip($event, 'train', currentDLModelAnalysisData.lossCurves.epochs[index], loss)"
                          @mouseout="hideTooltip"
                        />
                      </g>

                      <g v-for="(loss, index) in currentDLModelAnalysisData.lossCurves.validationLoss" :key="`val-${index}`">
                        <circle
                          :cx="getXPosition(index)"
                          :cy="getLossYPosition(loss)"
                          r="4"
                          fill="#f56c6c"
                          @mouseover="showTooltip($event, 'validation', currentDLModelAnalysisData.lossCurves.epochs[index], loss)"
                          @mouseout="hideTooltip"
                        />
                      </g>
                    </svg>

                    <!-- X轴标签 -->
                    <div class="chart-x-axis">
                      <div v-for="epoch in currentDLModelAnalysisData.lossCurves.epochs" :key="epoch" class="x-label">
                        Epoch {{ epoch }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 悬浮提示框 -->
        <div
          v-show="tooltipVisible"
          class="chart-tooltip"
          :style="{ left: tooltipX + 'px', top: tooltipY + 'px' }"
        >
          <div class="tooltip-content">
            <div class="tooltip-title">Epoch {{ tooltipEpoch }}</div>
            <div class="tooltip-item">
              <span class="tooltip-label">{{ tooltipLabel }}:</span>
              <span class="tooltip-value">{{ tooltipValue }}</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDLModelDataAnalysisDialog = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模型管理弹窗 -->
    <el-dialog
      v-model="showModelManagementDialog"
      title="模型管理"
      width="1200px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="model-management-content">
        <!-- 左侧导航菜单 -->
        <div class="model-nav-menu">
          <div
            class="nav-item"
            :class="{ active: activeModelTab === 'dataAccess' }"
            @click="switchModelTab('dataAccess')"
          >
            数据接入算法规则配置
          </div>
          <div
            class="nav-item"
            :class="{ active: activeModelTab === 'mlModel' }"
            @click="switchModelTab('mlModel')"
          >
            机器学习模型管理
          </div>
          <div
            class="nav-item"
            :class="{ active: activeModelTab === 'dlModel' }"
            @click="switchModelTab('dlModel')"
          >
            深度学习模型管理
          </div>
          <div
            class="nav-item"
            :class="{ active: activeModelTab === 'nlpConfig' }"
            @click="switchModelTab('nlpConfig')"
          >
            自然语言处理配置
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="model-content-area">
          <!-- 数据接入算法规则配置 -->
          <div v-if="activeModelTab === 'dataAccess'" class="model-tab-content">
            <div class="tab-header">
              <h3>数据接入算法规则配置</h3>
              <el-button type="primary" size="small" @click="openAddAlgorithmDialog">新增算法规则</el-button>
            </div>
            <el-table :data="algorithmRules" v-loading="tableLoading" style="width: 100%">
              <el-table-column prop="name" label="规则名称" />
              <el-table-column prop="algorithmType" label="算法类型" />
              <el-table-column prop="status" label="状态">
                <template #default="{ row }">
                  <el-tag :type="row.status ? 'success' : 'danger'">
                    {{ row.status ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间" />
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button type="primary" link size="small" @click="editAlgorithmRule(row)">编辑</el-button>
                  <el-button type="danger" link size="small" @click="deleteAlgorithmRule(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 机器学习模型管理 -->
          <div v-if="activeModelTab === 'mlModel'" class="model-tab-content">
            <div class="tab-header">
              <h3>机器学习模型管理</h3>
              <el-button type="primary" size="small" @click="openAddMLModelDialog">新增模型</el-button>
            </div>
            <el-table :data="mlModels" v-loading="tableLoading" style="width: 100%">
              <el-table-column prop="name" label="模型名称" />
              <el-table-column prop="description" label="模型描述" />
              <el-table-column prop="dataSource" label="数据源" />
              <el-table-column prop="status" label="状态">
                <template #default="{ row }">
                  <el-tag
                    :type="row.status === '已部署' ? 'success' : row.status === '已训练' ? 'warning' : 'info'"
                  >
                    {{ row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="creator" label="创建人" />
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button type="primary" link size="small" @click="editMLModel(row)">编辑</el-button>
                  <el-button type="danger" link size="small" @click="deleteMLModel(row)">删除</el-button>
                  <el-button type="primary" link size="small" @click="openMLModelAnalysis(row)">模型分析</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 深度学习模型管理 -->
          <div v-if="activeModelTab === 'dlModel'" class="model-tab-content">
            <div class="tab-header">
              <h3>深度学习模型管理</h3>
              <el-button type="primary" size="small" @click="openAddDLModelDialog">新增模型</el-button>
            </div>
            <el-table :data="dlModels" v-loading="tableLoading" style="width: 100%">
              <el-table-column prop="name" label="模型名称" />
              <el-table-column prop="description" label="模型描述" />
              <el-table-column prop="dataSource" label="数据源" />
              <el-table-column prop="status" label="状态">
                <template #default="{ row }">
                  <el-tag
                    :type="row.status === '已部署' ? 'success' : row.status === '已训练' ? 'warning' : 'info'"
                  >
                    {{ row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="algorithm" label="模型算法" />
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button type="primary" link size="small" @click="editDLModel(row)">编辑</el-button>
                  <el-button type="danger" link size="small" @click="deleteDLModel(row)">删除</el-button>
                  <el-button type="primary" link size="small" @click="openDLModelDataAnalysis(row)">数据分析</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 自然语言处理配置 -->
          <div v-if="activeModelTab === 'nlpConfig'" class="model-tab-content">
            <div class="tab-header">
              <h3>自然语言处理配置</h3>
              <el-button type="primary" size="small" @click="openAddNLPConfigDialog">新增</el-button>
            </div>
            <el-table :data="nlpConfigs" v-loading="tableLoading" style="width: 100%">
              <el-table-column prop="name" label="规则名称" />
              <el-table-column prop="algorithmType" label="算法类型" />
              <el-table-column prop="languageModel" label="语言模型" />
              <el-table-column prop="creator" label="创建人" />
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button type="primary" link size="small" @click="editNLPConfig(row)">编辑</el-button>
                  <el-button type="danger" link size="small" @click="deleteNLPConfig(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showModelManagementDialog = false">取消</el-button>
          <el-button type="primary" :loading="modelManagementLoading" @click="confirmModelManagement">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增/编辑算法规则弹窗 -->
    <el-dialog
      v-model="showAddAlgorithmDialog"
      :title="currentEditingItem && currentEditingType === 'algorithm' ? '编辑算法规则' : '新增算法规则'"
      width="500px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <el-form
        ref="algorithmFormRef"
        :model="algorithmForm"
        :rules="algorithmFormRules"
        label-width="100px"
      >
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="algorithmForm.name" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="算法类型" prop="algorithmType">
          <el-select v-model="algorithmForm.algorithmType" placeholder="请选择" style="width: 100%">
            <el-option
              v-for="option in algorithmTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="规则描述">
          <el-input
            v-model="algorithmForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch
            v-model="algorithmForm.status"
            active-text="开"
            inactive-text=""
            :active-value="true"
            :inactive-value="false"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddAlgorithmDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmAddAlgorithm">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增/编辑机器学习模型弹窗 -->
    <el-dialog
      v-model="showAddMLModelDialog"
      :title="currentEditingItem && currentEditingType === 'mlModel' ? '编辑机器学习模型' : '新增机器学习模型'"
      width="600px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <el-form
        ref="mlModelFormRef"
        :model="mlModelForm"
        :rules="mlModelFormRules"
        label-width="100px"
      >
        <el-form-item label="模型名称" prop="name">
          <el-input v-model="mlModelForm.name" placeholder="请输入模型名称" />
        </el-form-item>
        <el-form-item label="模型描述" prop="description">
          <el-input
            v-model="mlModelForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模型描述"
          />
        </el-form-item>
        <el-form-item label="数据源" prop="dataSource">
          <el-select v-model="mlModelForm.dataSource" placeholder="请选择" style="width: 100%">
            <el-option
              v-for="option in dataSourceOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模型选择" prop="modelSelection">
          <el-select v-model="mlModelForm.modelSelection" placeholder="请选择" style="width: 100%">
            <el-option label="线性回归" value="线性回归" />
            <el-option label="逻辑回归" value="逻辑回归" />
            <el-option label="决策树" value="决策树" />
            <el-option label="随机森林" value="随机森林" />
          </el-select>
        </el-form-item>
        <el-form-item label="训练数据">
          <el-upload
            :before-upload="handleMLModelFileUpload"
            :show-file-list="false"
            accept=".csv,.xlsx,.json,.txt"
          >
            <el-button type="primary" plain>
              <el-icon><Upload /></el-icon>
              上传文件
            </el-button>
          </el-upload>
          <div v-if="mlModelForm.trainingData" class="upload-success">
            已上传：{{ mlModelForm.trainingData }}
          </div>
        </el-form-item>
        <el-form-item label="定时训练时间" prop="scheduledTrainingTime">
          <el-select v-model="mlModelForm.scheduledTrainingTime" placeholder="请选择" style="width: 100%">
            <el-option label="每日" value="每日" />
            <el-option label="每周" value="每周" />
            <el-option label="每月" value="每月" />
          </el-select>
        </el-form-item>
        <el-form-item label="部署环境" prop="deploymentEnvironment">
          <el-select v-model="mlModelForm.deploymentEnvironment" placeholder="请选择" style="width: 100%">
            <el-option label="开发环境" value="开发环境" />
            <el-option label="测试环境" value="测试环境" />
            <el-option label="生产环境" value="生产环境" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务器地址" prop="serviceAddress">
          <el-input v-model="mlModelForm.serviceAddress" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="部署版本" prop="deploymentVersion">
          <el-input v-model="mlModelForm.deploymentVersion" placeholder="请输入" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddMLModelDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmAddMLModel">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增/编辑深度学习模型弹窗 -->
    <el-dialog
      v-model="showAddDLModelDialog"
      :title="currentEditingItem && currentEditingType === 'dlModel' ? '编辑深度学习模型' : '新增深度学习模型'"
      width="600px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <el-form
        ref="dlModelFormRef"
        :model="dlModelForm"
        :rules="dlModelFormRules"
        label-width="100px"
      >
        <el-form-item label="模型名称" prop="name">
          <el-input v-model="dlModelForm.name" placeholder="请输入模型名称" />
        </el-form-item>
        <el-form-item label="模型描述" prop="description">
          <el-input
            v-model="dlModelForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模型描述"
          />
        </el-form-item>
        <el-form-item label="数据源" prop="dataSource">
          <el-select v-model="dlModelForm.dataSource" placeholder="请选择" style="width: 100%">
            <el-option
              v-for="option in dataSourceOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模型算法" prop="modelAlgorithm">
          <el-select v-model="dlModelForm.modelAlgorithm" placeholder="请选择" style="width: 100%">
            <el-option label="CNN" value="CNN" />
            <el-option label="RNN" value="RNN" />
            <el-option label="LSTM" value="LSTM" />
            <el-option label="Transformer" value="Transformer" />
          </el-select>
        </el-form-item>
        <el-form-item label="训练数据">
          <el-upload
            :before-upload="handleDLModelFileUpload"
            :show-file-list="false"
            accept=".csv,.xlsx,.json,.txt"
          >
            <el-button type="primary" plain>
              <el-icon><Upload /></el-icon>
              上传文件
            </el-button>
          </el-upload>
          <div v-if="dlModelForm.trainingData" class="upload-success">
            已上传：{{ dlModelForm.trainingData }}
          </div>
        </el-form-item>
        <el-form-item label="定时训练时间" prop="scheduledTrainingTime">
          <el-select v-model="dlModelForm.scheduledTrainingTime" placeholder="请选择" style="width: 100%">
            <el-option label="每日" value="每日" />
            <el-option label="每周" value="每周" />
            <el-option label="每月" value="每月" />
          </el-select>
        </el-form-item>
        <el-form-item label="训练迭代次数" prop="trainingIterations">
          <el-input v-model="dlModelForm.trainingIterations" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="部署环境" prop="deploymentEnvironment">
          <el-select v-model="dlModelForm.deploymentEnvironment" placeholder="请选择" style="width: 100%">
            <el-option label="开发环境" value="开发环境" />
            <el-option label="测试环境" value="测试环境" />
            <el-option label="生产环境" value="生产环境" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务器地址" prop="serviceAddress">
          <el-input v-model="dlModelForm.serviceAddress" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="部署版本" prop="deploymentVersion">
          <el-input v-model="dlModelForm.deploymentVersion" placeholder="请输入" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddDLModelDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmAddDLModel">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增/编辑自然语言处理配置弹窗 -->
    <el-dialog
      v-model="showAddNLPConfigDialog"
      :title="currentEditingItem && currentEditingType === 'nlpConfig' ? '编辑自然语言处理配置' : '新增自然语言处理配置'"
      width="600px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <el-form
        ref="nlpConfigFormRef"
        :model="nlpConfigForm"
        :rules="nlpConfigFormRules"
        label-width="100px"
      >
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="nlpConfigForm.name" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="算法类型" prop="algorithmType">
          <el-select v-model="nlpConfigForm.algorithmType" placeholder="请选择" style="width: 100%">
            <el-option
              v-for="option in nlpAlgorithmTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="规则配置" prop="ruleConfiguration">
          <el-input
            v-model="nlpConfigForm.ruleConfiguration"
            type="textarea"
            :rows="3"
            placeholder="请选择"
          />
        </el-form-item>
        <el-form-item label="语言模型选择" prop="languageModel">
          <el-select v-model="nlpConfigForm.languageModel" placeholder="请选择" style="width: 100%">
            <el-option
              v-for="option in nlpLanguageModelOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建人" prop="creator">
          <el-input v-model="nlpConfigForm.creator" placeholder="请输入创建人" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddNLPConfigDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmAddNLPConfig">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据接入故障处理弹窗 -->
    <el-dialog
      v-model="showFaultHandlingDialog"
      title="数据接入故障处理"
      width="900px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      class="fault-handling-dialog"
    >
      <div class="fault-handling-content">
        <!-- 左侧导航菜单 -->
        <div class="fault-nav-menu">
          <div
            :class="['nav-item', { active: faultHandlingActiveTab === 'prevention' }]"
            @click="switchFaultHandlingTab('prevention')"
          >
            故障处理预防规则
          </div>
          <div
            :class="['nav-item', { active: faultHandlingActiveTab === 'config' }]"
            @click="switchFaultHandlingTab('config')"
          >
            故障处理规则设置
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="fault-content-area" v-loading="faultHandlingLoading">
          <!-- 故障处理预防规则页面 -->
          <div v-if="faultHandlingActiveTab === 'prevention'" class="prevention-rules">
            <div class="prevention-rules-header">
              <h3>故障处理预防规则</h3>
              <el-button type="primary" @click="openAddPreventionRuleDialog">
                新增
              </el-button>
            </div>
            <el-table :data="faultPreventionRules" style="width: 100%">
              <el-table-column prop="name" label="规则名称" width="120" />
              <el-table-column prop="triggerCondition" label="触发条件" width="180" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-switch
                    v-if="row.status !== ''"
                    :model-value="row.status === '启用'"
                    @change="(value) => toggleRuleStatus(row.id, value)"
                  />
                  <span v-else class="status-empty">-</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button
                    type="text"
                    class="edit-btn"
                    @click="editRule(row.id)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    class="delete-btn"
                    @click="deleteRule(row.id)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 故障规则配置页面 -->
          <div v-if="faultHandlingActiveTab === 'config'" class="rule-config">
            <div class="config-section">
              <h4>故障规则配置</h4>

              <!-- 触发条件 -->
              <div class="form-group">
                <label class="form-label">触发条件</label>
                <div class="checkbox-group">
                  <el-checkbox v-model="faultRuleConfigForm.triggerCondition.continuousFailure">
                    连续3次连接失败
                  </el-checkbox>
                  <el-checkbox v-model="faultRuleConfigForm.triggerCondition.errorCodes">
                    错误码包含 ['ECONNREFUSED','ETIMEDOUT']
                  </el-checkbox>
                </div>
              </div>

              <!-- 执行动作 -->
              <div class="form-group">
                <label class="form-label">执行动作</label>
                <div class="radio-group">
                  <el-radio-group v-model="faultRuleConfigForm.executeAction">
                    <el-radio value="autoRetry">
                      <span>自动重试(回调: </span>
                      <el-input-number
                        v-model="faultRuleConfigForm.autoRetryConfig.interval"
                        :min="1"
                        :max="300"
                        size="small"
                        style="width: 80px; margin: 0 4px;"
                      />
                      <span>秒 次数: </span>
                      <el-input-number
                        v-model="faultRuleConfigForm.autoRetryConfig.maxTimes"
                        :min="1"
                        :max="10"
                        size="small"
                        style="width: 80px; margin: 0 4px;"
                      />
                      <span>)</span>
                    </el-radio>
                    <el-radio value="notify">
                      通知负责人：
                      <el-select
                        v-model="faultRuleConfigForm.notifyConfig.responsible"
                        placeholder="请选择"
                        style="width: 120px; margin-left: 8px;"
                      >
                        <el-option label="请选择" value="请选择" />
                        <el-option label="管理员" value="admin" />
                        <el-option label="运维人员" value="ops" />
                      </el-select>
                    </el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showFaultHandlingDialog = false">取消</el-button>
          <el-button
            v-if="faultHandlingActiveTab === 'config'"
            type="primary"
            @click="submitFaultRuleConfig"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增故障处理预防规则弹窗 -->
    <el-dialog
      v-model="showAddPreventionRuleDialog"
      title="故障处理预防规则"
      width="600px"
      :close-on-click-modal="false"
    >
      <template #header>
        <div class="dialog-header">
          <span>故障处理预防规则</span>
        </div>
      </template>

      <el-form
        ref="preventionRuleFormRef"
        :model="preventionRuleForm"
        :rules="preventionRuleRules"
        label-width="140px"
        class="prevention-rule-form"
      >
        <el-form-item label="规则名称" prop="name" required>
          <el-input
            v-model="preventionRuleForm.name"
            placeholder="请输入"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="触发条件" prop="triggerCondition" required>
          <el-input
            v-model="preventionRuleForm.triggerCondition"
            placeholder="请输入"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="处理/修复/预防步骤" prop="actionSteps" required>
          <el-input
            v-model="preventionRuleForm.actionSteps"
            type="textarea"
            :rows="6"
            placeholder="请选择"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-switch
            v-model="preventionRuleForm.status"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAddPreventionRule">取消</el-button>
          <el-button type="primary" @click="confirmAddPreventionRule">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<route>
{
  meta: {
    title: '数据接入'
  }
}
</route>

<style scoped lang="scss">
.no-data-tip {
  margin: 16px 0;
}

.auto-detect-section {
  .detect-tip {
    margin-bottom: 12px;
    font-weight: 500;
    color: #606266;
  }

  .data-source-list {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 12px;
    background-color: #fafafa;

    .data-source-item {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.ledger-access {
  height: 100%;
  display: flex;
  flex-direction: column;

  .warning-tips {
    background: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 4px;
    padding: 12px 16px;
    margin-bottom: 16px;
    position: relative;
    font-size: 14px;
    line-height: 1.6;

    .tip-item {
      margin-bottom: 8px;
      display: flex;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      i {
        color: #faad14;
        margin-right: 8px;
        margin-top: 2px;
        flex-shrink: 0;
      }
    }

    .close-btn {
      position: absolute;
      top: 8px;
      right: 12px;
      font-size: 18px;
      color: #999;
      padding: 0;

      &:hover {
        color: #666;
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 16px;
    min-height: 0;

    .left-sidebar {
      width: 200px;
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      flex-shrink: 0;

      .sidebar-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        border-bottom: 1px solid #e4e7ed;
        background: #f8f9fa;

        span {
          font-weight: 500;
          color: #303133;

          &.active {
            color: #409eff;
          }
        }

        .el-button {
          font-size: 12px;
          padding: 4px 8px;
        }
      }

      .group-list {
        padding: 8px 0;

        .group-item {
          padding: 8px 16px;
          cursor: pointer;
          font-size: 14px;

          &:hover {
            background: #f5f7fa;
          }

          &.active {
            background: #ecf5ff;
            color: #409eff;
          }
        }
      }
    }

    .right-content {
      flex: 1;
      min-width: 0;

      .top-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }

      .search {
        margin-bottom: 16px;
      }

      .progress-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        .el-progress {
          flex: 1;
        }

        .progress-text {
          font-size: 12px;
          color: #666;
          white-space: nowrap;

          .speed {
            color: #409eff;
            margin-left: 4px;
          }
        }
      }
    }
  }

  // 响应式适配
  @media (max-width: 1200px) {
    .main-content {
      .left-sidebar {
        width: 160px;
      }

      .right-content .top-actions {
        .el-button {
          font-size: 12px;
          padding: 5px 8px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .main-content {
      flex-direction: column;

      .left-sidebar {
        width: 100%;

        .sidebar-header {
          span {
            font-size: 12px;
            padding: 8px 12px;
          }
        }
      }
    }
  }
}

/* 步骤指示器样式 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .step-number {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #d9d9d9;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-bottom: 8px;
    }

    .step-title {
      font-size: 14px;
      color: #666;
    }

    &.active {
      .step-number {
        background: #409eff;
      }

      .step-title {
        color: #409eff;
        font-weight: 500;
      }
    }

    &.completed {
      .step-number {
        background: #67c23a;
      }

      .step-title {
        color: #67c23a;
      }
    }
  }

  .step-line {
    width: 100px;
    height: 1px;
    background: #d9d9d9;
    margin: 0 20px;
    margin-bottom: 24px;
  }
}

/* 步骤内容样式 */
.step-content {
  min-height: 300px;

  .auto-detect-section {
    .detect-tip {
      color: #666;
      margin-bottom: 12px;
      font-size: 14px;
    }

    .data-source-list {
      background: #f5f7fa;
      padding: 16px;
      border-radius: 4px;

      .data-source-item {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .order-list {
    .order-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .order-number {
        font-weight: 500;
        margin-right: 8px;
      }

      .order-name {
        flex: 1;
        color: #666;
      }

      .order-actions {
        display: flex;
        gap: 4px;

        .el-button {
          padding: 4px 8px;
          font-size: 16px;
          line-height: 1;
        }
      }
    }
  }
}

/* 稳定性检测弹窗样式 */
.stability-test-content {
  text-align: center;
  padding: 40px 20px;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;

    .el-icon {
      font-size: 32px;
      color: #409eff;
    }

    span {
      color: #666;
      font-size: 14px;
    }
  }

  .result-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;

    .success-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: #f0f9ff;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .result-text {
      color: #67c23a;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 日志清理规则弹窗样式 */
.clean-range-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .el-checkbox {
    margin-right: 0;

    .el-checkbox__label {
      color: #606266;
      font-size: 14px;
    }
  }
}

/* 缓存查看弹窗样式 */
.cache-search-area {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.cache-table-container {
  .el-table {
    .el-table__header {
      background-color: #f5f7fa;

      th {
        background-color: #f5f7fa !important;
        color: #606266;
        font-weight: 600;
      }
    }

    .el-table__body {
      tr {
        &:hover {
          background-color: #f5f7fa;
        }
      }

      td {
        padding: 12px 0;
        border-bottom: 1px solid #ebeef5;
      }
    }
  }
}

/* 分组管理样式 */
.group-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }

  &.active {
    background-color: #e6f7ff;
    color: #1890ff;
  }

  .group-name {
    flex: 1;
    user-select: none;
  }

  .delete-btn {
    opacity: 0;
    transition: opacity 0.2s;
    margin-left: 8px;
    font-size: 16px;
    font-weight: bold;
    padding: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

  &:hover .delete-btn {
    opacity: 1;
  }
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .active {
    font-weight: 600;
    color: #1890ff;
  }
}

.expired-text {
  color: #67c23a;
  font-weight: 500;
}

/* 认证配置弹窗样式 */
.expire-wakeup-methods {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .el-checkbox {
    margin-right: 0;

    .el-checkbox__label {
      color: #606266;
      font-size: 14px;
    }
  }
}

/* 认证方式单选框样式 */
.el-radio-group {
  .el-radio {
    margin-right: 24px;

    .el-radio__label {
      color: #606266;
      font-size: 14px;
    }
  }
}

/* 日志弹窗样式 */
.log-search-area {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;

  .el-form {
    margin-bottom: 0;
  }

  .el-form-item {
    margin-bottom: 0;
    margin-right: 16px;
  }
}

.log-table-area {
  .el-table {
    .el-table__header {
      background-color: #409eff;

      th {
        background-color: #409eff !important;
        color: #fff;
        font-weight: 600;
        text-align: center;
      }
    }

    .el-table__body {
      tr {
        &:hover {
          background-color: #f5f7fa;
        }
      }

      td {
        padding: 12px 0;
        border-bottom: 1px solid #ebeef5;
        text-align: center;
      }
    }
  }
}

/* 数据订阅弹窗样式 */
.subscription-dialog-content {
  .subscription-form-area {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .trigger-condition-area {
      .condition-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .condition-label {
          font-weight: 500;
          margin-right: 12px;
          min-width: 80px;
        }

        .condition-hint {
          color: #909399;
          font-size: 12px;
          margin-left: 8px;
        }

        .el-checkbox-group {
          margin-left: 12px;
          margin-right: 12px;
        }

        .el-checkbox {
          margin-right: 16px;
        }
      }
    }

    .notification-methods {
      .el-checkbox {
        margin-right: 24px;
      }
    }

    .add-subscription-btn {
      text-align: center;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #e4e7ed;
    }
  }

  .subscription-table-area {
    .el-table {
      .el-table__header {
        background-color: #409eff;

        th {
          background-color: #409eff !important;
          color: #fff;
          font-weight: 600;
          text-align: center;
        }
      }

      .el-table__body {
        tr {
          &:hover {
            background-color: #f5f7fa;
          }
        }

        td {
          padding: 8px 0;
          border-bottom: 1px solid #ebeef5;
          text-align: center;
        }
      }
    }

    .trigger-condition-display {
      text-align: left;
      font-size: 12px;
      line-height: 1.4;

      div {
        margin-bottom: 2px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

/* 过期时间样式 */
.expired-text {
  color: #f56c6c;
}

/* 模型管理弹窗样式 */
.model-management-content {
  display: flex;
  height: 600px;

  .model-nav-menu {
    width: 200px;
    border-right: 1px solid #e4e7ed;
    padding-right: 0;

    .nav-item {
      padding: 12px 16px;
      cursor: pointer;
      color: #606266;
      font-size: 14px;
      border-bottom: 1px solid #f0f0f0;
      transition: all 0.3s;

      &:hover {
        background-color: #f5f7fa;
        color: #409eff;
      }

      &.active {
        background-color: #409eff;
        color: #ffffff;
        font-weight: 500;
      }
    }
  }

  .model-content-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;

    .model-tab-content {
      .tab-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }
      }

      .el-table {
        .el-tag {
          font-size: 12px;
        }
      }
    }
  }

  .upload-success {
    margin-top: 8px;
    color: #67c23a;
    font-size: 12px;
  }
}

/* 模型分析弹窗样式 */
.model-analysis-content {
  .analysis-title {
    margin-bottom: 20px;
    text-align: center;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .analysis-metrics {
    margin-bottom: 30px;

    .metric-card {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .metric-label {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 8px;
      }

      .metric-value {
        font-size: 24px;
        font-weight: 600;
        color: #409eff;
      }
    }
  }

  .analysis-chart {
    .chart-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 20px;
      text-align: center;
    }

    .chart-container {
      background: #ffffff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 20px;

      .chart-bars {
        display: flex;
        justify-content: space-around;
        align-items: flex-end;
        height: 200px;

        .chart-bar {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 80px;

          .bar-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 10px;
          }

          .bar-container {
            width: 40px;
            height: 150px;
            background: #f5f7fa;
            border-radius: 4px;
            position: relative;
            display: flex;
            align-items: flex-end;

            .bar-fill {
              width: 100%;
              border-radius: 4px;
              transition: height 0.8s ease;

              &.accuracy-bar {
                background: linear-gradient(to top, #409eff, #66b1ff);
              }

              &.recall-bar {
                background: linear-gradient(to top, #67c23a, #85ce61);
              }

              &.f1-bar {
                background: linear-gradient(to top, #e6a23c, #ebb563);
              }
            }
          }

          .bar-value {
            font-size: 12px;
            font-weight: 600;
            color: #303133;
            margin-top: 8px;
          }
        }
      }
    }
  }
}

/* 深度学习模型数据分析弹窗样式 */
.dl-model-analysis-content {
  .chart-container {
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 16px;
    height: 350px;

    .chart-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      text-align: center;
    }

    .chart-area {
      height: calc(100% - 40px);
      position: relative;
    }

    .chart-legend {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;
      gap: 20px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 50%;

          &.accuracy-color {
            background: #409eff;
          }

          &.recall-color {
            background: #67c23a;
          }

          &.train-loss-color {
            background: #e6a23c;
          }

          &.validation-loss-color {
            background: #f56c6c;
          }
        }

        .legend-text {
          font-size: 12px;
          color: #606266;
        }
      }
    }

    .simple-line-chart {
      display: flex;
      height: calc(100% - 60px);

      .chart-y-axis {
        width: 40px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 10px 0;

        .y-label {
          font-size: 10px;
          color: #909399;
          text-align: right;
          line-height: 1;
        }
      }

      .chart-content {
        flex: 1;
        position: relative;

        .line-chart-svg {
          border: 1px solid #e4e7ed;
          border-radius: 4px;
        }

        .chart-x-axis {
          display: flex;
          justify-content: space-between;
          padding: 8px 20px 0;

          .x-label {
            font-size: 10px;
            color: #909399;
            text-align: center;
          }
        }
      }
    }
  }

  .chart-tooltip {
    position: fixed;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 9999;
    pointer-events: none;

    .tooltip-content {
      .tooltip-title {
        font-weight: 600;
        margin-bottom: 4px;
      }

      .tooltip-item {
        display: flex;
        gap: 8px;

        .tooltip-label {
          color: #ccc;
        }

        .tooltip-value {
          font-weight: 600;
        }
      }
    }
  }
}

/* 故障处理弹窗样式 */
.fault-handling-content {
  display: flex;
  height: 500px;

  .fault-nav-menu {
    width: 200px;
    border-right: 1px solid #e4e7ed;
    padding-right: 0;

    .nav-item {
      padding: 12px 16px;
      cursor: pointer;
      color: #606266;
      font-size: 14px;
      border-bottom: 1px solid #f0f0f0;
      transition: all 0.3s;

      &:hover {
        background-color: #f5f7fa;
        color: #409eff;
      }

      &.active {
        background-color: #409eff;
        color: #ffffff;
        font-weight: 500;
      }
    }
  }

  .fault-content-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }
}

/* 预防规则页面样式 */
.prevention-rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }
}

/* 新增预防规则弹窗样式 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .retention-time {
    color: #606266;
    font-size: 14px;
    font-weight: normal;
  }
}

.prevention-rule-form {
  .el-form-item {
    margin-bottom: 24px;
  }

  .el-form-item__label {
    color: #303133;
    font-weight: 500;
  }

  .el-input__inner,
  .el-textarea__inner {
    border-radius: 4px;
  }

  .el-textarea {
    .el-textarea__inner {
      min-height: 120px;
      resize: vertical;
    }
  }
}

</style>