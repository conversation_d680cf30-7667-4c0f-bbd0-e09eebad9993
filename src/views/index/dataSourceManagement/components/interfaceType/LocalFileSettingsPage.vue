<!-- 本地文件设置页面 -->
<template>
  <div class="local-file-settings-page">
    <div class="page-header">
      <h2>本地文件设置</h2>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>

    <div class="settings-content">
      <!-- 本地文件路径选择 -->
      <div class="settings-section">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="150px" class="settings-form">
          <el-form-item label="本地文件路径选择" prop="filePath" required>
            <div class="file-path-input">
              <el-input v-model="form.filePath" placeholder="请输入（选择后的文件路径将显示文件路径）" />
              <el-button type="default" icon="Folder" @click="handleBrowseFolder">浏览</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 本地文件读取模式设置 -->
      <div class="settings-section">
        <el-form :model="form" :rules="rules" ref="formRef2" label-width="150px" class="settings-form">
          <el-form-item label="本地文件读取模式设置" prop="readMode" required>
            <el-select v-model="form.readMode" placeholder="请选择" style="width: 100%;">
              <el-option label="请选择" value="" />
              <el-option label="顺序读取" value="sequential" />
              <el-option label="随机读取" value="random" />
              <el-option label="内存映射" value="memory_mapped" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 本地文件编码设置 -->
      <div class="settings-section">
        <el-form :model="form" :rules="rules" ref="formRef3" label-width="150px" class="settings-form">
          <el-form-item label="本地文件编码设置" prop="encoding" required>
            <el-select v-model="form.encoding" placeholder="请选择" style="width: 100%;">
              <el-option label="请选择" value="" />
              <el-option label="UTF-8" value="utf8" />
              <el-option label="GBK" value="gbk" />
              <el-option label="ASCII" value="ascii" />
              <el-option label="ISO-8859-1" value="iso88591" />
              <el-option label="UTF-16" value="utf16" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 支持文件格式 -->
      <div class="settings-section">
        <h3>支持文件格式</h3>
        <div class="file-formats">
          <div class="format-row">
            <el-checkbox v-model="form.supportedFormats.txt">TXT</el-checkbox>
            <el-checkbox v-model="form.supportedFormats.csv">CSV</el-checkbox>
            <el-checkbox v-model="form.supportedFormats.json">JSON</el-checkbox>
            <el-checkbox v-model="form.supportedFormats.xml">XML</el-checkbox>
            <el-checkbox v-model="form.supportedFormats.pdf">PDF</el-checkbox>
          </div>
          <div class="format-row">
            <el-checkbox v-model="form.supportedFormats.docx">DOCX</el-checkbox>
            <el-checkbox v-model="form.supportedFormats.xlsx">XLSX</el-checkbox>
            <el-checkbox v-model="form.supportedFormats.pptx">PPTX</el-checkbox>
            <el-checkbox v-model="form.supportedFormats.svg">SVG</el-checkbox>
            <el-checkbox v-model="form.supportedFormats.png">PNG</el-checkbox>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="LocalFileSettingsPage">
// 表单引用
const formRef = ref()
const formRef2 = ref()
const formRef3 = ref()

// 表单验证规则
const rules = ref({
  filePath: [
    { required: true, message: '请输入本地文件路径', trigger: 'blur' }
  ],
  readMode: [
    { required: true, message: '请选择本地文件读取模式', trigger: 'change' }
  ],
  encoding: [
    { required: true, message: '请选择本地文件编码', trigger: 'change' }
  ]
})

// 表单数据
const form = ref({
  // 文件路径
  filePath: '',
  
  // 读取模式
  readMode: '',
  
  // 编码设置
  encoding: '',
  
  // 支持的文件格式
  supportedFormats: {
    txt: false,
    csv: false,
    json: false,
    xml: false,
    pdf: false,
    docx: false,
    xlsx: false,
    pptx: false,
    svg: false,
    png: false
  }
})

// 缓存键
const STORAGE_KEY = 'interfaceType_localFileSettings'

// 加载保存的数据
const loadData = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const data = JSON.parse(cached)
      Object.assign(form.value, data)
    }
  } catch (error) {
    console.error('加载本地文件设置数据失败:', error)
  }
}

// 浏览文件夹功能
const handleBrowseFolder = () => {
  // 检查浏览器是否支持 showDirectoryPicker API
  if ('showDirectoryPicker' in window) {
    // 使用现代的 File System Access API
    window.showDirectoryPicker()
      .then(directoryHandle => {
        form.value.filePath = `/${directoryHandle.name}/`
        ElMessage.success(`已选择文件夹: ${directoryHandle.name}`)
      })
      .catch(error => {
        if (error.name !== 'AbortError') {
          console.error('选择文件夹失败:', error)
          ElMessage.error('选择文件夹失败')
        }
      })
  } else {
    // 降级到传统的 webkitdirectory 方式
    const input = document.createElement('input')
    input.type = 'file'
    input.webkitdirectory = true
    input.style.display = 'none'

    input.onchange = (event) => {
      const files = event.target.files
      if (files && files.length > 0) {
        // 只获取文件夹路径，不读取文件内容
        const firstFile = files[0]
        const folderPath = firstFile.webkitRelativePath.split('/')[0]
        form.value.filePath = `/${folderPath}/`
        ElMessage.success(`已选择文件夹: ${folderPath}`)

        // 清空文件列表，避免实际上传文件
        input.value = ''
      }
    }

    document.body.appendChild(input)
    input.click()
    document.body.removeChild(input)
  }
}

// 保存数据
const handleSave = () => {
  // 验证所有表单
  Promise.all([
    formRef.value?.validate(),
    formRef2.value?.validate(),
    formRef3.value?.validate()
  ]).then(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(form.value))
      ElMessage.success('本地文件设置保存成功')
    } catch (error) {
      console.error('保存本地文件设置数据失败:', error)
      ElMessage.error('保存失败')
    }
  }).catch(() => {
    ElMessage.warning('请填写必填项')
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.local-file-settings-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .settings-content {
    flex: 1;
    overflow-y: auto;
  }

  .settings-section {
    margin-bottom: 32px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .settings-form {
      .el-form-item {
        margin-bottom: 16px;
      }
    }

    .file-path-input {
      display: flex;
      gap: 8px;

      .el-input {
        flex: 1;
      }
    }

    .file-formats {
      .format-row {
        display: flex;
        gap: 20px;
        margin-bottom: 12px;
        flex-wrap: wrap;

        .el-checkbox {
          margin-right: 0;
          min-width: 80px;
        }
      }
    }
  }
}
</style>
