<!-- FTP配置页面 -->
<template>
  <div class="ftp-config-page">
    <div class="page-header">
      <h2>FTP配置</h2>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>

    <div class="settings-content">
      <!-- FTP接口基础信息设置 -->
      <div class="settings-section">
        <h3>FTP接口基础信息设置</h3>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px" class="settings-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="服务器地址" prop="serverHost" required>
                <el-input v-model="form.serverHost" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口号" prop="port" required>
                <el-input v-model="form.port" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名" prop="username" required>
                <el-input v-model="form.username" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码" prop="password" required>
                <el-input v-model="form.password" type="password" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- FTP文件编码格式设置 -->
      <div class="settings-section">
        <h3>FTP文件编码格式设置</h3>
        <el-form :model="form" :rules="rules" ref="formRef2" label-width="120px" class="settings-form">
          <el-form-item label="文件编码" prop="fileEncoding" required>
            <el-select v-model="form.fileEncoding" placeholder="请选择" style="width: 100%;">
              <el-option label="请选择" value="" />
              <el-option label="UTF-8" value="utf8" />
              <el-option label="GBK" value="gbk" />
              <el-option label="ASCII" value="ascii" />
              <el-option label="ISO-8859-1" value="iso88591" />
            </el-select>
          </el-form-item>
          <el-form-item label="FTP文件路径" prop="filePath" required>
            <div class="file-path-input">
              <el-input v-model="form.filePath" placeholder="请输入" />
              <el-button type="default" icon="Folder" @click="handleBrowseFile">浏览</el-button>
            </div>
          </el-form-item>
          <el-form-item label="文件名模式匹配">
            <el-input v-model="form.filePattern" placeholder="请输入（例如：*.csv）" />
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="FtpConfigPage">
// 表单引用
const formRef = ref()
const formRef2 = ref()

// 表单验证规则
const rules = ref({
  serverHost: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  fileEncoding: [
    { required: true, message: '请选择文件编码', trigger: 'change' }
  ],
  filePath: [
    { required: true, message: '请输入FTP文件路径', trigger: 'blur' }
  ]
})

// 表单数据
const form = ref({
  // 基础信息
  serverHost: '',
  port: '',
  username: '',
  password: '',
  
  // 文件编码设置
  fileEncoding: '',
  filePath: '',
  filePattern: ''
})

// 缓存键
const STORAGE_KEY = 'interfaceType_ftpConfig'

// 加载保存的数据
const loadData = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const data = JSON.parse(cached)
      Object.assign(form.value, data)
    }
  } catch (error) {
    console.error('加载FTP配置数据失败:', error)
  }
}

// 浏览文件夹功能
const handleBrowseFile = () => {
  // 检查浏览器是否支持 showDirectoryPicker API
  if ('showDirectoryPicker' in window) {
    // 使用现代的 File System Access API
    window.showDirectoryPicker()
      .then(directoryHandle => {
        form.value.filePath = `/${directoryHandle.name}/`
        ElMessage.success(`已选择文件夹: ${directoryHandle.name}`)
      })
      .catch(error => {
        if (error.name !== 'AbortError') {
          console.error('选择文件夹失败:', error)
          ElMessage.error('选择文件夹失败')
        }
      })
  } else {
    // 降级到传统的 webkitdirectory 方式
    const input = document.createElement('input')
    input.type = 'file'
    input.webkitdirectory = true
    input.style.display = 'none'

    input.onchange = (event) => {
      const files = event.target.files
      if (files && files.length > 0) {
        // 只获取文件夹路径，不读取文件内容
        const firstFile = files[0]
        const folderPath = firstFile.webkitRelativePath.split('/')[0]
        form.value.filePath = `/${folderPath}/`
        ElMessage.success(`已选择文件夹: ${folderPath}`)

        // 清空文件列表，避免实际上传文件
        input.value = ''
      }
    }

    document.body.appendChild(input)
    input.click()
    document.body.removeChild(input)
  }
}

// 保存数据
const handleSave = () => {
  // 验证所有表单
  Promise.all([
    formRef.value?.validate(),
    formRef2.value?.validate()
  ]).then(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(form.value))
      ElMessage.success('FTP配置保存成功')
    } catch (error) {
      console.error('保存FTP配置数据失败:', error)
      ElMessage.error('保存失败')
    }
  }).catch(() => {
    ElMessage.warning('请填写必填项')
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.ftp-config-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .settings-content {
    flex: 1;
    overflow-y: auto;
  }

  .settings-section {
    margin-bottom: 32px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .settings-form {
      .el-form-item {
        margin-bottom: 16px;
      }
    }

    .file-path-input {
      display: flex;
      gap: 8px;

      .el-input {
        flex: 1;
      }
    }
  }
}
</style>
