<!-- HTTPS接口设置页面 -->
<template>
  <div class="https-settings-page">
    <div class="page-header">
      <h2>HTTPS设置</h2>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>

    <div class="settings-content">
      <!-- 基础信息 -->
      <div class="settings-section">
        <h3>基础信息</h3>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px" class="settings-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="名称" prop="name" required>
                <el-input v-model="form.name" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="url" prop="url" required>
                <el-input v-model="form.url" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="接口描述">
            <el-input v-model="form.description" type="textarea" placeholder="请输入" />
          </el-form-item>
        </el-form>
      </div>

      <!-- TLS/SSL证书管理 -->
      <div class="settings-section">
        <h3>TLS/SSL证书管理</h3>
        <div class="cert-settings">
          <el-checkbox v-model="form.enableCertVerification">启用证书校验</el-checkbox>
        </div>
        
        <el-form :model="form" label-width="150px" class="settings-form" style="margin-top: 16px;">
          <el-form-item label="SSL证书类型">
            <el-select v-model="form.sslCertType" placeholder="请选择" style="width: 100%;">
              <el-option label="请选择" value="" />
              <el-option label="PEM格式" value="pem" />
              <el-option label="PKCS12格式" value="pkcs12" />
              <el-option label="JKS格式" value="jks" />
            </el-select>
          </el-form-item>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户端证书 (PEM格式)">
                <div class="upload-section">
                  <el-button type="primary" @click="selectClientCertFile">选择文件</el-button>
                  <span v-if="form.clientCert" class="file-name">{{ form.clientCert }}</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证书密钥 (PEM格式)">
                <div class="upload-section">
                  <el-button type="primary" @click="selectCertKeyFile">选择文件</el-button>
                  <span v-if="form.certKey" class="file-name">{{ form.certKey }}</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- TLS版本设置 -->
      <div class="settings-section">
        <h3>TLS版本设置</h3>
        <div class="tls-versions">
          <el-checkbox v-model="form.tlsVersions.tls13">TLS 1.3</el-checkbox>
          <el-checkbox v-model="form.tlsVersions.tls12">TLS 1.2</el-checkbox>
          <el-checkbox v-model="form.tlsVersions.tls11">TLS 1.1</el-checkbox>
          <el-checkbox v-model="form.tlsVersions.tls10">TLS 1.0</el-checkbox>
          <el-checkbox v-model="form.tlsVersions.ssl30">SSL 3.0</el-checkbox>
        </div>
      </div>

      <!-- 加密套件设置 -->
      <div class="settings-section">
        <h3>加密套件设置</h3>
        <div class="cipher-suites">
          <el-checkbox v-model="form.cipherSuites.ecdheEcdsaAes256">ECDHE-ECDSA-AES256-GCM-SHA384</el-checkbox>
          <el-checkbox v-model="form.cipherSuites.ecdheRsaAes256">ECDHE-RSA-AES256-GCM-SHA384</el-checkbox>
          <el-checkbox v-model="form.cipherSuites.rsaAes256">RSA-AES256-SHA</el-checkbox>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="HttpsSettingsPage">
// 表单引用
const formRef = ref()

// 表单验证规则
const rules = ref({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入URL', trigger: 'blur' }
  ]
})

// 表单数据
const form = ref({
  // 基础信息
  name: '',
  url: '',
  description: '',
  
  // 证书管理
  enableCertVerification: false,
  sslCertType: '',
  clientCert: '',
  certKey: '',
  clientCertFile: null,
  certKeyFile: null,
  
  // TLS版本
  tlsVersions: {
    tls13: false,
    tls12: false,
    tls11: false,
    tls10: false,
    ssl30: false
  },
  
  // 加密套件
  cipherSuites: {
    ecdheEcdsaAes256: false,
    ecdheRsaAes256: false,
    rsaAes256: false
  }
})

// 缓存键
const STORAGE_KEY = 'interfaceType_httpsSettings'

// 加载保存的数据
const loadData = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const data = JSON.parse(cached)
      Object.assign(form.value, data)
    }
  } catch (error) {
    console.error('加载HTTPS设置数据失败:', error)
  }
}

// 文件选择处理
const selectClientCertFile = () => {
  // 创建一个隐藏的文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.pem,.crt,.cer'
  input.style.display = 'none'

  input.onchange = (event) => {
    const files = event.target.files
    if (files && files.length > 0) {
      const file = files[0]
      form.value.clientCertFile = file
      form.value.clientCert = file.name
      ElMessage.success(`客户端证书文件已选择: ${file.name}`)
      console.log('客户端证书文件选择:', file)
    }
  }

  document.body.appendChild(input)
  input.click()
  document.body.removeChild(input)
}

const selectCertKeyFile = () => {
  // 创建一个隐藏的文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.pem,.key'
  input.style.display = 'none'

  input.onchange = (event) => {
    const files = event.target.files
    if (files && files.length > 0) {
      const file = files[0]
      form.value.certKeyFile = file
      form.value.certKey = file.name
      ElMessage.success(`证书密钥文件已选择: ${file.name}`)
      console.log('证书密钥文件选择:', file)
    }
  }

  document.body.appendChild(input)
  input.click()
  document.body.removeChild(input)
}

// 保存数据
const handleSave = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      try {
        // 保存时不包含文件对象，只保存文件名
        const saveData = { ...form.value }
        delete saveData.clientCertFile
        delete saveData.certKeyFile

        localStorage.setItem(STORAGE_KEY, JSON.stringify(saveData))
        ElMessage.success('HTTPS设置保存成功')
      } catch (error) {
        console.error('保存HTTPS设置数据失败:', error)
        ElMessage.error('保存失败')
      }
    } else {
      ElMessage.warning('请填写必填项')
    }
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.https-settings-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .settings-content {
    flex: 1;
    overflow-y: auto;
  }

  .settings-section {
    margin-bottom: 32px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .settings-form {
      .el-form-item {
        margin-bottom: 16px;
      }
    }

    .cert-settings {
      margin-bottom: 16px;
    }

    .upload-section {
      display: flex;
      align-items: center;
      gap: 12px;

      .file-name {
        color: #67c23a;
        font-size: 14px;
      }
    }

    .tls-versions,
    .cipher-suites {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .el-checkbox {
        margin-right: 0;
      }
    }
  }
}
</style>
