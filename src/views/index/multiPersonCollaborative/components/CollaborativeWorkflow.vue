<template>
	<div class="collaborative-workflow">
		<div class="page-header">
			<h2 class="page-title">协同工作流程</h2>
			<p class="page-description">设计和管理团队协同工作流程</p>
		</div>

		<div class="content-placeholder">
			<ProcessEngine style="width: 100%; height: 100%" />
		</div>
	</div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {Connection} from '@element-plus/icons-vue'
import ProcessEngine from '../../processEngine/index.vue'
const features = ref(['工作流程设计', '任务分配管理', '流程审批', '进度跟踪'])
</script>

<style scoped lang="scss">
.collaborative-workflow {
	.page-header {
		margin-bottom: 30px;

		.page-title {
			margin: 0 0 8px 0;
			font-size: 24px;
			font-weight: 600;
			color: #303133;
		}

		.page-description {
			margin: 0;
			color: #606266;
			font-size: 14px;
		}
	}

	.content-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		min-height: 400px;
		background: #ffffff;
		border-radius: 12px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		padding: 40px;
		text-align: center;

		.placeholder-icon {
			margin-bottom: 20px;
			color: #e6a23c;
		}

		h3 {
			margin: 0 0 12px 0;
			font-size: 20px;
			color: #303133;
		}

		p {
			margin: 0 0 24px 0;
			color: #909399;
			font-size: 14px;
		}

		.feature-list {
			display: flex;
			flex-wrap: wrap;
			gap: 8px;
			justify-content: center;

			.feature-tag {
				margin: 0;
			}
		}
	}
}
</style>
