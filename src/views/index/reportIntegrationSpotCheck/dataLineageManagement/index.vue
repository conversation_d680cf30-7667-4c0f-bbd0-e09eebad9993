<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, ArrowRight, Star, StarFilled, ArrowDown, UploadFilled } from '@element-plus/icons-vue'

import { useDataLineageStorage } from './useDataLineageStorage'
import type { DataLineageRecord, DataLineagePermissionConfig, DataLineageFormData, AccessRecord, DataLineageVersion, VersionManagementForm } from './types'
import { PERMISSION_LABELS, TABLE_OPTIONS, FIELD_TYPE_OPTIONS, TRANSFORM_TYPE_OPTIONS, RESPONSIBLE_OPTIONS } from './types'
import { useUserStore } from '@/stores/useUserStore'
import xlsx from '@/plugin/exceljs'
import * as ExcelJs from 'exceljs'

// 路由实例
const router = useRouter()

// 用户store实例
const userStore = useUserStore()

// 使用数据管理 composable
const {
  records,
  filteredRecords,
  searchKeyword,
  selectedRecords,
  permissionConfigs,
  toggleFavorite,
  updatePermissionConfig,
  getPermissionConfig,
  hasPermission,
  addDataLineage,
  updateDataLineage,
  deleteDataLineage,
  getRecordById,
  saveToStorage
} = useDataLineageStorage()

// 页面状态
const loading = ref(false)
const tableHeight = ref(400)
const tableRef = ref()

// 搜索表单
const searchForm = ref({
  keyword: ''
})

const searchFormProp = ref([
  { label: '关键词', prop: 'keyword', type: 'text', placeholder: '请输入源字段、目标字段或负责人' }
])

// 权限配置弹窗
const showPermissionDialog = ref(false)
const currentPermissionRecord = ref<DataLineageRecord | null>(null)
const permissionForm = ref({
  authorizedPersons: [] as string[],
  permissions: {
    view: false,
    edit: false,
    delete: false,
    editPermission: false
  }
})

// 数据血缘详情弹窗
const showDetailDialog = ref(false)
const currentDetailRecord = ref<DataLineageRecord | null>(null)

// 访问记录弹窗
const showAccessRecordsDialog = ref(false)
const accessRecords = ref<AccessRecord[]>([])
const accessRecordsLoading = ref(false)

// 版本管理弹窗
const showVersionManagementDialog = ref(false)
const versionManagementForm = ref<VersionManagementForm>({
  selectedRecords: [],
  targetVersion: '',
  targetTime: ''
})
const versionHistory = ref<DataLineageVersion[]>([])
const versionManagementLoading = ref(false)

// 导入导出功能
const showImportDialog = ref(false)
const importLoading = ref(false)
const importFile = ref<File | null>(null)
const importProgress = ref(0)
const importResult = ref<{
  success: number
  failed: number
  duplicates: number
  errors: string[]
}>({ success: 0, failed: 0, duplicates: 0, errors: [] })
const showImportResult = ref(false)



// 访问记录分页状态
const accessRecordsCurrentPage = ref(1)
const accessRecordsPageSize = ref(10)
const accessRecordsTotalCount = ref(0)

// 访问记录分页数据
const paginatedAccessRecords = computed(() => {
  const start = (accessRecordsCurrentPage.value - 1) * accessRecordsPageSize.value
  const end = start + accessRecordsPageSize.value
  return accessRecords.value.slice(start, end)
})

// 访问记录表格列配置
const accessRecordsColumns = ref([
  {
    label: '用户',
    prop: 'userName',
    width: 100,
    align: 'center'
  },
  {
    label: '操作类型',
    prop: 'actionType',
    width: 100,
    align: 'center',
    slot: 'actionType'
  },
  {
    label: '血缘关系',
    prop: 'recordTitle',
    width: 200,
    showOverflowTooltip: true
  },
  {
    label: '操作描述',
    prop: 'actionDescription',
    minWidth: 250,
    showOverflowTooltip: true
  },
  {
    label: '访问时间',
    prop: 'accessTime',
    width: 160,
    align: 'center'
  },
  {
    label: '耗时',
    prop: 'duration',
    width: 80,
    align: 'center',
    slot: 'duration'
  },
  {
    label: 'IP地址',
    prop: 'ipAddress',
    width: 120,
    align: 'center'
  },
  {
    label: '结果',
    prop: 'result',
    width: 80,
    align: 'center',
    slot: 'result'
  }
])

// 新增/编辑血缘关系弹窗
const showLineageDialog = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const currentEditRecord = ref<DataLineageRecord | null>(null)
const lineageForm = ref<DataLineageFormData>({
  sourceTable: '',
  sourceField: '',
  sourceCategory: '',
  targetTable: '',
  targetField: '',
  targetCategory: '',
  transformType: '',
  transformLogic: '',
  dependency: '',
  responsible: '',
  remark: '',
  permissions: {
    view: true,
    edit: true,
    delete: false
  }
})

// 表单验证规则
const lineageFormRules = ref({
  sourceTable: [{ required: true, message: '请选择源表', trigger: 'change' }],
  sourceField: [{ required: true, message: '请输入源字段', trigger: 'blur' }],
  sourceCategory: [{ required: true, message: '请选择源字段类型', trigger: 'change' }],
  targetTable: [{ required: true, message: '请选择目标表', trigger: 'change' }],
  targetField: [{ required: true, message: '请输入目标字段', trigger: 'blur' }],
  targetCategory: [{ required: true, message: '请选择目标字段类型', trigger: 'change' }],
  transformType: [{ required: true, message: '请选择转换类型', trigger: 'change' }],
  responsible: [{ required: true, message: '请输入负责人', trigger: 'blur' }]
})

// 授权人员选项
const authorizedPersonOptions = ref([
  { label: '张三', value: '张三' },
  { label: '李四', value: '李四' },
  { label: '王五', value: '王五' },
  { label: '赵六', value: '赵六' },
  { label: '钱七', value: '钱七' },
  { label: '孙八', value: '孙八' },
  { label: '周九', value: '周九' },
  { label: '吴十', value: '吴十' }
])

// 表格列配置
const columns = ref([
  { prop: 'tag', label: '标签', minWidth: 80 },
  { prop: 'sourceField', label: '源字段', minWidth: 120, sortable: true },
  { prop: 'sourceCategory', label: '源分类', minWidth: 80 },
  { prop: 'targetField', label: '目标字段', minWidth: 120, sortable: true },
  { prop: 'dataVolume', label: '涉及数据量', minWidth: 120, sortable: true },
  { prop: 'responsible', label: '负责人', minWidth: 80 },
  { prop: 'dataVersion', label: '数据版本', minWidth: 150, sortable: true },
  { prop: 'remark', label: '备注', minWidth: 120 },
  { prop: 'permissions', label: '权限', minWidth: 200 },
  { prop: 'favorite', label: '收藏', minWidth: 60, sortable: true }
])

// 操作按钮配置
const buttons = [
  { label: '详情', code: 'detail' },
  { label: '编辑', type: 'primary', code: 'edit' },
  { label: '复制', type: 'info', code: 'copy' },
  { label: '分享', type: 'success', code: 'share' },
  { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' },
  { label: '编辑权限', code: 'editPermission', more: true }
]

// 排序状态
const sortField = ref('')
const sortOrder = ref<'asc' | 'desc'>('asc')

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)

// 排序后的数据
const sortedData = computed(() => {
  if (!sortField.value) {
    return filteredRecords.value
  }
  
  return [...filteredRecords.value].sort((a, b) => {
    const aValue = a[sortField.value as keyof DataLineageRecord]
    const bValue = b[sortField.value as keyof DataLineageRecord]
    
    // 处理不同类型的数据
    let comparison = 0
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      comparison = aValue.localeCompare(bValue, 'zh-CN')
    } else if (typeof aValue === 'number' && typeof bValue === 'number') {
      comparison = aValue - bValue
    } else if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
      comparison = aValue === bValue ? 0 : (aValue ? 1 : -1)
    } else {
      // 其他类型转为字符串比较
      const aStr = String(aValue || '')
      const bStr = String(bValue || '')
      comparison = aStr.localeCompare(bStr, 'zh-CN')
    }
    
    return sortOrder.value === 'asc' ? comparison : -comparison
  })
})

// 分页数据（先排序后分页）
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return sortedData.value.slice(start, end)
})

// 搜索处理
const onSearch = () => {
  // 记录查询行为
  if (searchForm.value.keyword) {
    recordUserAccess('query', undefined, {
      searchKeywords: searchForm.value.keyword
    })
  }
  
  searchKeyword.value = searchForm.value.keyword
  // 重置到第一页
  currentPage.value = 1
  ElMessage.success('搜索完成')
}

// 返回上级页面
const handleGoBack = () => {
  router.push('/reportIntegrationSpotCheck')
}

// Block 高度变化处理
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 120
}

// 搜索展开处理
const expendSearch = (_expanded: boolean) => {
  // 搜索展开/收起处理
}

// 表格操作按钮点击处理
const onTableClickButton = ({ row, btn }: { row: DataLineageRecord, btn: any }) => {
  switch (btn.code) {
    case 'detail':
      handleViewDetail(row)
      break
    case 'edit':
      handleEdit(row)
      break
    case 'copy':
      handleCopyRecord(row)
      break
    case 'share':
      handleShareRecord(row)
      break
    case 'delete':
      handleDelete(row)
      break
    case 'editPermission':
      handleEditPermission(row)
      break
  }
}

// 查看详情
const handleViewDetail = (row: DataLineageRecord) => {
  if (!hasPermission(row.id, 'view')) {
    ElMessage.warning('您没有查看此记录的权限')
    return
  }
  
  // 记录访问行为
  recordUserAccess('view', row, {
    viewedFields: ['sourceTable', 'sourceField', 'targetTable', 'targetField', 'transformType']
  })
  
  currentDetailRecord.value = row
  showDetailDialog.value = true
}

// 新增血缘关系
const handleAddLineage = () => {
  dialogMode.value = 'add'
  currentEditRecord.value = null
  // 重置表单
  lineageForm.value = {
    sourceTable: '',
    sourceField: '',
    sourceCategory: '',
    targetTable: '',
    targetField: '',
    targetCategory: '',
    transformType: '',
    transformLogic: '',
    dependency: '',
    responsible: '',
    remark: '',
    permissions: {
      view: true,
      edit: true,
      delete: false
    }
  }
  showLineageDialog.value = true
}

// 编辑
const handleEdit = (row: DataLineageRecord) => {
  if (!hasPermission(row.id, 'edit')) {
    ElMessage.warning('您没有编辑此记录的权限')
    return
  }
  
  // 记录访问行为
  recordUserAccess('edit', row)
  
  dialogMode.value = 'edit'
  currentEditRecord.value = row
  
  // 填充表单数据
  lineageForm.value = {
    sourceTable: row.sourceTable,
    sourceField: row.sourceField,
    sourceCategory: row.sourceCategory,
    targetTable: row.targetTable,
    targetField: row.targetField,
    targetCategory: row.targetCategory,
    transformType: row.transformType,
    transformLogic: row.transformLogic,
    dependency: row.dependency,
    responsible: row.responsible,
    remark: row.remark,
    permissions: { ...row.permissions }
  }
  
  showLineageDialog.value = true
}

// 复制记录
const handleCopyRecord = (row: DataLineageRecord) => {
  if (!hasPermission(row.id, 'view')) {
    ElMessage.warning('您没有查看此记录的权限')
    return
  }
  
  try {
    // 创建复制的数据对象
    const copyData = {
      标签: row.tag.text,
      源表名: row.sourceTable,
      源字段: row.sourceField,
      源数据类型: row.sourceCategory,
      目标表名: row.targetTable,
      目标字段: row.targetField,
      目标数据类型: row.targetCategory,
      转换类型: row.transformType,
      转换逻辑: row.transformLogic || '无',
      依赖关系: row.dependency || '无依赖',
      数据量: row.dataVolume,
      负责人: row.responsible,
      数据版本: row.dataVersion,
      备注: row.remark || '无'
    }
    
    // 格式化为文本
    const copyText = Object.entries(copyData)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n')
    
    // 复制到剪贴板
    if (navigator.clipboard && window.isSecureContext) {
      // 使用现代API
      navigator.clipboard.writeText(copyText).then(() => {
        ElMessage.success('数据血缘关系信息已复制到剪贴板')
        // 记录访问行为
        recordUserAccess('view', row, {
          action: 'copy',
          viewedFields: Object.keys(copyData)
        })
      }).catch(() => {
        ElMessage.error('复制失败，请手动复制')
      })
    } else {
      // 降级方案：使用传统方法
      const textArea = document.createElement('textarea')
      textArea.value = copyText
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        const successful = document.execCommand('copy')
        if (successful) {
          ElMessage.success('数据血缘关系信息已复制到剪贴板')
          // 记录访问行为
          recordUserAccess('view', row, {
            action: 'copy',
            viewedFields: Object.keys(copyData)
          })
        } else {
          ElMessage.error('复制失败，请手动复制')
        }
      } catch (err) {
        ElMessage.error('复制失败，请手动复制')
      } finally {
        document.body.removeChild(textArea)
      }
    }
  } catch (error) {
    console.error('复制记录时发生错误:', error)
    ElMessage.error('复制失败，请重试')
  }
}

// 分享记录
const handleShareRecord = (row: DataLineageRecord) => {
  if (!hasPermission(row.id, 'view')) {
    ElMessage.warning('您没有查看此记录的权限')
    return
  }
  
  try {
    // 生成分享链接（模拟）
    const baseUrl = window.location.origin + window.location.pathname
    const shareUrl = `${baseUrl}?recordId=${row.id}&action=view`
    
    // 创建分享内容
    const shareTitle = `数据血缘关系：${row.sourceField} → ${row.targetField}`
    const shareText = `查看数据血缘关系详情：\n` +
      `源字段：${row.sourceTable}.${row.sourceField}\n` +
      `目标字段：${row.targetTable}.${row.targetField}\n` +
      `转换类型：${row.transformType}\n` +
      `负责人：${row.responsible}\n` +
      `链接：${shareUrl}`
    
    // 检查是否支持Web Share API
    if (navigator.share && window.isSecureContext) {
      navigator.share({
        title: shareTitle,
        text: shareText,
        url: shareUrl
      }).then(() => {
        ElMessage.success('分享成功')
        // 记录访问行为
        recordUserAccess('view', row, {
          action: 'share',
          shareUrl: shareUrl
        })
      }).catch((error) => {
        if (error.name !== 'AbortError') {
          console.error('分享失败:', error)
          // 降级到复制链接
          copyShareLink(shareText)
        }
      })
    } else {
      // 降级方案：复制分享内容到剪贴板
      copyShareLink(shareText)
    }
  } catch (error) {
    console.error('分享记录时发生错误:', error)
    ElMessage.error('分享失败，请重试')
  }
}

// 复制分享链接的辅助函数
const copyShareLink = (shareText: string) => {
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(shareText).then(() => {
      ElMessage.success('分享内容已复制到剪贴板，您可以粘贴到其他地方分享')
    }).catch(() => {
      ElMessage.error('复制失败，请手动复制分享内容')
    })
  } else {
    // 传统方法
    const textArea = document.createElement('textarea')
    textArea.value = shareText
    textArea.style.position = 'fixed'
    textArea.style.opacity = '0'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    
    try {
      const successful = document.execCommand('copy')
      if (successful) {
        ElMessage.success('分享内容已复制到剪贴板，您可以粘贴到其他地方分享')
      } else {
        ElMessage.error('复制失败，请手动复制分享内容')
      }
    } catch (err) {
      ElMessage.error('复制失败，请手动复制分享内容')
    } finally {
      document.body.removeChild(textArea)
    }
  }
}

// 删除
const handleDelete = (row: DataLineageRecord) => {
  if (!hasPermission(row.id, 'delete')) {
    ElMessage.warning('您没有删除此记录的权限')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除数据血缘关系 "${row.sourceTable} -> ${row.targetTable}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      try {
        // 记录访问行为
        recordUserAccess('delete', row)
        
        deleteDataLineage(row.id)
        ElMessage.success('删除成功')
        saveToStorage()
      } catch (error) {
        ElMessage.error('删除失败')
        console.error('删除失败:', error)
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 确认提交血缘关系表单
const handleConfirmLineage = async () => {
  try {
    // 手动验证必填项
    if (!lineageForm.value.sourceTable) {
      ElMessage.error('请选择源表')
      return
    }
    if (!lineageForm.value.sourceField) {
      ElMessage.error('请输入源字段')
      return
    }
    if (!lineageForm.value.sourceCategory) {
      ElMessage.error('请选择源字段类型')
      return
    }
    if (!lineageForm.value.targetTable) {
      ElMessage.error('请选择目标表')
      return
    }
    if (!lineageForm.value.targetField) {
      ElMessage.error('请输入目标字段')
      return
    }
    if (!lineageForm.value.targetCategory) {
      ElMessage.error('请选择目标字段类型')
      return
    }
    if (!lineageForm.value.transformType) {
      ElMessage.error('请选择转换类型')
      return
    }
    if (!lineageForm.value.responsible) {
      ElMessage.error('请输入负责人')
      return
    }

    if (dialogMode.value === 'add') {
      // 新增
      const newId = addDataLineage(lineageForm.value)
      if (newId) {
        ElMessage.success('新增血缘关系成功')
        showLineageDialog.value = false
      } else {
        ElMessage.error('新增失败')
      }
    } else {
      // 编辑
      if (!currentEditRecord.value) {
        ElMessage.error('编辑数据异常')
        return
      }
      const success = updateDataLineage(currentEditRecord.value.id, lineageForm.value)
      if (success) {
        ElMessage.success('编辑血缘关系成功')
        showLineageDialog.value = false
      } else {
        ElMessage.error('编辑失败')
      }
    }
  } catch (error) {
    console.error('提交表单时发生错误:', error)
    ElMessage.error('操作失败，请重试')
  }
}

// 取消血缘关系表单
const handleCancelLineage = () => {
  showLineageDialog.value = false
  currentEditRecord.value = null
}

// 编辑权限
const handleEditPermission = (row: DataLineageRecord) => {
  if (!hasPermission(row.id, 'editPermission')) {
    ElMessage.warning('您没有编辑权限的权限')
    return
  }

  currentPermissionRecord.value = row
  
  // 加载现有权限配置
  const existingConfig = getPermissionConfig(row.id)
  if (existingConfig) {
    permissionForm.value.authorizedPersons = [...existingConfig.authorizedPersons]
    permissionForm.value.permissions = { ...existingConfig.permissions }
  } else {
    permissionForm.value.authorizedPersons = []
    permissionForm.value.permissions = { ...row.permissions }
  }
  
  showPermissionDialog.value = true
}

// 确认权限配置
const handleConfirmPermission = () => {
  if (!currentPermissionRecord.value) return

  if (permissionForm.value.authorizedPersons.length === 0) {
    ElMessage.warning('请至少选择一个授权人员')
    return
  }

  const config: DataLineagePermissionConfig = {
    recordId: currentPermissionRecord.value.id,
    permissions: { ...permissionForm.value.permissions },
    authorizedPersons: [...permissionForm.value.authorizedPersons],
    updateTime: new Date().toISOString()
  }

  updatePermissionConfig(config)
  
  ElMessage.success('权限配置保存成功')
  showPermissionDialog.value = false
}

// 取消权限配置
const handleCancelPermission = () => {
  showPermissionDialog.value = false
  currentPermissionRecord.value = null
}

// 切换收藏状态
const handleToggleFavorite = (row: DataLineageRecord) => {
  toggleFavorite(row.id)
  ElMessage.success(row.isFavorite ? '已取消收藏' : '已收藏')
}

// 表格选择变化
const handleSelectionChange = (selection: DataLineageRecord[]) => {
  selectedRecords.value = selection
}

// 排序变化处理
const handleSortChange = ({ prop, order }: { prop: string, order: string | null }) => {
  if (order === null) {
    // 取消排序
    sortField.value = ''
    sortOrder.value = 'asc'
  } else {
    sortField.value = prop
    sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
  }
  // 排序变化时重置到第一页
  currentPage.value = 1
}

// 查看访问记录
const handleViewAccessRecords = async () => {
  showAccessRecordsDialog.value = true
  accessRecordsLoading.value = true
  
  try {
    // 模拟获取访问记录数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 生成模拟数据
    accessRecords.value = generateMockAccessRecords()
  } catch (error) {
    ElMessage.error('获取访问记录失败')
    console.error('获取访问记录失败:', error)
  } finally {
    accessRecordsLoading.value = false
  }
}

// 生成模拟访问记录数据
const generateMockAccessRecords = (): AccessRecord[] => {
  const mockData: AccessRecord[] = []
  const users = ['张三', '李四', '王五', '赵六', '钱七']
  const actionTypes: ('view' | 'query' | 'edit' | 'delete')[] = ['view', 'query', 'edit', 'delete']
  const tables = ['用户表', '订单表', '商品表', '支付表', '日志表']
  
  for (let i = 0; i < 50; i++) {
    const user = users[Math.floor(Math.random() * users.length)]
    const actionType = actionTypes[Math.floor(Math.random() * actionTypes.length)]
    const sourceTable = tables[Math.floor(Math.random() * tables.length)]
    const targetTable = tables[Math.floor(Math.random() * tables.length)]
    
    const record: AccessRecord = {
      id: `access_${i + 1}`,
      userId: `user_${Math.floor(Math.random() * 1000)}`,
      userName: user,
      recordId: `record_${Math.floor(Math.random() * 100)}`,
      recordTitle: `${sourceTable} -> ${targetTable}`,
      actionType,
      actionDescription: getActionDescription(actionType, sourceTable, targetTable),
      sourceTable,
      targetTable,
      accessTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().replace('T', ' ').slice(0, 19),
      duration: Math.floor(Math.random() * 300) + 10,
      ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      details: {
        searchKeywords: actionType === 'query' ? '用户数据,订单信息' : undefined,
        viewedFields: actionType === 'view' ? ['id', 'name', 'create_time'] : undefined,
        operationResult: Math.random() > 0.1 ? 'success' : 'failed',
        errorMessage: Math.random() > 0.9 ? '权限不足' : undefined
      }
    }
    
    mockData.push(record)
  }
  
  return mockData.sort((a, b) => new Date(b.accessTime).getTime() - new Date(a.accessTime).getTime())
}

// 获取操作描述
const getActionDescription = (actionType: string, sourceTable: string, targetTable: string): string => {
  switch (actionType) {
    case 'view':
      return `查看了数据血缘关系：${sourceTable} -> ${targetTable}`
    case 'query':
      return `查询了数据血缘关系：${sourceTable} -> ${targetTable}`
    case 'edit':
      return `编辑了数据血缘关系：${sourceTable} -> ${targetTable}`
    case 'delete':
      return `删除了数据血缘关系：${sourceTable} -> ${targetTable}`
    default:
      return `操作了数据血缘关系：${sourceTable} -> ${targetTable}`
  }
}

// 关闭访问记录弹窗
const handleCloseAccessRecords = () => {
  showAccessRecordsDialog.value = false
  accessRecords.value = []
}



// 获取操作类型标签类型
const getActionTypeTagType = (actionType: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
  switch (actionType) {
    case 'view':
      return 'primary'
    case 'query':
      return 'success'
    case 'edit':
      return 'warning'
    case 'delete':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取操作类型标签文本
const getActionTypeLabel = (actionType: string): string => {
  switch (actionType) {
    case 'view':
      return '查看'
    case 'query':
      return '查询'
    case 'edit':
      return '编辑'
    case 'delete':
      return '删除'
    default:
      return '未知'
  }
}

// 记录用户访问行为
const recordUserAccess = (actionType: 'view' | 'query' | 'edit' | 'delete', record?: DataLineageRecord, details?: any) => {
  const currentUser = userStore.getUserInfo || JSON.parse(localStorage.getItem('currentUserInfo') || '{}')
  const userName = currentUser?.name || currentUser?.userName || '未知用户'
  const userId = currentUser?.id || currentUser?.userId || 'unknown'
  
  const accessRecord: AccessRecord = {
    id: `access_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    userId,
    userName,
    recordId: record?.id || 'unknown',
    recordTitle: record ? `${record.sourceTable} -> ${record.targetTable}` : '系统操作',
    actionType,
    actionDescription: getActionDescription(actionType, record?.sourceTable || '', record?.targetTable || ''),
    sourceTable: record?.sourceTable || '',
    targetTable: record?.targetTable || '',
    accessTime: new Date().toISOString().replace('T', ' ').slice(0, 19),
    duration: Math.floor(Math.random() * 30) + 1, // 模拟耗时
    ipAddress: '127.0.0.1', // 在实际项目中应该获取真实IP
    userAgent: navigator.userAgent,
    details: {
      searchKeywords: details?.searchKeywords,
      viewedFields: details?.viewedFields,
      operationResult: 'success',
      errorMessage: details?.errorMessage
    }
  }
  
  // 将访问记录添加到列表开头（最新的在前）
  accessRecords.value.unshift(accessRecord)
  
  // 更新总数
  accessRecordsTotalCount.value = accessRecords.value.length
  
  // 在实际项目中，这里应该调用API将记录保存到后端
  console.log('记录用户访问:', accessRecord)
}

// 访问记录分页变化处理
function handleAccessRecordsPageChange(page: number) {
  accessRecordsCurrentPage.value = page
}

function handleAccessRecordsPageSizeChange(size: number) {
  accessRecordsPageSize.value = size
  accessRecordsCurrentPage.value = 1
}

// 版本管理相关方法
// 打开版本管理弹窗
function handleVersionManagement() {
  // 初始化表单数据
  versionManagementForm.value = {
    selectedRecords: [],
    targetVersion: '',
    targetTime: ''
  }
  
  // 加载上次保存的状态
  loadVersionManagementState()
  
  // 如果没有保存的状态，则加载默认的版本历史
  if (versionHistory.value.length === 0 && versionManagementForm.value.selectedRecords.length === 0) {
    loadVersionHistory()
  }
  
  showVersionManagementDialog.value = true
}

// 加载版本历史数据
function loadVersionHistory() {
  versionManagementLoading.value = true
  
  // 模拟版本历史数据
  setTimeout(() => {
    versionHistory.value = generateMockVersionHistory()
    versionManagementLoading.value = false
  }, 500)
}

// 生成模拟版本历史数据
function generateMockVersionHistory(): DataLineageVersion[] {
  if (versionManagementForm.value.selectedRecords.length === 0) {
    return []
  }
  
  const versions: DataLineageVersion[] = []
  const operators = ['张三', '李四', '王五', '赵六']
  const descriptions = [
    '初始版本创建，建立基础数据血缘关系',
    '优化转换逻辑，提升数据处理效率',
    '新增字段映射规则，完善数据转换',
    '修复数据质量问题，调整清洗规则',
    '增加数据验证规则，提高数据准确性',
    '优化依赖关系配置，减少处理时间'
  ]
  
  // 为每个选中的血缘关系生成版本历史
  versionManagementForm.value.selectedRecords.forEach((recordId, recordIndex) => {
    const record = records.value.find(r => r.id === recordId)
    if (!record) return
    
    for (let i = 0; i < 6; i++) {
      const date = new Date()
      date.setDate(date.getDate() - i * 7) // 每周一个版本
      
      versions.push({
        id: `version_${recordIndex}_${i + 1}`,
        recordId: recordId,
        version: `v1.${6 - i}.0`,
        createTime: date.toISOString().slice(0, 19).replace('T', ' '),
        description: `${record.sourceField || '未定义'} → ${record.targetField || '未定义'}: ${descriptions[i]}`,
        operator: operators[i % operators.length],
        config: record
      })
    }
  })
  
  // 按时间排序，最新的在前
  return versions.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
}

// 执行版本回溯
function handleVersionRollback() {
  if (versionManagementForm.value.selectedRecords.length === 0) {
    ElMessage.warning('请先选择要管理的数据血缘关系')
    return
  }
  
  if (!versionManagementForm.value.targetTime) {
    ElMessage.warning('请选择回溯时间')
    return
  }
  
  ElMessageBox.confirm(
    `确定要将选中的 ${versionManagementForm.value.selectedRecords.length} 个数据血缘关系回溯到指定时间点 ${versionManagementForm.value.targetTime} 吗？`,
    '确认回溯',
    {
      confirmButtonText: '确定回溯',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 模拟回溯操作
    ElMessage.success('版本回溯成功！配置已保存到全局存储')
    
    // 添加新的版本记录到历史中
    addVersionRecord()
    
    // 记录操作日志
    recordUserAccess('edit', undefined, {
      action: 'version_rollback',
      targetTime: versionManagementForm.value.targetTime,
      affectedRecords: versionManagementForm.value.selectedRecords.length,
      selectedRecords: versionManagementForm.value.selectedRecords
    })
    
    // 保存当前状态到本地存储
    saveVersionManagementState()
    
    // 关闭弹窗
    handleCloseVersionManagement()
  }).catch(() => {
    // 用户取消操作
  })
}

// 删除版本
function handleDeleteVersion(version: DataLineageVersion) {
  ElMessageBox.confirm(
    `确定要删除版本 ${version.version} 吗？删除后无法恢复。`,
    '确认删除',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'error'
    }
  ).then(() => {
    // 从版本历史中移除
    const index = versionHistory.value.findIndex(v => v.id === version.id)
    if (index > -1) {
      versionHistory.value.splice(index, 1)
      ElMessage.success('版本删除成功')
      
      // 记录操作日志
      recordUserAccess('delete', undefined, {
        action: 'version_delete',
        deletedVersion: version.version
      })
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 添加新的版本记录到历史中
function addVersionRecord() {
  const currentTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
  const currentUser = userStore.userInfo?.userName || '当前用户'
  
  // 为每个选中的血缘关系添加新版本记录
  versionManagementForm.value.selectedRecords.forEach((recordId, index) => {
    const record = records.value.find(r => r.id === recordId)
    if (!record) return
    
    const newVersion: DataLineageVersion = {
      id: `rollback_${Date.now()}_${index}`,
      recordId: recordId,
      version: `v${new Date().getFullYear()}.${String(new Date().getMonth() + 1).padStart(2, '0')}.${String(new Date().getDate()).padStart(2, '0')}`,
      createTime: currentTime,
      description: `${record.sourceField || '未定义'} → ${record.targetField || '未定义'}: 版本回溯操作，回溯到指定时间点 ${versionManagementForm.value.targetTime}`,
      operator: currentUser,
      config: record
    }
    
    // 添加到版本历史开头（最新的在前）
    versionHistory.value.unshift(newVersion)
  })
}

// 保存版本管理状态到本地存储
function saveVersionManagementState() {
  const state = {
    selectedRecords: versionManagementForm.value.selectedRecords,
    targetVersion: versionManagementForm.value.targetVersion,
    targetTime: versionManagementForm.value.targetTime,
    versionHistory: versionHistory.value,
    lastSaveTime: new Date().toISOString()
  }
  
  localStorage.setItem('versionManagementState', JSON.stringify(state))
}

// 从本地存储加载版本管理状态
function loadVersionManagementState() {
  try {
    const savedState = localStorage.getItem('versionManagementState')
    if (savedState) {
      const state = JSON.parse(savedState)
      versionManagementForm.value.selectedRecords = state.selectedRecords || []
      versionManagementForm.value.targetVersion = state.targetVersion || ''
      versionManagementForm.value.targetTime = state.targetTime || ''
      
      // 如果有保存的版本历史，则使用保存的，否则重新生成
      if (state.versionHistory && state.versionHistory.length > 0) {
        versionHistory.value = state.versionHistory
      } else if (versionManagementForm.value.selectedRecords.length > 0) {
        loadVersionHistory()
      }
    }
  } catch (error) {
    console.warn('加载版本管理状态失败:', error)
  }
}

// 处理血缘关系选择变化
function handleLineageSelectionChange() {
  // 当选择的血缘关系发生变化时，重新加载版本历史
  if (versionManagementForm.value.selectedRecords.length > 0) {
    loadVersionHistory()
  } else {
    versionHistory.value = []
  }
  // 清空当前选择的版本
  versionManagementForm.value.targetVersion = ''
}

// 移除选中的血缘关系记录
function removeSelectedRecord(recordId: string) {
  const index = versionManagementForm.value.selectedRecords.indexOf(recordId)
  if (index > -1) {
    versionManagementForm.value.selectedRecords.splice(index, 1)
    handleLineageSelectionChange()
  }
}

// 关闭版本管理弹窗
const handleCloseVersionManagement = () => {
  showVersionManagementDialog.value = false
  versionManagementForm.value = {
    selectedRecords: [],
    targetVersion: '',
    targetTime: ''
  }
  versionHistory.value = []
}

// ==================== 导入导出功能 ====================

// 下载导入模板
const handleDownloadTemplate = async () => {
  try {
    const templateData = [
      {
        '序号': 1,
        '标签文本': '核心',
        '标签颜色': '#ff4d4f',
        '源表名': 'user_info',
        '源字段': '用户ID',
        '源数据类型': 'INT',
        '目标表名': 'customer',
        '目标字段': 'USER_ID',
        '目标数据类型': 'INT',
        '转换类型': '直接映射',
        '转换逻辑': '将用户ID字段进行直接映射处理',
        '依赖关系': '无依赖',
        '数据量': '5000条',
        '负责人': '张三',
        '数据版本': '2024-01-15 10:30:00',
        '备注': '核心业务字段'
      }
    ]

    // 直接使用ExcelJS创建工作簿
    const ExcelJS = await import('exceljs')
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('数据血缘关系导入模板')
    
    // 设置表头
    const headers = Object.keys(templateData[0])
    worksheet.addRow(headers)
    
    // 添加示例数据
    templateData.forEach(row => {
      const values = headers.map(header => row[header as keyof typeof row])
      worksheet.addRow(values)
    })
    
    // 设置列宽
    worksheet.columns = [
      { width: 8 },   // 序号
      { width: 12 },  // 标签文本
      { width: 12 },  // 标签颜色
      { width: 15 },  // 源表名
      { width: 15 },  // 源字段
      { width: 12 },  // 源数据类型
      { width: 15 },  // 目标表名
      { width: 15 },  // 目标字段
      { width: 12 },  // 目标数据类型
      { width: 12 },  // 转换类型
      { width: 25 },  // 转换逻辑
      { width: 15 },  // 依赖关系
      { width: 12 },  // 数据量
      { width: 10 },  // 负责人
      { width: 20 },  // 数据版本
      { width: 20 }   // 备注
    ]
    
    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }
    headerRow.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    }
    
    // 生成文件并下载
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '数据血缘关系导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('模板下载失败:', error)
    ElMessage.error('模板下载失败，请重试')
  }
}

// 打开导入弹窗
const handleOpenImport = () => {
  showImportDialog.value = true
  importFile.value = null
  importProgress.value = 0
  importResult.value = { success: 0, failed: 0, duplicates: 0, errors: [] }
  showImportResult.value = false
}

// 文件选择处理
const handleFileChange = (file: any) => {
  console.log('before-upload事件 - 文件选择:', file)
  // Element Plus的before-upload事件传递的就是原始File对象
  importFile.value = file
  console.log('保存的文件对象:', importFile.value)
  return false // 阻止自动上传
}

// 文件列表变化处理
const handleFileListChange = (file: any, fileList: any[]) => {
  console.log('on-change事件 - 文件变化:', file, fileList)
  if (file && file.raw) {
    // Element Plus的on-change事件，文件在raw属性中
    importFile.value = file.raw
    console.log('从on-change保存的文件对象:', importFile.value)
  } else if (fileList.length > 0 && fileList[0].raw) {
    importFile.value = fileList[0].raw
    console.log('从fileList保存的文件对象:', importFile.value)
  } else {
    console.log('清空文件对象')
    importFile.value = null
  }
}



// 验证导入行数据
const validateImportRow = (row: any, rowIndex: number) => {
  const errors: string[] = []
  const requiredFields = [
    { key: '源表名', field: 'sourceTable' },
    { key: '源字段', field: 'sourceField' },
    { key: '源数据类型', field: 'sourceCategory' },
    { key: '目标表名', field: 'targetTable' },
    { key: '目标字段', field: 'targetField' },
    { key: '目标数据类型', field: 'targetCategory' },
    { key: '转换类型', field: 'transformType' },
    { key: '负责人', field: 'responsible' }
  ]

  const data: Partial<DataLineageRecord> = {}

  // 检查必填字段
  for (const field of requiredFields) {
    const value = row[field.key]
    if (!value || String(value).trim() === '') {
      errors.push(`第${rowIndex}行: ${field.key}不能为空`)
    } else {
      (data as any)[field.field] = String(value).trim()
    }
  }

  // 处理可选字段
  data.tag = {
    text: row['标签文本'] || '一般',
    color: row['标签颜色'] || '#409eff'
  }
  data.transformLogic = row['转换逻辑'] || ''
  data.dependency = row['依赖关系'] || '无依赖'
  data.dataVolume = row['数据量'] || '未知'
  data.dataVersion = row['数据版本'] || new Date().toLocaleString('zh-CN')
  data.remark = row['备注'] || ''

  return {
    isValid: errors.length === 0,
    errors,
    data: data as DataLineageRecord
  }
}

// 检查重复记录
const checkDuplicateRecord = (newRecord: DataLineageRecord) => {
  const existing = records.value.find(record => 
    record.sourceTable === newRecord.sourceTable &&
    record.sourceField === newRecord.sourceField &&
    record.targetTable === newRecord.targetTable &&
    record.targetField === newRecord.targetField
  )
  
  return {
    exists: !!existing,
    existingRecord: existing
  }
}

// 关闭导入弹窗
const handleCloseImport = () => {
  showImportDialog.value = false
  importFile.value = null
  importLoading.value = false
  importProgress.value = 0
  showImportResult.value = false
}

// 批量导出功能（未选择数据时导出全部）
const handleBatchExport = () => {
  if (records.value.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }
  
  // 如果没有选择数据，则导出全部数据
  if (selectedRecords.value.length === 0) {
    ElMessage.info('未选择数据，将导出全部数据')
    exportDataLineageRecords(records.value, '数据血缘关系_全量导出')
  } else {
    // 导出选中的数据
    exportDataLineageRecords(selectedRecords.value, '数据血缘关系_批量导出')
  }
}

// 导出数据血缘记录
const exportDataLineageRecords = async (recordsToExport: DataLineageRecord[], fileName: string) => {
  try {
    console.log('开始导出数据:', recordsToExport.length, '条记录')
    
    const exportData = recordsToExport.map((record, index) => ({
      '序号': index + 1,
      '标签文本': record.tag.text,
      '标签颜色': record.tag.color,
      '源表名': record.sourceTable,
      '源字段': record.sourceField,
      '源数据类型': record.sourceCategory,
      '目标表名': record.targetTable,
      '目标字段': record.targetField,
      '目标数据类型': record.targetCategory,
      '转换类型': record.transformType,
      '转换逻辑': record.transformLogic,
      '依赖关系': record.dependency,
      '数据量': record.dataVolume,
      '负责人': record.responsible,
      '数据版本': record.dataVersion,
      '备注': record.remark,
      '查看权限': record.permissions.view ? '是' : '否',
      '编辑权限': record.permissions.edit ? '是' : '否',
      '删除权限': record.permissions.delete ? '是' : '否',
      '权限管理': record.permissions.editPermission ? '是' : '否',
      '是否收藏': record.isFavorite ? '是' : '否',
      '创建时间': record.createTime,
      '更新时间': record.updateTime
    }))

    console.log('导出数据结构:', exportData.slice(0, 2))

    // 生成带时间戳的文件名
    const now = new Date()
    const timestamp = now.getFullYear() + 
      String(now.getMonth() + 1).padStart(2, '0') + 
      String(now.getDate()).padStart(2, '0') + '_' +
      String(now.getHours()).padStart(2, '0') + 
      String(now.getMinutes()).padStart(2, '0') + 
      String(now.getSeconds()).padStart(2, '0')
    
    const finalFileName = `${fileName}_${timestamp}.xlsx`
    console.log('导出文件名:', finalFileName)

    // 使用ExcelJS直接创建和下载文件
    const workbook = new ExcelJs.Workbook()
    workbook.creator = userStore.userInfo?.userName || 'System'
    workbook.created = new Date()
    
    const worksheet = workbook.addWorksheet('数据血缘关系')
    
    // 设置表头
    const headers = Object.keys(exportData[0] || {})
    worksheet.addRow(headers)
    
    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }
    
    // 添加数据行
    exportData.forEach(row => {
      const values = headers.map(header => row[header as keyof typeof row])
      worksheet.addRow(values)
    })
    
    // 自动调整列宽
    worksheet.columns.forEach((column: any) => {
      column.width = 15
    })
    
    // 生成文件并下载
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = finalFileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    console.log('✅ 文件下载完成')
    ElMessage.success(`导出成功！共导出 ${recordsToExport.length} 条记录`)
    
    // 记录用户访问行为
    recordUserAccess('query', undefined, {
      action: 'export',
      count: recordsToExport.length,
      fileName: `${fileName}_${timestamp}.xlsx`
    })
  } catch (error) {
    ElMessage.error(`导出失败: ${error}`)
    console.error('Export error:', error)
  }
}

// 导入数据按钮处理
const handleImportData = () => {
  showImportDialog.value = true
}

// 导入弹窗确认按钮处理
const handleImportDataConfirm = async () => {
  console.log('=== 开始执行导入 ===')
  console.log('importFile.value:', importFile.value)
  console.log('importFile.value类型:', typeof importFile.value)
  console.log('importFile.value是否为File对象:', importFile.value && (importFile.value as any) instanceof File)
  
  if (!importFile.value) {
    console.error('❌ 没有选择文件')
    console.log('当前importFile状态:', {
      value: importFile.value,
      type: typeof importFile.value,
      isFile: importFile.value && (importFile.value as any) instanceof File
    })
    ElMessage.warning('请选择要导入的文件')
    return
  }
  
  // 检查文件类型
  const fileName = importFile.value.name || ''
  console.log('文件名:', fileName)
  
  if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
    console.error('❌ 文件格式不正确:', fileName)
    ElMessage.error('请选择Excel文件（.xlsx 或 .xls格式）')
    return
  }
  
  console.log('✅ 文件验证通过，开始导入处理')

  importLoading.value = true
  importProgress.value = 0
  
  try {
    // 使用项目中的ExcelJS导入功能
    const xlsxImporter = new xlsx(importFile.value, {
      onImportBegin: () => {
        console.log('开始导入Excel文件')
      },
      onImportProgress: (progress: number) => {
        importProgress.value = Math.round(progress * 50) // 前50%用于文件读取
      },
      onImportend: () => {
        console.log('Excel文件读取完成')
      }
    })

    const sheets = await xlsxImporter.import()
    
    if (!sheets || sheets.length === 0) {
      ElMessage.warning('导入文件中没有数据')
      importLoading.value = false
      return
    }

    const sheet = sheets[0]
    const cellData = sheet.celldata || []
    
    if (cellData.length === 0) {
      ElMessage.warning('导入文件中没有数据')
      importLoading.value = false
      return
    }

    console.log('Excel数据结构:', cellData.slice(0, 10)) // 查看前10个单元格的结构
    
    // 将celldata转换为行列结构
    const rowsData: any[][] = []
    let maxRow = 0
    let maxCol = 0
    
    // 找出最大行列数
    cellData.forEach((cell: any) => {
      if (cell.r > maxRow) maxRow = cell.r
      if (cell.c > maxCol) maxCol = cell.c
    })
    
    console.log('最大行数:', maxRow + 1, '最大列数:', maxCol + 1)
    
    // 初始化行数组
    for (let i = 0; i <= maxRow; i++) {
      rowsData[i] = new Array(maxCol + 1).fill('')
    }
    
    // 填充数据
    cellData.forEach((cell: any) => {
      if (cell.r >= 0 && cell.c >= 0) {
        rowsData[cell.r][cell.c] = cell.v || ''
      }
    })
    
    console.log('转换后的行数据:', rowsData.slice(0, 3)) // 查看前3行
    
    if (rowsData.length <= 1) {
      ElMessage.warning('导入文件中没有数据行')
      importLoading.value = false
      return
    }

    // 获取表头行（第一行）
    const headerRow = rowsData[0] || []
    const headers = headerRow.map((cell: any) => String(cell || ''))
    
    let successCount = 0
    let failedCount = 0
    let duplicateCount = 0
    const errors: string[] = []
    const duplicateRecords: string[] = []

    // 从第二行开始处理数据
    for (let i = 1; i < rowsData.length; i++) {
      const rowData = rowsData[i] || []
      const currentProgress = 50 + Math.round(((i) / (rowsData.length - 1)) * 50) // 后50%用于数据处理
      importProgress.value = currentProgress
      
      try {
        // 将行数据转换为对象
        const rowObject: any = {}
        headers.forEach((header: string, index: number) => {
          const cellValue = String(rowData[index] || '').trim()
          if (header) {
            rowObject[header] = cellValue
          }
        })
        
        console.log(`第${i + 1}行数据:`, rowObject)

        // 数据验证
        const validationResult = validateImportRow(rowObject, i + 1) // i+1因为从第二行开始
        if (!validationResult.isValid) {
          errors.push(...validationResult.errors)
          failedCount++
          continue
        }

        // 检查重复数据
        const isDuplicate = checkDuplicateRecord(validationResult.data)
        if (isDuplicate.exists) {
          duplicateRecords.push(`第${i + 1}行: ${validationResult.data.sourceField} → ${validationResult.data.targetField} (与现有记录重复)`)
          duplicateCount++
          continue
        }

        // 添加记录
        const newRecord: DataLineageRecord = {
          ...validationResult.data,
          id: `lineage_import_${Date.now()}_${i}`,
          sequence: records.value.length + successCount + 1,
          permissions: {
            view: true,
            edit: true,
            delete: true,
            editPermission: true
          },
          isFavorite: false,
          createTime: new Date().toLocaleString('zh-CN'),
          updateTime: new Date().toLocaleString('zh-CN')
        }

        // 添加到记录列表
        records.value.push(newRecord)
        successCount++
        
        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 10))
        
      } catch (error) {
        console.error(`处理第${i + 1}行数据时出错:`, error)
        errors.push(`第${i + 1}行: 数据处理异常`)
        failedCount++
      }
    }

    // 设置导入结果
    importResult.value = {
      success: successCount,
      failed: failedCount,
      duplicates: duplicateCount,
      errors: [...errors, ...duplicateRecords]
    }
    
    showImportResult.value = true
    importProgress.value = 100
    
    // 显示导入结果消息
    if (successCount > 0) {
      ElMessage.success(`导入完成！成功导入 ${successCount} 条记录`)
    }
    if (failedCount > 0 || duplicateCount > 0) {
      ElMessage.warning(`导入完成，但有 ${failedCount + duplicateCount} 条记录未成功导入`)
    }
    
  } catch (error) {
    console.error('导入过程中发生错误:', error)
    ElMessage.error('导入失败，请检查文件格式')
    importResult.value = {
      success: 0,
      failed: 1,
      duplicates: 0,
      errors: ['导入过程中发生异常，请检查文件格式']
    }
    showImportResult.value = true
  } finally {
    importLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  // 页面加载完成
})
</script>

<route>
{
  meta: {
    title: '数据血缘管理',
    ignoreLabel: false
  }
}
</route>

<template>
  <div class="data-lineage-management">
    <Block
      title="数据血缘管理"
      :enable-expand-content="true"
      :enableBackButton="false"
      :enable-fixed-height="true"
      :enable-close-button="false"
      @content-expand="expendSearch"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button
          size="small"
          type="default"
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
        
        <el-button size="small" type="primary" @click="handleAddLineage">新增血缘关系</el-button>
      
        <!-- 导入导出功能按钮 -->
        <el-button size="small" type="primary" @click="handleImportData">导入数据</el-button>
        <el-button size="small" type="primary" @click="handleBatchExport">批量导出</el-button>
        <el-button size="small" type="primary" @click="handleViewAccessRecords">访问记录</el-button>
        <el-button size="small" type="primary" @click="handleVersionManagement">版本管理</el-button>
      </template>

      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="2"
            :label-width="80"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
          />
        </div>
      </template>

      <!-- 列表 -->
      <TableV2
        ref="tableRef"
        :defaultTableData="paginatedData"
        :columns="columns"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :height="tableHeight"
        :buttons="buttons"
        @loading="loading = $event"
        @click-button="onTableClickButton"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <!-- 标签列自定义显示 -->
        <template #tag="{ row }">
          <el-tag :color="row.tag.color" size="small" style="color: white; border: none;">
            {{ row.tag.text }}
          </el-tag>
        </template>

        <!-- 权限列自定义显示 -->
        <template #permissions="{ row }">
          <div class="permissions-display">
            <template v-for="(value, key) in row.permissions" :key="key">
              <el-tag
                v-if="typeof key === 'string' && key in PERMISSION_LABELS"
                :type="value ? 'success' : 'info'"
                size="small"
                style="margin-right: 4px; margin-bottom: 2px;"
              >
                {{ PERMISSION_LABELS[key as unknown as keyof typeof PERMISSION_LABELS] }}
              </el-tag>
            </template>
          </div>
        </template>

        <!-- 收藏列自定义显示 -->
        <template #favorite="{ row }">
          <el-button
            type="text"
            @click="handleToggleFavorite(row)"
            style="padding: 0;"
          >
            <el-icon :color="row.isFavorite ? '#f56c6c' : '#c0c4cc'" size="18">
              <StarFilled v-if="row.isFavorite" />
              <Star v-else />
            </el-icon>
          </el-button>
        </template>


      </TableV2>

      <!-- 分页 -->
      <div class="pagination-container" style="margin-top: 16px; text-align: right;">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="sortedData.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="currentPage = 1"
        />
      </div>
    </Block>

    <!-- 权限配置弹窗 -->
    <DialogComp
      v-model:visible="showPermissionDialog"
      title="编辑权限配置"
      width="600px"
      @click-confirm="handleConfirmPermission"
      @click-cancel="handleCancelPermission"
      @closed="handleCancelPermission"
    >
      <template #body>
        <div class="permission-config-content">
          <el-form
            :model="permissionForm"
            label-width="120px"
            class="permission-config-form"
          >
            <el-form-item label="授权人员" required>
              <el-select
                v-model="permissionForm.authorizedPersons"
                placeholder="请选择授权人员"
                style="width: 100%"
                multiple
                clearable
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="option in authorizedPersonOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="操作权限" required>
              <div class="permission-checkboxes">
                <el-checkbox
                  v-model="permissionForm.permissions.view"
                  label="查看权限"
                />
                <el-checkbox
                  v-model="permissionForm.permissions.edit"
                  label="编辑权限"
                />
                <el-checkbox
                  v-model="permissionForm.permissions.delete"
                  label="删除权限"
                />
                <el-checkbox
                  v-model="permissionForm.permissions.editPermission"
                  label="编辑权限权限"
                />
              </div>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </DialogComp>

    <!-- 数据血缘详情弹窗 -->
    <DialogComp
      v-model:visible="showDetailDialog"
      title="查看数据血缘详情"
      width="1000px"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showDetailDialog = false"
      @closed="showDetailDialog = false"
    >
      <template #body>
        <div class="detail-content" v-if="currentDetailRecord">
          <!-- 基本信息区域 -->
          <div class="detail-section">
            <div class="section-header">
              <h3>基本信息</h3>
            </div>
            <div class="info-grid">
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ currentDetailRecord.createTime }}</span>
              </div>
              <div class="info-item">
                <label>更新时间：</label>
                <span>{{ currentDetailRecord.updateTime }}</span>
              </div>
              <div class="info-item">
                <label>负责人：</label>
                <span>{{ currentDetailRecord.responsible }}</span>
              </div>
              <div class="info-item">
                <label>当前版本：</label>
                <span>{{ currentDetailRecord.dataVersion }}</span>
              </div>
              <div class="info-item">
                <label>转换类型：</label>
                <span>{{ currentDetailRecord.transformType }}</span>
              </div>
              <div class="info-item">
                <label>涉及数据量：</label>
                <span>{{ currentDetailRecord.dataVolume }}</span>
              </div>
            </div>
            
            <!-- 转换逻辑和依赖关系 -->
            <div class="transform-details">
              <div class="detail-row">
                <label>转换逻辑：</label>
                <div class="detail-content">
                  <el-input
                    :model-value="currentDetailRecord.transformLogic || '暂无转换逻辑'"
                    type="textarea"
                    :rows="2"
                    readonly
                    placeholder="暂无转换逻辑"
                  />
                </div>
              </div>
              <div class="detail-row">
                <label>依赖关系：</label>
                <div class="detail-content">
                  <el-input
                    :model-value="currentDetailRecord.dependency || '暂无依赖关系'"
                    readonly
                    placeholder="暂无依赖关系"
                  />
                </div>
              </div>
            </div>
            
            <!-- 权限信息 -->
            <div class="permissions-info">
              <label>权限：</label>
              <div class="permission-tags">
                <el-checkbox 
                  :model-value="currentDetailRecord.permissions.view" 
                  disabled
                >访问</el-checkbox>
                <el-checkbox 
                  :model-value="currentDetailRecord.permissions.edit" 
                  disabled
                >编辑</el-checkbox>
                <el-checkbox 
                  :model-value="currentDetailRecord.permissions.delete" 
                  disabled
                >删除</el-checkbox>
              </div>
            </div>
            
            <!-- 数据统计 -->
            <div class="data-statistics">
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-label">总记录数</div>
                  <div class="stat-value">1,234,567</div>
                </div>
                <div class="stat-card">
                  <div class="stat-label">涉及数据量总数</div>
                  <div class="stat-value">{{ currentDetailRecord.dataVolume }}</div>
                </div>
                <div class="stat-card">
                  <div class="stat-label">成功处理数</div>
                  <div class="stat-value">1,234,000</div>
                </div>
                <div class="stat-card">
                  <div class="stat-label">失败处理数</div>
                  <div class="stat-value">567</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 数据流向图 -->
          <div class="detail-section">
            <div class="section-header">
              <h3>数据流向</h3>
            </div>
            <div class="data-flow">
              <div class="flow-item source">
                <div class="flow-box">
                  <div class="box-title">源表</div>
                  <div class="box-content">
                    <div class="table-name">{{ currentDetailRecord.sourceTable }}</div>
                    <div class="field-info">
                      <span class="field-name">{{ currentDetailRecord.sourceField }}</span>
                      <el-tag size="small" type="info">{{ currentDetailRecord.sourceCategory }}</el-tag>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="flow-arrow">
                <el-icon size="24" color="#409eff">
                  <ArrowRight />
                </el-icon>
                <div class="transform-info">
                  <el-tag type="primary" size="small">{{ currentDetailRecord.transformType }}</el-tag>
                </div>
              </div>
              
              <div class="flow-item target">
                <div class="flow-box">
                  <div class="box-title">目标表</div>
                  <div class="box-content">
                    <div class="table-name">{{ currentDetailRecord.targetTable }}</div>
                    <div class="field-info">
                      <span class="field-name">{{ currentDetailRecord.targetField }}</span>
                      <el-tag size="small" type="success">{{ currentDetailRecord.targetCategory }}</el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 数据转换历史 -->
          <div class="detail-section">
            <div class="section-header">
              <h3>数据转换历史</h3>
            </div>
            <div class="transform-history">
              <div class="history-item">
                <div class="history-time">2024-01-01 14:00:01</div>
                <div class="history-content">
                  <div class="history-title">数据清洗</div>
                  <div class="history-desc">对源数据进行空值处理和格式标准化</div>
                </div>
              </div>
              <div class="history-item">
                <div class="history-time">2024-01-01 14:05:23</div>
                <div class="history-content">
                  <div class="history-title">字段映射</div>
                  <div class="history-desc">将源字段映射到目标字段结构</div>
                </div>
              </div>
              <div class="history-item">
                <div class="history-time">2024-01-01 14:10:45</div>
                <div class="history-content">
                  <div class="history-title">数据验证</div>
                  <div class="history-desc">验证转换后数据的完整性和准确性</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 转换类型分布 -->
          <div class="detail-section">
            <div class="section-header">
              <h3>转换类型分布</h3>
            </div>
            <div class="transform-stats">
              <div class="stat-item">
                <div class="stat-label">清洗</div>
                <div class="stat-bar">
                  <div class="stat-progress" style="width: 12%"></div>
                </div>
                <div class="stat-value">12%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">格式化转换</div>
                <div class="stat-bar">
                  <div class="stat-progress" style="width: 12%"></div>
                </div>
                <div class="stat-value">12%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">计算</div>
                <div class="stat-bar">
                  <div class="stat-progress" style="width: 12%"></div>
                </div>
                <div class="stat-value">12%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">聚合</div>
                <div class="stat-bar">
                  <div class="stat-progress" style="width: 12%"></div>
                </div>
                <div class="stat-value">12%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">映射</div>
                <div class="stat-bar">
                  <div class="stat-progress" style="width: 12%"></div>
                </div>
                <div class="stat-value">12%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">联表</div>
                <div class="stat-bar">
                  <div class="stat-progress" style="width: 12%"></div>
                </div>
                <div class="stat-value">12%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">分组</div>
                <div class="stat-bar">
                  <div class="stat-progress" style="width: 12%"></div>
                </div>
                <div class="stat-value">12%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">过滤</div>
                <div class="stat-bar">
                  <div class="stat-progress" style="width: 12%"></div>
                </div>
                <div class="stat-value">12%</div>
              </div>
            </div>
          </div>

          <!-- 备注区域 -->
          <div class="detail-section">
            <div class="section-header">
              <h3>备注</h3>
            </div>
            <div class="remark-content">
              <el-input
                :model-value="currentDetailRecord.remark || '暂无备注'"
                type="textarea"
                :rows="3"
                readonly
                placeholder="暂无备注"
              />
            </div>
          </div>
        </div>
      </template>
    </DialogComp>

    <!-- 新增/编辑血缘关系弹窗 -->
    <DialogComp
      v-model:visible="showLineageDialog"
      :title="dialogMode === 'add' ? '新增数据血缘关系' : '编辑数据血缘关系'"
      width="800px"
      @click-confirm="handleConfirmLineage"
      @click-cancel="handleCancelLineage"
      @closed="handleCancelLineage"
    >
      <template #body>
        <div class="lineage-form-content">
          <el-form
            :model="lineageForm"
            :rules="lineageFormRules"
            label-width="120px"
            class="lineage-form"
          >
            <!-- 源表信息 -->
            <div class="form-section">
              <div class="section-title">源表信息</div>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="源表" prop="sourceTable" required>
                    <el-select
                      v-model="lineageForm.sourceTable"
                      placeholder="请选择源表"
                      style="width: 100%"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="option in TABLE_OPTIONS"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="源字段" prop="sourceField" required>
                    <el-input
                      v-model="lineageForm.sourceField"
                      placeholder="请输入源字段名称"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="源字段类型" prop="sourceCategory" required>
                    <el-select
                      v-model="lineageForm.sourceCategory"
                      placeholder="请选择字段类型"
                      style="width: 100%"
                      clearable
                    >
                      <el-option
                        v-for="option in FIELD_TYPE_OPTIONS"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 目标表信息 -->
            <div class="form-section">
              <div class="section-title">目标表信息</div>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="目标表" prop="targetTable" required>
                    <el-select
                      v-model="lineageForm.targetTable"
                      placeholder="请选择目标表"
                      style="width: 100%"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="option in TABLE_OPTIONS"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="目标字段" prop="targetField" required>
                    <el-input
                      v-model="lineageForm.targetField"
                      placeholder="请输入目标字段名称"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="目标字段类型" prop="targetCategory" required>
                    <el-select
                      v-model="lineageForm.targetCategory"
                      placeholder="请选择字段类型"
                      style="width: 100%"
                      clearable
                    >
                      <el-option
                        v-for="option in FIELD_TYPE_OPTIONS"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 转换信息 -->
            <div class="form-section">
              <div class="section-title">转换信息</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="转换类型" prop="transformType" required>
                    <el-select
                      v-model="lineageForm.transformType"
                      placeholder="请选择转换类型"
                      style="width: 100%"
                      clearable
                    >
                      <el-option
                        v-for="option in TRANSFORM_TYPE_OPTIONS"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="负责人" prop="responsible" required>
                    <el-input
                      v-model="lineageForm.responsible"
                      placeholder="请输入负责人"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="转换逻辑" prop="transformLogic">
                    <el-input
                      v-model="lineageForm.transformLogic"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入转换逻辑描述"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="依赖关系" prop="dependency">
                    <el-input
                      v-model="lineageForm.dependency"
                      placeholder="请输入依赖关系描述"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="lineageForm.remark"
                      type="textarea"
                      :rows="2"
                      placeholder="请输入备注信息"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 权限配置 -->
            <div class="form-section">
              <div class="section-title">权限配置</div>
              <el-form-item label="操作权限">
                <div class="permission-checkboxes">
                  <el-checkbox
                    v-model="lineageForm.permissions.view"
                    label="访问权限"
                  />
                  <el-checkbox
                    v-model="lineageForm.permissions.edit"
                    label="编辑权限"
                  />
                  <el-checkbox
                    v-model="lineageForm.permissions.delete"
                    label="删除权限"
                  />
                </div>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </template>
    </DialogComp>

    <!-- 访问记录弹窗 -->
    <DialogComp
      v-model:visible="showAccessRecordsDialog"
      title="访问记录"
      width="1200px"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="handleCloseAccessRecords"
      @closed="handleCloseAccessRecords"
    >
      <template #body>
        <div class="access-records-content" v-loading="accessRecordsLoading">
          <!-- 统计信息 -->
          <div class="records-stats">
            <div class="stat-card">
              <div class="stat-number">{{ accessRecords.length }}</div>
              <div class="stat-label">总访问次数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ accessRecords.filter(r => r.actionType === 'view').length }}</div>
              <div class="stat-label">查看次数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ accessRecords.filter(r => r.actionType === 'query').length }}</div>
              <div class="stat-label">查询次数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ accessRecords.filter(r => r.details?.operationResult === 'success').length }}</div>
              <div class="stat-label">成功操作</div>
            </div>
          </div>

          <!-- 访问记录表格 -->
          <TableV2
            :defaultTableData="paginatedAccessRecords"
            :columns="accessRecordsColumns"
            :enable-toolbar="false"
            :enable-own-button="false"
            :enable-selection="false"
            :height="400"
            @loading="accessRecordsLoading = $event"
          >
            <!-- 操作类型列自定义显示 -->
            <template #actionType="{ row }">
              <el-tag
                :type="getActionTypeTagType(row.actionType)"
                size="small"
              >
                {{ getActionTypeLabel(row.actionType) }}
              </el-tag>
            </template>

            <!-- 结果列自定义显示 -->
            <template #result="{ row }">
              <el-tag
                :type="row.details?.operationResult === 'success' ? 'success' : 'danger'"
                size="small"
              >
                {{ row.details?.operationResult === 'success' ? '成功' : '失败' }}
              </el-tag>
            </template>

            <!-- 耗时列自定义显示 -->
            <template #duration="{ row }">
              <span>{{ row.duration }}s</span>
            </template>
          </TableV2>

          <!-- 分页 -->
          <div class="pagination-container" style="margin-top: 16px; text-align: right;">
            <el-pagination
              v-model:current-page="accessRecordsCurrentPage"
              v-model:page-size="accessRecordsPageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="accessRecords.length"
              layout="total, sizes, prev, pager, next, jumper"
              @current-change="handleAccessRecordsPageChange"
              @size-change="handleAccessRecordsPageSizeChange"
            />
          </div>
        </div>
      </template>
    </DialogComp>

    <!-- 版本管理弹窗 -->
    <DialogComp
      v-model:visible="showVersionManagementDialog"
      title="数据血缘关系版本管理"
      width="1000px"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="handleCloseVersionManagement"
      @closed="handleCloseVersionManagement"
    >
      <template #body>
        <div class="version-management-content" v-loading="versionManagementLoading">
          <!-- 数据血缘关系选择 -->
          <div class="lineage-selection-section">
            <div class="section-header">
              <h4>选择数据血缘关系</h4>
              <el-tag type="info" size="small">{{ versionManagementForm.selectedRecords.length }} 个已选择</el-tag>
            </div>
            <div class="lineage-selection-content">
              <el-select
                v-model="versionManagementForm.selectedRecords"
                placeholder="请选择要管理版本的数据血缘关系"
                style="width: 100%"
                multiple
                clearable
                collapse-tags
                collapse-tags-tooltip
                filterable
                @change="handleLineageSelectionChange"
              >
                <el-option
                  v-for="record in records"
                  :key="record.id"
                  :label="`${record.sourceField || '未定义'} → ${record.targetField || '未定义'}`"
                  :value="record.id"
                >
                  <div class="lineage-option">
                    <div class="lineage-main">
                      <span class="source-field">{{ record.sourceField || '未定义' }}</span>
                      <el-icon style="margin: 0 8px; color: #409eff"><ArrowRight /></el-icon>
                      <span class="target-field">{{ record.targetField || '未定义' }}</span>
                    </div>
                    <div class="lineage-meta">
                      <span class="table-info">{{ record.sourceTable || '未定义' }} → {{ record.targetTable || '未定义' }}</span>
                      <span class="responsible">负责人：{{ record.responsible || '未定义' }}</span>
                    </div>
                  </div>
                </el-option>
              </el-select>
            </div>
            <!-- 已选择的血缘关系列表 -->
            <div v-if="versionManagementForm.selectedRecords.length > 0" class="selected-records-list">
              <div class="selected-records-header">已选择的血缘关系：</div>
              <div class="selected-records-tags">
                <el-tag 
                  v-for="recordId in versionManagementForm.selectedRecords" 
                  :key="recordId"
                  type="primary" 
                  size="small"
                  closable
                  @close="removeSelectedRecord(recordId)"
                  style="margin-right: 8px; margin-bottom: 8px;"
                >
                  {{ records.find(r => r.id === recordId)?.sourceField || '未定义' }} → 
                  {{ records.find(r => r.id === recordId)?.targetField || '未定义' }}
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 版本选择区域 -->
          <div class="version-selection-section">
            <div class="section-header">
              <h4>版本选择</h4>
            </div>
            <el-row :gutter="20" style="align-items: end;">
              <el-col :span="18">
                <el-form-item label="版本时间" label-width="100px">
                  <el-date-picker
                    v-model="versionManagementForm.targetTime"
                    type="datetime"
                    placeholder="请选择版本时间"
                    style="width: 100%"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    :default-time="new Date(2024, 3, 26, 13, 0, 21)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-button 
                  type="primary" 
                  @click="handleVersionRollback"
                  :disabled="versionManagementForm.selectedRecords.length === 0 || !versionManagementForm.targetTime"
                  style="width: 100%; height: 32px; margin-top: -60px;"
                >
                  回溯
                </el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 版本更改描述 -->
          <div class="version-description-section">
            <div class="section-header">
              <h4>版本更改描述</h4>
            </div>
            <div class="version-description-content">
              <div 
                v-if="versionManagementForm.targetVersion"
                class="description-text"
              >
                {{ versionHistory.find(v => v.version === versionManagementForm.targetVersion)?.description || '暂无描述' }}
              </div>
              <div v-else class="empty-description">
                请在下方版本历史中点击选择要回溯的版本
              </div>
            </div>
          </div>

          <!-- 版本历史列表 -->
          <div class="version-history-section">
            <div class="section-header">
              <h4>版本历史</h4>
            </div>
            <div class="version-history-list">
              <div 
                v-for="version in versionHistory" 
                :key="version.id"
                class="version-item"
                :class="{ 'selected': version.version === versionManagementForm.targetVersion }"
                @click="versionManagementForm.targetVersion = version.version"
              >
                <div class="version-header">
                  <div class="version-info">
                    <span class="version-number">{{ version.version }}</span>
                    <span class="version-time">{{ version.createTime }}</span>
                  </div>
                  <div class="version-actions">
                    <el-button
                      type="text"
                      size="small"
                      @click.stop="handleDeleteVersion(version)"
                      style="color: #f56c6c"
                    >
                      删除版本
                    </el-button>
                  </div>
                </div>
                <div class="version-description">{{ version.description }}</div>
                <div class="version-operator">操作人：{{ version.operator }}</div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DialogComp>

    <!-- 导入数据弹窗 -->
    <DialogComp
      v-model:visible="showImportDialog"
      title="导入数据血缘关系"
      width="600px"
      :visible-confirm-button="!showImportResult"
      :visible-cancel-button="true"
      confirm-text="开始导入"
      cancel-text="取消"
      :confirm-loading="importLoading"
      @click-confirm="handleImportDataConfirm"
      @click-cancel="handleCloseImport"
      @closed="handleCloseImport"
    >
      <template #body>
        <div class="import-content" v-loading="importLoading">
          <!-- 文件选择区域 -->
          <div v-if="!showImportResult" class="import-file-section">
            <div class="section-header">
              <div class="section-title">选择导入文件</div>
              <el-button 
                size="small" 
                type="primary" 
                @click="handleDownloadTemplate"
                style="margin-bottom: 12px;"
              >
                下载导入模板
              </el-button>
            </div>
            <el-upload
              class="upload-demo"
              drag
              :auto-upload="false"
              :show-file-list="true"
              :limit="1"
              accept=".xlsx,.xls"
              :before-upload="handleFileChange"
              :on-change="handleFileListChange"
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传 xlsx/xls 文件，且不超过 10MB
                </div>
              </template>
            </el-upload>
            
            <div class="import-tips">
              <el-alert
                title="导入说明"
                type="info"
                :closable="false"
                show-icon
              >
                <template #default>
                  <div>
                    <p>1. 请使用指定的Excel模板格式进行数据导入</p>
                    <p>2. 导入时会自动检查数据格式和重复性</p>
                    <p>3. 重复的数据将被跳过，不会覆盖现有数据</p>
                    <p>4. 导入完成后将显示详细的导入结果</p>
                  </div>
                </template>
              </el-alert>
            </div>
          </div>

          <!-- 导入进度 -->
          <div v-if="importLoading" class="import-progress-section">
            <div class="section-title">导入进度</div>
            <el-progress 
              :percentage="importProgress" 
              :stroke-width="8"
              status="success"
            >
              <template #default="{ percentage }">
                <span class="percentage-value">{{ percentage }}%</span>
              </template>
            </el-progress>
            <div class="progress-text">
              正在处理数据，请稍候...
            </div>
          </div>

          <!-- 导入结果 -->
          <div v-if="showImportResult" class="import-result-section">
            <div class="section-title">导入结果</div>
            
            <div class="result-summary">
              <el-row :gutter="16">
                <el-col :span="6">
                  <div class="result-item success">
                    <div class="result-number">{{ importResult.success }}</div>
                    <div class="result-label">成功</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="result-item warning">
                    <div class="result-number">{{ importResult.duplicates }}</div>
                    <div class="result-label">重复</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="result-item danger">
                    <div class="result-number">{{ importResult.failed }}</div>
                    <div class="result-label">失败</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="result-item info">
                    <div class="result-number">{{ importResult.success + importResult.duplicates + importResult.failed }}</div>
                    <div class="result-label">总计</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 错误信息列表 -->
            <div v-if="importResult.errors.length > 0" class="error-list">
              <div class="error-title">错误详情：</div>
              <div class="error-content">
                <div 
                  v-for="(error, index) in importResult.errors.slice(0, 10)" 
                  :key="index"
                  class="error-item"
                >
                  {{ error }}
                </div>
                <div v-if="importResult.errors.length > 10" class="error-more">
                  ... 还有 {{ importResult.errors.length - 10 }} 条错误信息
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DialogComp>
  </div>
</template>

<style lang="scss" scoped>
.data-lineage-management {
  height: 100%;

  .search {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
  }

  .permissions-display {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .table-operation-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;

    .el-button {
      margin: 0;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0;
    border-top: 1px solid #ebeef5;
  }
}

// 权限配置弹窗样式
.permission-config-content {
  padding: 20px;

  .permission-config-form {
    .el-form-item {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    :deep(.el-form-item__label) {
      color: #303133;
      font-weight: 500;
    }

    .permission-checkboxes {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;

      .el-checkbox {
        margin: 0;

        :deep(.el-checkbox__label) {
          color: #606266;
          font-size: 14px;
          font-weight: 500;
        }

        :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
          background-color: #409eff;
          border-color: #409eff;
        }

        :deep(.el-checkbox__inner:hover) {
          border-color: #409eff;
        }
      }
    }

    :deep(.el-select) {
      .el-input__wrapper {
        border-radius: 4px;
      }
    }
  }
}

// 新增/编辑血缘关系弹窗样式
.lineage-form-content {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;

  .lineage-form {
    .form-section {
      margin-bottom: 32px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e4e7ed;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 20px;
        padding-bottom: 8px;
        border-bottom: 2px solid #409eff;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          bottom: -2px;
          width: 40px;
          height: 2px;
          background-color: #409eff;
        }
      }
    }

    .el-form-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    :deep(.el-form-item__label) {
      color: #303133;
      font-weight: 500;
      font-size: 14px;
    }

    :deep(.el-input__wrapper) {
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #c0c4cc;
      }

      &.is-focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }

    :deep(.el-select) {
      .el-input__wrapper {
        border-radius: 6px;
      }
    }

    :deep(.el-textarea__inner) {
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #c0c4cc;
      }

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }

    .permission-checkboxes {
      display: flex;
      gap: 24px;
      flex-wrap: wrap;

      .el-checkbox {
        margin: 0;

        :deep(.el-checkbox__label) {
          color: #606266;
          font-size: 14px;
          font-weight: 500;
        }

        :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
          background-color: #409eff;
          border-color: #409eff;
        }

        :deep(.el-checkbox__inner:hover) {
          border-color: #409eff;
        }
      }
    }
  }

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// 表格样式优化
:deep(.el-table) {
  .el-table__header {
    background-color: #f8f9fa;

    th {
      background-color: #f8f9fa !important;
      color: #303133;
      font-weight: 600;
      border-bottom: 2px solid #e4e7ed;
    }
  }

  .el-table__body {
    tr:hover {
      background-color: #f5f7fa;
    }

    td {
      border-bottom: 1px solid #ebeef5;
      padding: 12px 0;
    }
  }
}

// 标签样式
:deep(.el-tag) {
  font-weight: 500;
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 12px;
}

// 按钮禁用状态样式
:deep(.el-button:disabled) {
  opacity: 0.5;
  cursor: not-allowed;

  &.el-button--primary:disabled {
    background-color: #a0cfff;
    border-color: #a0cfff;
  }

  &.el-button--danger:disabled {
    background-color: #fab6b6;
    border-color: #fab6b6;
  }

  &.el-button--info:disabled {
    background-color: #c8c9cc;
    border-color: #c8c9cc;
  }
}

// 访问记录弹窗样式
.access-records-content {
  padding: 20px;
  
  .records-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 12px;
      text-align: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      }
      
      &:nth-child(2) {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &:nth-child(3) {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &:nth-child(4) {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
      
      .stat-number {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
      
      .stat-label {
        font-size: 14px;
        opacity: 0.9;
        font-weight: 500;
      }
    }
  }
  
  .records-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    :deep(.el-table) {
      .el-table__header {
        background-color: #f8f9fa;
        
        th {
          background-color: #f8f9fa !important;
          color: #303133;
          font-weight: 600;
          border-bottom: 2px solid #e4e7ed;
          font-size: 14px;
        }
      }
      
      .el-table__body {
        tr:hover {
          background-color: #f5f7fa;
        }
        
        td {
          border-bottom: 1px solid #ebeef5;
          padding: 12px 0;
          font-size: 13px;
        }
      }
    }
  }
}



// 收藏按钮样式
:deep(.el-button.is-text) {
  padding: 4px;

  &:hover {
    background-color: transparent;
  }
}

// 数据血缘详情弹窗样式
.detail-content {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;

  .detail-section {
    margin-bottom: 32px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    &:last-child {
      margin-bottom: 0;
    }

    .section-header {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 2px solid #409eff;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  // 基本信息网格
  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;

    .info-item {
      display: flex;
      align-items: center;

      label {
        font-weight: 500;
        color: #606266;
        margin-right: 8px;
        min-width: 80px;
      }

      span {
        color: #303133;
        flex: 1;
      }
    }
  }

  // 转换详情
  .transform-details {
    margin-top: 20px;

    .detail-row {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;
      gap: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        font-weight: 500;
        color: #606266;
        min-width: 80px;
        padding-top: 8px;
      }

      .detail-content {
        flex: 1;

        :deep(.el-input__wrapper) {
          background-color: #f8f9fa;
          border: 1px solid #e4e7ed;
        }

        :deep(.el-textarea__inner) {
          background-color: #f8f9fa;
          border: 1px solid #e4e7ed;
          resize: none;
        }
      }
    }
  }

  // 权限信息
  .permissions-info {
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 12px;

    label {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
    }

    .permission-tags {
      display: flex;
      gap: 16px;

      :deep(.el-checkbox) {
        .el-checkbox__label {
          color: #606266;
          font-size: 14px;
        }

        &.is-disabled {
          .el-checkbox__label {
            color: #c0c4cc;
          }
        }
      }
    }
  }

  // 数据统计
  .data-statistics {
    margin-top: 20px;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;

      .stat-card {
        padding: 16px;
        background-color: white;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        text-align: center;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        .stat-label {
          font-size: 12px;
          color: #909399;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .stat-value {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }
      }
    }
  }

  // 数据流向图
  .data-flow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;

    .flow-item {
      flex: 1;
      
      .flow-box {
        padding: 16px;
        background-color: white;
        border: 2px solid #e4e7ed;
        border-radius: 8px;
        text-align: center;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }

        .box-title {
          font-size: 14px;
          font-weight: 600;
          color: #909399;
          margin-bottom: 12px;
        }

        .table-name {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 8px;
        }

        .field-info {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          .field-name {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }

    .flow-arrow {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .transform-info {
        font-size: 12px;
      }
    }
  }

  // 转换历史
  .transform-history {
    .history-item {
      display: flex;
      gap: 16px;
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;

      &:last-child {
        border-bottom: none;
      }

      .history-time {
        min-width: 140px;
        font-size: 12px;
        color: #909399;
        font-family: monospace;
      }

      .history-content {
        flex: 1;

        .history-title {
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }

        .history-desc {
          font-size: 14px;
          color: #606266;
          line-height: 1.4;
        }
      }
    }
  }

  // 转换类型统计
  .transform-stats {
    .stat-item {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .stat-label {
        min-width: 80px;
        font-size: 14px;
        color: #606266;
      }

      .stat-bar {
        flex: 1;
        height: 8px;
        background-color: #f0f2f5;
        border-radius: 4px;
        overflow: hidden;
        position: relative;

        .stat-progress {
          height: 100%;
          background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
          border-radius: 4px;
          transition: width 0.3s ease;
        }
      }

      .stat-value {
        min-width: 40px;
        text-align: right;
        font-size: 14px;
        font-weight: 600;
        color: #409eff;
      }
    }
  }

  // 备注内容
  .remark-content {
    :deep(.el-textarea__inner) {
      background-color: #f8f9fa;
      border: 1px solid #e4e7ed;
      resize: none;
    }
  }

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// 版本管理弹窗样式
.version-management-content {
  padding: 20px;
  
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
    
    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .lineage-selection-section {
    margin-bottom: 24px;
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    
    .lineage-selection-content {
      margin-bottom: 16px;
      
      :deep(.el-select) {
        .el-select__tags {
          max-height: 80px;
          overflow-y: auto;
        }
      }
    }
    
    .selected-records-list {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #e4e7ed;
      
      .selected-records-header {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
        font-weight: 500;
      }
      
      .selected-records-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }
    }
  }
  
  // 血缘关系选项样式
  :deep(.lineage-option) {
    padding: 8px 0;
    
    .lineage-main {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      
      .source-field {
        color: #409eff;
        font-weight: 500;
      }
      
      .target-field {
        color: #67c23a;
        font-weight: 500;
      }
    }
    
    .lineage-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      
      .table-info {
        color: #606266;
        font-size: 11px;
        background: #f0f2f5;
        padding: 1px 4px;
        border-radius: 2px;
      }
      
      .responsible {
        color: #909399;
      }
    }
  }
  
  .version-selection-section {
    margin-bottom: 24px;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
  
  .version-description-section {
    margin-bottom: 24px;
    
    .version-description-content {
      background: #fff;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      padding: 16px;
      min-height: 80px;
      
      .description-text {
        color: #303133;
        line-height: 1.6;
        font-size: 14px;
      }
      
      .empty-description {
        color: #909399;
        font-style: italic;
        text-align: center;
        padding: 20px 0;
      }
    }
  }
  
  .version-history-section {
    margin-bottom: 24px;
    
    .version-history-list {
      max-height: 320px;
      overflow-y: auto;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      background: #fff;
      
      .version-item {
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        
        &:hover {
          background-color: #f5f7fa;
        }
        
        &.selected {
          background-color: #ecf5ff;
          border-left: 4px solid #409eff;
          
          &::before {
            content: '';
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background-color: #409eff;
            border-radius: 50%;
          }
        }
        
        &:last-child {
          border-bottom: none;
        }
        
        .version-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          
          .version-info {
            display: flex;
            align-items: center;
            gap: 12px;
            
            .version-number {
              font-weight: 600;
              color: #409eff;
              font-size: 15px;
              padding: 2px 8px;
              background: rgba(64, 158, 255, 0.1);
              border-radius: 4px;
            }
            
            .version-time {
              color: #909399;
              font-size: 12px;
              background: #f5f7fa;
              padding: 2px 6px;
              border-radius: 3px;
            }
          }
          
          .version-actions {
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        }
        
        &:hover .version-actions {
          opacity: 1;
        }
        
        .version-description {
          color: #606266;
          font-size: 13px;
          line-height: 1.5;
          margin-bottom: 6px;
          padding-left: 4px;
        }
        
        .version-operator {
          color: #909399;
          font-size: 12px;
          padding-left: 4px;
        }
      }
      
      // 滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
        
        &:hover {
          background: #a8a8a8;
        }
      }
    }
  }
  
  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
    
    .el-button {
      min-width: 100px;
      font-weight: 500;
      
      &.el-button--primary {
        background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        
        &:hover {
          background: linear-gradient(135deg, #3a8ee6 0%, #337ecc 100%);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
          transform: translateY(-1px);
        }
        
        &:disabled {
          background: #c0c4cc;
          box-shadow: none;
          transform: none;
        }
      }
    }
  }
}

// 导入导出功能样式
.import-content {
  padding: 20px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e4e7ed;
  }
  
  .import-file-section {
    margin-bottom: 24px;
    
    .upload-demo {
      margin-bottom: 16px;
    }
    
    .import-tips {
      margin-top: 16px;
      
      :deep(.el-alert__content) {
        p {
          margin: 4px 0;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }
  
  .import-progress-section {
    text-align: center;
    padding: 40px 20px;
    
    .progress-text {
      margin-top: 16px;
      color: #606266;
      font-size: 14px;
    }
    
    .percentage-value {
      font-size: 16px;
      font-weight: 600;
      color: #67c23a;
    }
  }
  
  .import-result-section {
    .result-summary {
      margin-bottom: 24px;
      
      .result-item {
        text-align: center;
        padding: 20px;
        border-radius: 8px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        
        .result-number {
          font-size: 24px;
          font-weight: 700;
          margin-bottom: 8px;
        }
        
        .result-label {
          font-size: 14px;
          color: #6c757d;
        }
        
        &.success {
          background: #f0f9ff;
          border-color: #67c23a;
          
          .result-number {
            color: #67c23a;
          }
        }
        
        &.warning {
          background: #fefce8;
          border-color: #e6a23c;
          
          .result-number {
            color: #e6a23c;
          }
        }
        
        &.danger {
          background: #fef2f2;
          border-color: #f56c6c;
          
          .result-number {
            color: #f56c6c;
          }
        }
        
        &.info {
          background: #f0f9ff;
          border-color: #409eff;
          
          .result-number {
            color: #409eff;
          }
        }
      }
    }
    
    .error-list {
      .error-title {
        font-size: 14px;
        font-weight: 600;
        color: #f56c6c;
        margin-bottom: 12px;
      }
      
      .error-content {
        max-height: 200px;
        overflow-y: auto;
        background: #fef2f2;
        border: 1px solid #fbc4c4;
        border-radius: 6px;
        padding: 12px;
        
        .error-item {
          font-size: 13px;
          color: #f56c6c;
          line-height: 1.5;
          margin-bottom: 4px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
        
        .error-more {
          font-size: 13px;
          color: #909399;
          font-style: italic;
          text-align: center;
          margin-top: 8px;
          padding-top: 8px;
          border-top: 1px solid #fbc4c4;
        }
      }
    }
  }
}
</style>
