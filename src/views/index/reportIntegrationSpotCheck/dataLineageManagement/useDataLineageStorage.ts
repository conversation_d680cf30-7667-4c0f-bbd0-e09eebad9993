import { ref, computed } from 'vue'
import type { DataLineageRecord, DataLineagePermissionConfig, DataLineageFormData } from './types'
import { STORAGE_KEYS, TAG_COLORS, CATEGORY_OPTIONS, RESPONSIBLE_OPTIONS, TABLE_OPTIONS, TRANSFORM_TYPE_OPTIONS } from './types'

export function useDataLineageStorage() {
  // 响应式数据
  const records = ref<DataLineageRecord[]>([])
  const permissionConfigs = ref<DataLineagePermissionConfig[]>([])
  const searchKeyword = ref('')
  const selectedRecords = ref<DataLineageRecord[]>([])

  // 生成模拟数据
  const generateMockData = (): DataLineageRecord[] => {
    const mockData: DataLineageRecord[] = []

    // 定义有逻辑关系的字段映射
    const fieldMappings = [
      { sourceTable: 'user_info', source: '用户ID', target: 'USER_ID', targetTable: 'customer', category: 'INT' },
      { sourceTable: 'order_detail', source: '订单号', target: 'ORDER_NO', targetTable: 'payment', category: 'VARCHAR' },
      { sourceTable: 'product_info', source: '金额', target: 'AMOUNT', targetTable: 'order_detail', category: 'DECIMAL' },
      { sourceTable: 'user_info', source: '创建时间', target: 'CREATE_TIME', targetTable: 'employee', category: 'DATE' },
      { sourceTable: 'order_detail', source: '状态码', target: 'STATUS_CODE', targetTable: 'inventory', category: 'INT' },
      { sourceTable: 'customer', source: '用户地址', target: 'USER_ADDRESS', targetTable: 'user_info', category: 'VARCHAR' },
      { sourceTable: 'user_info', source: '联系电话', target: 'PHONE_NUMBER', targetTable: 'customer', category: 'VARCHAR' },
      { sourceTable: 'customer', source: '邮箱地址', target: 'EMAIL_ADDRESS', targetTable: 'employee', category: 'VARCHAR' },
      { sourceTable: 'employee', source: '用户姓名', target: 'USER_NAME', targetTable: 'user_info', category: 'VARCHAR' },
      { sourceTable: 'department', source: '部门编号', target: 'DEPT_ID', targetTable: 'employee', category: 'INT' },
      { sourceTable: 'product_info', source: '价格', target: 'PRICE', targetTable: 'inventory', category: 'DECIMAL' },
      { sourceTable: 'inventory', source: '数量', target: 'QUANTITY', targetTable: 'order_detail', category: 'INT' },
      { sourceTable: 'user_info', source: '是否有效', target: 'IS_ACTIVE', targetTable: 'customer', category: 'BOOL' },
      { sourceTable: 'order_detail', source: '更新时间', target: 'UPDATE_TIME', targetTable: 'payment', category: 'DATE' },
      { sourceTable: 'product_info', source: '折扣率', target: 'DISCOUNT_RATE', targetTable: 'order_detail', category: 'FLOAT' },
      { sourceTable: 'customer', source: '是否删除', target: 'IS_DELETED', targetTable: 'user_info', category: 'BOOL' },
      { sourceTable: 'product_info', source: '评分', target: 'RATING', targetTable: 'customer', category: 'FLOAT' },
      { sourceTable: 'supplier', source: '备注信息', target: 'REMARK', targetTable: 'product_info', category: 'VARCHAR' },
      { sourceTable: 'category', source: '排序号', target: 'SORT_ORDER', targetTable: 'product_info', category: 'INT' },
      { sourceTable: 'payment', source: '生效日期', target: 'EFFECTIVE_DATE', targetTable: 'order_detail', category: 'DATE' }
    ]

    const tagTexts = ['核心', '重要', '一般', '临时', '归档', '敏感', '公开', '内部']
    const remarks = [
      '数据同步正常',
      '需要定期维护',
      '重要业务数据',
      '测试环境数据',
      '历史数据归档',
      '敏感信息加密',
      '公开数据展示',
      '内部使用数据',
      '待清理数据',
      '核心业务字段'
    ]

    for (let i = 0; i < fieldMappings.length; i++) {
      const mapping = fieldMappings[i]
      const responsibleIndex = Math.floor(Math.random() * RESPONSIBLE_OPTIONS.length)
      const tagIndex = Math.floor(Math.random() * tagTexts.length)
      const colorIndex = Math.floor(Math.random() * TAG_COLORS.length)
      const remarkIndex = Math.floor(Math.random() * remarks.length)

      // 生成数据版本时间（最近30天内的随机时间）
      const versionTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
      const dataVersion = versionTime.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-')

      // 随机生成权限，某些记录故意缺失权限用于演示
      const hasViewPermission = Math.random() > 0.1 // 90% 有查看权限
      const hasEditPermission = Math.random() > 0.2 // 80% 有编辑权限
      const hasDeletePermission = Math.random() > 0.3 // 70% 有删除权限
      const hasEditPermissionPermission = Math.random() > 0.4 // 60% 有编辑权限权限

      mockData.push({
        id: `lineage_${i + 1}`,
        sequence: i + 1,
        tag: {
          color: TAG_COLORS[colorIndex],
          text: tagTexts[tagIndex]
        },
        sourceTable: mapping.sourceTable,
        sourceField: mapping.source,
        sourceCategory: mapping.category,
        targetTable: mapping.targetTable,
        targetField: mapping.target,
        targetCategory: mapping.category, // 源和目标使用相同的数据类型
        transformType: TRANSFORM_TYPE_OPTIONS[Math.floor(Math.random() * TRANSFORM_TYPE_OPTIONS.length)].value,
        transformLogic: `将${mapping.source}字段进行${TRANSFORM_TYPE_OPTIONS[Math.floor(Math.random() * TRANSFORM_TYPE_OPTIONS.length)].label}处理`,
        dependency: Math.random() > 0.5 ? `依赖于${mapping.sourceTable}.${mapping.source}` : '无依赖',
        dataVolume: `${Math.floor(Math.random() * 10000) + 1000}条`,
        responsible: RESPONSIBLE_OPTIONS[responsibleIndex],
        dataVersion,
        remark: remarks[remarkIndex],
        permissions: {
          view: hasViewPermission,
          edit: hasEditPermission,
          delete: hasDeletePermission,
          editPermission: hasEditPermissionPermission
        },
        isFavorite: Math.random() > 0.7, // 30% 概率收藏
        createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        updateTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
      })
    }

    return mockData
  }

  // 从 localStorage 加载数据
  const loadFromStorage = () => {
    try {
      const storedRecords = localStorage.getItem(STORAGE_KEYS.DATA_LINEAGE_RECORDS)
      const storedPermissions = localStorage.getItem(STORAGE_KEYS.DATA_LINEAGE_PERMISSIONS)

      if (storedRecords) {
        const parsedRecords = JSON.parse(storedRecords)
        // 检查数据结构是否包含新字段，或者分类是否使用旧的分类系统
        const needsUpdate = parsedRecords.length > 0 && (
          !parsedRecords[0].dataVersion ||
          !parsedRecords[0].hasOwnProperty('remark') ||
          (parsedRecords[0].sourceCategory && !['INT', 'FLOAT', 'DECIMAL', 'VARCHAR', 'DATE', 'BOOL'].includes(parsedRecords[0].sourceCategory))
        )

        if (needsUpdate) {
          // 数据结构已更新，重新生成数据
          records.value = generateMockData()
          saveToStorage()
        } else {
          records.value = parsedRecords
        }
      } else {
        // 首次加载，生成模拟数据
        records.value = generateMockData()
        saveToStorage()
      }

      if (storedPermissions) {
        permissionConfigs.value = JSON.parse(storedPermissions)
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      records.value = generateMockData()
    }
  }

  // 保存到 localStorage
  const saveToStorage = () => {
    try {
      localStorage.setItem(STORAGE_KEYS.DATA_LINEAGE_RECORDS, JSON.stringify(records.value))
      localStorage.setItem(STORAGE_KEYS.DATA_LINEAGE_PERMISSIONS, JSON.stringify(permissionConfigs.value))
    } catch (error) {
      console.error('保存数据失败:', error)
    }
  }

  // 过滤后的数据
  const filteredRecords = computed(() => {
    if (!searchKeyword.value) {
      return records.value
    }

    const keyword = searchKeyword.value.toLowerCase()
    return records.value.filter(record => 
      record.sourceField.toLowerCase().includes(keyword) ||
      record.targetField.toLowerCase().includes(keyword) ||
      record.responsible.toLowerCase().includes(keyword) ||
      record.tag.text.toLowerCase().includes(keyword)
    )
  })

  // 切换收藏状态
  const toggleFavorite = (recordId: string) => {
    const record = records.value.find(r => r.id === recordId)
    if (record) {
      record.isFavorite = !record.isFavorite
      record.updateTime = new Date().toISOString()
      saveToStorage()
    }
  }

  // 更新权限配置
  const updatePermissionConfig = (config: DataLineagePermissionConfig) => {
    const existingIndex = permissionConfigs.value.findIndex(c => c.recordId === config.recordId)
    if (existingIndex >= 0) {
      permissionConfigs.value[existingIndex] = config
    } else {
      permissionConfigs.value.push(config)
    }

    // 同时更新记录的权限状态
    const record = records.value.find(r => r.id === config.recordId)
    if (record) {
      record.permissions = { ...config.permissions }
      record.updateTime = new Date().toISOString()
    }

    saveToStorage()
  }

  // 获取权限配置
  const getPermissionConfig = (recordId: string): DataLineagePermissionConfig | null => {
    return permissionConfigs.value.find(c => c.recordId === recordId) || null
  }

  // 检查是否有权限
  const hasPermission = (recordId: string, permission: keyof DataLineageRecord['permissions']): boolean => {
    const record = records.value.find(r => r.id === recordId)
    return record?.permissions[permission] || false
  }

  // 新增血缘关系
  const addDataLineage = (formData: DataLineageFormData): string => {
    const newId = `lineage_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const currentTime = new Date().toISOString()
    
    // 生成随机标签
    const tagTexts = ['核心', '重要', '一般', '临时']
    const tagIndex = Math.floor(Math.random() * tagTexts.length)
    const colorIndex = Math.floor(Math.random() * TAG_COLORS.length)
    
    const newRecord: DataLineageRecord = {
      id: newId,
      sequence: records.value.length + 1,
      tag: {
        color: TAG_COLORS[colorIndex],
        text: tagTexts[tagIndex]
      },
      sourceTable: formData.sourceTable,
      sourceField: formData.sourceField,
      sourceCategory: formData.sourceCategory,
      targetTable: formData.targetTable,
      targetField: formData.targetField,
      targetCategory: formData.targetCategory,
      transformType: formData.transformType,
      transformLogic: formData.transformLogic,
      dependency: formData.dependency,
      dataVolume: `${Math.floor(Math.random() * 10000) + 1000}条`, // 模拟数据量
      responsible: formData.responsible,
      dataVersion: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-'),
      remark: formData.remark,
      permissions: {
        view: formData.permissions.view,
        edit: formData.permissions.edit,
        delete: formData.permissions.delete,
        editPermission: true // 创建者默认有编辑权限权限
      },
      isFavorite: false,
      createTime: currentTime,
      updateTime: currentTime
    }
    
    records.value.push(newRecord)
    saveToStorage()
    return newId
  }

  // 编辑血缘关系
  const updateDataLineage = (id: string, formData: DataLineageFormData): boolean => {
    const index = records.value.findIndex(r => r.id === id)
    if (index === -1) return false
    
    const existingRecord = records.value[index]
    const updatedRecord: DataLineageRecord = {
      ...existingRecord,
      sourceTable: formData.sourceTable,
      sourceField: formData.sourceField,
      sourceCategory: formData.sourceCategory,
      targetTable: formData.targetTable,
      targetField: formData.targetField,
      targetCategory: formData.targetCategory,
      transformType: formData.transformType,
      transformLogic: formData.transformLogic,
      dependency: formData.dependency,
      responsible: formData.responsible,
      remark: formData.remark,
      permissions: {
        ...existingRecord.permissions,
        view: formData.permissions.view,
        edit: formData.permissions.edit,
        delete: formData.permissions.delete
      },
      updateTime: new Date().toISOString(),
      dataVersion: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-')
    }
    
    records.value[index] = updatedRecord
    saveToStorage()
    return true
  }

  // 删除血缘关系
  const deleteDataLineage = (id: string): boolean => {
    const index = records.value.findIndex(r => r.id === id)
    if (index === -1) return false
    
    records.value.splice(index, 1)
    // 重新排序
    records.value.forEach((record, idx) => {
      record.sequence = idx + 1
    })
    
    // 同时删除相关的权限配置
    const permissionIndex = permissionConfigs.value.findIndex(p => p.recordId === id)
    if (permissionIndex !== -1) {
      permissionConfigs.value.splice(permissionIndex, 1)
    }
    
    saveToStorage()
    return true
  }

  // 根据ID获取记录
  const getRecordById = (id: string): DataLineageRecord | null => {
    return records.value.find(r => r.id === id) || null
  }

  // 初始化
  loadFromStorage()

  return {
    records,
    filteredRecords,
    searchKeyword,
    selectedRecords,
    permissionConfigs,
    toggleFavorite,
    updatePermissionConfig,
    addDataLineage,
    updateDataLineage,
    deleteDataLineage,
    getRecordById,
    getPermissionConfig,
    hasPermission,
    saveToStorage,
    loadFromStorage
  }
}
