// 数据血缘管理相关类型定义

export interface DataLineageRecord {
  id: string
  sequence: number
  tag: {
    color: string
    text: string
  }
  sourceTable: string
  sourceField: string
  sourceCategory: string
  targetTable: string
  targetField: string
  targetCategory: string
  transformType: string
  transformLogic: string
  dependency: string
  dataVolume: string
  responsible: string
  dataVersion: string
  remark: string
  permissions: {
    view: boolean
    edit: boolean
    delete: boolean
    editPermission: boolean
  }
  isFavorite: boolean
  createTime: string
  updateTime: string
}

export interface DataLineagePermissionConfig {
  recordId: string
  permissions: {
    view: boolean
    edit: boolean
    delete: boolean
    editPermission: boolean
  }
  authorizedPersons: string[]
  updateTime: string
}

export interface DataLineageSearchForm {
  keyword: string
  sourceCategory: string
  targetCategory: string
  responsible: string
}

// 版本管理相关接口
export interface DataLineageVersion {
  id: string
  recordId: string
  version: string
  createTime: string
  description: string
  config: DataLineageRecord
  operator: string
}

export interface VersionManagementForm {
  selectedRecords: string[]
  targetVersion: string
  targetTime: string
}

// 访问记录接口
export interface AccessRecord {
  id: string
  userId: string
  userName: string
  recordId: string
  recordTitle: string
  actionType: 'view' | 'query' | 'edit' | 'delete'
  actionDescription: string
  sourceTable: string
  targetTable: string
  accessTime: string
  duration: number // 访问时长（秒）
  ipAddress: string
  userAgent: string
  details?: {
    searchKeywords?: string
    viewedFields?: string[]
    operationResult?: 'success' | 'failed'
    errorMessage?: string
  }
}

// 新增/编辑表单接口
export interface DataLineageFormData {
  sourceTable: string
  sourceField: string
  sourceCategory: string
  targetTable: string
  targetField: string
  targetCategory: string
  transformType: string
  transformLogic: string
  dependency: string
  responsible: string
  remark: string
  permissions: {
    view: boolean
    edit: boolean
    delete: boolean
  }
}

// 权限标签配置
export const PERMISSION_LABELS = {
  view: '查看',
  edit: '编辑', 
  delete: '删除',
  editPermission: '编辑权限'
}

// 分类选项
export const CATEGORY_OPTIONS = [
  { label: '全部', value: '' },
  { label: 'INT', value: 'INT' },
  { label: 'FLOAT', value: 'FLOAT' },
  { label: 'DECIMAL', value: 'DECIMAL' },
  { label: 'VARCHAR', value: 'VARCHAR' },
  { label: 'DATE', value: 'DATE' },
  { label: 'BOOL', value: 'BOOL' }
]

// 字段类型选项（不包含全部）
export const FIELD_TYPE_OPTIONS = [
  { label: 'INT', value: 'INT' },
  { label: 'FLOAT', value: 'FLOAT' },
  { label: 'DECIMAL', value: 'DECIMAL' },
  { label: 'VARCHAR', value: 'VARCHAR' },
  { label: 'DATE', value: 'DATE' },
  { label: 'BOOL', value: 'BOOL' }
]

// 转换类型选项
export const TRANSFORM_TYPE_OPTIONS = [
  { label: '清洗 (Cleaning)', value: 'cleaning' },
  { label: '转换 (Transform)', value: 'transform' },
  { label: '聚合 (Aggregate)', value: 'aggregate' },
  { label: '过滤 (Filter)', value: 'filter' },
  { label: '连接 (Join)', value: 'join' },
  { label: '分组 (Group)', value: 'group' },
  { label: '排序 (Sort)', value: 'sort' },
  { label: '去重 (Distinct)', value: 'distinct' }
]

// 表名选项
export const TABLE_OPTIONS = [
  { label: 'user_info', value: 'user_info' },
  { label: 'order_detail', value: 'order_detail' },
  { label: 'product_info', value: 'product_info' },
  { label: 'department', value: 'department' },
  { label: 'employee', value: 'employee' },
  { label: 'customer', value: 'customer' },
  { label: 'payment', value: 'payment' },
  { label: 'inventory', value: 'inventory' },
  { label: 'supplier', value: 'supplier' },
  { label: 'category', value: 'category' }
]

// 标签颜色配置
export const TAG_COLORS = [
  '#f56c6c', // 红色
  '#e6a23c', // 橙色  
  '#409eff', // 蓝色
  '#67c23a', // 绿色
  '#909399', // 灰色
  '#c71585', // 紫红色
  '#ff69b4', // 粉色
  '#32cd32'  // 青绿色
]

// 负责人选项
export const RESPONSIBLE_OPTIONS = [
  '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
  '郑十一', '王十二', '冯十三', '陈十四', '褚十五', '卫十六'
]

// localStorage 存储键
export const STORAGE_KEYS = {
  DATA_LINEAGE_RECORDS: 'DATA_LINEAGE_RECORDS',
  DATA_LINEAGE_PERMISSIONS: 'DATA_LINEAGE_PERMISSIONS'
}
