<script setup lang="ts" name="commonDataTypeManagement">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Star, Plus, Download, Upload, User, Clock, Refresh, DataAnalysis, Coin, UploadFilled, ArrowDown, ArrowRight } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/useUserStore'

// 路由实例
const router = useRouter()

// 用户store
const userStore = useUserStore()

// 获取当前操作人姓名
const getCurrentOperatorName = (): string => {
  const userInfo = userStore.getUserInfo
  if (userInfo) {
    // 优先使用 displayName，其次使用 name，最后使用 account
    return userInfo.displayName || userInfo.name || userInfo.account || '未知用户'
  }
  return '未知用户'
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const tableData = ref<any[]>([])
const filteredData = ref<any[]>([])
const selectedRows = ref<any[]>([])

// 搜索表单
const searchForm = ref({
  dataTypeName: '',
  category: '',
  importance: ''
})

const searchFormProp = ref([
  { label: '数据类型名称', prop: 'dataTypeName', type: 'text' },
  {
    label: '分类',
    prop: 'category',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '数据型', value: '数据型' },
      { label: '字符型', value: '字符型' },
      { label: '日期型', value: '日期型' },
      { label: '布尔型', value: '布尔型' },
      { label: '其他型', value: '其他型' }
    ]
  },
  {
    label: '重要性',
    prop: 'importance',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '高', value: '高' },
      { label: '中', value: '中' },
      { label: '低', value: '低' }
    ]
  }
])

// 表格配置
const tableRef = ref()
const tableHeight = ref(0)

// 表头配置
const columns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'tag', label: '标签', minWidth: 120 },
  { prop: 'dataTypeName', label: '数据类型名称', minWidth: 150 },
  { prop: 'category', label: '分类', minWidth: 100 },
  { prop: 'dataVolume', label: '数据量', minWidth: 100, sortable: true },
  { prop: 'permissions', label: '权限', minWidth: 150 },
  { prop: 'favorite', label: '收藏', minWidth: 120, sortable: true },
  { prop: 'importance', label: '重要性', minWidth: 100, sortable: true },
  { prop: 'remark', label: '备注', minWidth: 200 }
]

// 历史记录表头配置
const historyColumns = [
  { prop: 'sequence', label: '序号', width: 80 },
  { prop: 'dataTypeName', label: '字段名称', width: 180 },
  { prop: 'operationType', label: '变更类型', width: 120 },
  { prop: 'originalData', label: '变更前', minWidth: 200 },
  { prop: 'newData', label: '变更后', minWidth: 200 },
  { prop: 'operator', label: '操作人员', width: 120 },
  { prop: 'operationTime', label: '操作时间', width: 180 }
]

// 访问记录表头配置
const accessColumns = [
  { prop: 'sequence', label: '序号', width: 80 },
  { prop: 'dataTypeName', label: '数据类型', width: 120 },
  { prop: 'accessType', label: '访问类型', width: 100 },
  { prop: 'accessUser', label: '访问用户', width: 120 },
  { prop: 'accessTime', label: '访问时间', width: 180 },
  { prop: 'duration', label: '访问时长', width: 100 },
  { prop: 'ipAddress', label: 'IP地址', width: 140 },
  { prop: 'userAgent', label: '浏览器', minWidth: 200 },
  { prop: 'accessResult', label: '访问结果', width: 100 }
]

// 操作按钮定义 - TableV2 格式
const buttons = [
  { label: '查看', code: 'view', type: 'info' },
  { label: '编辑', code: 'edit', type: 'primary' },
  { label: '删除', code: 'delete', type: 'danger', popconfirm: '确认删除该数据类型吗？' },
  { label: '导出', code: 'export', more: true },
  { label: '分享', code: 'share', more: true },
  { label: '复制', code: 'copy', more: true }
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 列表请求参数
const reqParams = reactive({
  dataTypeName: '',
  category: '',
  importance: '',
  skipCount: 0,
  maxResultCount: 10
})

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const currentRow = ref<any>(null)
const isViewMode = ref(false)

// 表单数据
const dialogForm = ref({
  dataTypeName: '',
  dataTypeDescription: '',
  category: '',
  storageRequirement: '',
  valueRange: '',
  defaultValue: '',
  constraints: '',
  permissions: [] as string[]
})

// 表单属性
const dialogFormProps = ref([
  { label: '数据类型名称', prop: 'dataTypeName', type: 'text', required: true },
  { label: '数据类型描述', prop: 'dataTypeDescription', type: 'textarea' },
  {
    label: '类型分类',
    prop: 'category',
    type: 'select',
    options: [
      { label: '数据型', value: '数据型' },
      { label: '字符型', value: '字符型' },
      { label: '日期型', value: '日期型' },
      { label: '布尔型', value: '布尔型' },
      { label: '其他型', value: '其他型' }
    ]
  },
  { label: '存储需求', prop: 'storageRequirement', type: 'text' },
  { label: '数值范围', prop: 'valueRange', type: 'text' },
  { label: '默认值', prop: 'defaultValue', type: 'text' },
  { label: '约束条件', prop: 'constraints', type: 'textarea' },
  {
    label: '权限',
    prop: 'permissions',
    type: 'checkbox',
    options: [
      { label: '访问', value: '访问' },
      { label: '编辑', value: '编辑' },
      { label: '删除', value: '删除' }
    ]
  }
])

// 表单验证规则
const dialogFormRules = {
  dataTypeName: [{ required: true, message: '请输入数据类型名称', trigger: 'blur' }]
}

// 本地存储键
const STORAGE_KEY = 'commonDataTypeManagement'

// 统计数据
const statisticsData = ref<any>({})

// 导入相关数据
const showImportDialog = ref(false)
const importLoading = ref(false)
const uploadFileList = ref<any[]>([])
const importProgress = ref(0)
const importErrors = ref<string[]>([])

// 历史记录相关数据
const showHistoryDialog = ref(false)
const historyData = ref<any[]>([])
const historyPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 访问记录相关数据
const showAccessDialog = ref(false)
const accessData = ref<any[]>([])
const accessPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 版本管理相关数据
const showVersionDialog = ref(false)
const selectedDataTypes = ref<string[]>([])
const availableDataTypes = ref<string[]>([])
const selectedDataTypeName = ref('')
const rollbackDate = ref('')
const rollbackTime = ref('')
const versionChangeRecords = ref<any[]>([])
const versionRecordsPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 生成历史记录数据（数据库表结构变更记录）
const generateHistoryData = () => {
  const operators = ['张三', '李四', '王五', '赵六', '钱七', '孙八']
  const currentUserName = getCurrentOperatorName()
  const operationTypes = ['字段扩展', '字段新增', '字段删除', '约束添加', '字段重命名', '字段调整', '外键设置', '字段禁用', '类型变更']

  // 预定义的数据库结构变更记录模板
  const changeTemplates = [
    {
      type: '字段扩展',
      fieldName: 'username',
      description: '对 VARCHAR(50) 类型的 username 字段的最大长度进行了扩展至 VARCHAR(100)，以适应更长的内容输入需求。',
      originalData: 'VARCHAR(50)',
      newData: 'VARCHAR(100)'
    },
    {
      type: '字段新增',
      fieldName: 'user_status',
      description: '增加了对用户状态的支持，新增一种可选值 \'temporary_hold\' 用于表示临时冻结状态。',
      originalData: '',
      newData: 'ENUM(\'active\', \'inactive\', \'temporary_hold\')'
    },
    {
      type: '字段删除',
      fieldName: 'is_deleted',
      description: '移除了布尔类型的 is_deleted 字段，该字段的功能已被新的 status ENUM(\'active\', \'inactive\') 结构所替代。',
      originalData: 'BOOLEAN DEFAULT FALSE',
      newData: ''
    },
    {
      type: '约束添加',
      fieldName: 'email',
      description: '在 email 字段上添加了唯一性约束，防止重复数据出现。',
      originalData: 'VARCHAR(255)',
      newData: 'VARCHAR(255) UNIQUE'
    },
    {
      type: '字段调整',
      fieldName: 'created_at',
      description: '调整了 created_at TIMESTAMP 字段的默认值设置，使其自动填充为当前操作时间。',
      originalData: 'TIMESTAMP',
      newData: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
    },
    {
      type: '字段重命名',
      fieldName: 'reg_time → registration_time',
      description: '将 reg_time DATETIME 字段重命名为更具语义化的 registration_time DATETIME 字段，使名称更加清晰。',
      originalData: 'reg_time DATETIME',
      newData: 'registration_time DATETIME'
    },
    {
      type: '字段调整',
      fieldName: 'price',
      description: '调整了 price DECIMAL(10,2) 字段在数据库表中的顺序，将其置于 description TEXT 字段之后，以更符合业务逻辑展示习惯。',
      originalData: '位置：第3列',
      newData: '位置：第5列（description之后）'
    },
    {
      type: '外键设置',
      fieldName: 'category_id',
      description: '为 category_id INT 字段设置了外键依赖于 Category.id，并启用了级联更新机制，确保关联数据的一致性。',
      originalData: 'INT',
      newData: 'INT FOREIGN KEY REFERENCES Category(id) ON UPDATE CASCADE'
    },
    {
      type: '类型变更',
      fieldName: 'amount',
      description: '将 amount FLOAT 字段的数据类型从浮点型改为高精度小数类型 DECIMAL(15,2)，以提升计算准确性。',
      originalData: 'FLOAT',
      newData: 'DECIMAL(15,2)'
    },
    {
      type: '字段禁用',
      fieldName: 'two_factor_auth',
      description: '将 two_factor_auth BOOLEAN 字段标记为禁用状态，在前端界面中不再显示该字段内容，并停止其功能支持。',
      originalData: 'BOOLEAN DEFAULT FALSE（启用）',
      newData: 'BOOLEAN DEFAULT FALSE（禁用）'
    }
  ]

  // 额外生成一些模拟的变更记录
  const additionalTemplates = [
    {
      type: '字段扩展',
      fieldName: 'description',
      description: '将 description VARCHAR(255) 字段扩展为 TEXT 类型，支持更长的描述内容存储。',
      originalData: 'VARCHAR(255)',
      newData: 'TEXT'
    },
    {
      type: '字段新增',
      fieldName: 'last_login_ip',
      description: '新增 last_login_ip VARCHAR(45) 字段，用于记录用户最后登录的IP地址信息。',
      originalData: '',
      newData: 'VARCHAR(45)'
    },
    {
      type: '约束添加',
      fieldName: 'phone',
      description: '为 phone 字段添加格式验证约束，确保电话号码格式的正确性。',
      originalData: 'VARCHAR(20)',
      newData: 'VARCHAR(20) CHECK (phone REGEXP \'^[0-9]{11}$\')'
    },
    {
      type: '字段调整',
      fieldName: 'updated_at',
      description: '设置 updated_at 字段在记录更新时自动更新时间戳。',
      originalData: 'TIMESTAMP',
      newData: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    },
    {
      type: '类型变更',
      fieldName: 'age',
      description: '将 age TINYINT 字段改为 SMALLINT 类型，扩大年龄值的存储范围。',
      originalData: 'TINYINT',
      newData: 'SMALLINT'
    },
    {
      type: '字段删除',
      fieldName: 'temp_token',
      description: '移除临时令牌字段 temp_token，该功能已迁移至独立的令牌管理表中。',
      originalData: 'VARCHAR(128)',
      newData: ''
    },
    {
      type: '外键设置',
      fieldName: 'department_id',
      description: '为 department_id 字段建立与 Department 表的外键关联，并设置级联删除。',
      originalData: 'INT',
      newData: 'INT FOREIGN KEY REFERENCES Department(id) ON DELETE CASCADE'
    },
    {
      type: '字段重命名',
      fieldName: 'create_time → created_timestamp',
      description: '将 create_time 字段重命名为 created_timestamp，使命名更加规范统一。',
      originalData: 'create_time DATETIME',
      newData: 'created_timestamp DATETIME'
    }
  ]

  const allTemplates = [...changeTemplates, ...additionalTemplates]
  const history = []
  const now = new Date()

  // 生成18条历史记录
  for (let i = 0; i < 18; i++) {
    const template = allTemplates[i % allTemplates.length]

    // 随机选择操作人员，有30%概率是当前用户
    let operator: string
    if (Math.random() < 0.3) {
      operator = currentUserName
    } else {
      operator = operators[Math.floor(Math.random() * operators.length)]
    }

    // 生成操作时间（最近60天内）
    const operationTime = new Date(now.getTime() - Math.random() * 60 * 24 * 60 * 60 * 1000)

    history.push({
      id: i + 1,
      sequence: i + 1,
      dataTypeName: template.fieldName,
      operationType: template.type,
      originalData: template.originalData,
      newData: template.newData,
      operator,
      operationTime: operationTime.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      description: template.description
    })
  }

  // 按时间倒序排列
  return history.sort((a, b) => new Date(b.operationTime).getTime() - new Date(a.operationTime).getTime())
}

// 生成访问记录数据
const generateAccessData = () => {
  const accessUsers = ['张三', '李四', '王五', '赵六', '钱七', '孙八']
  const currentUserName = getCurrentOperatorName()
  const accessTypes = ['查看详情', '搜索查询']
  const dataTypeNames = [
    'INT', 'VARCHAR', 'DECIMAL', 'DATE', 'BOOLEAN', 'TEXT', 'FLOAT', 'TIMESTAMP',
    'CHAR', 'BLOB', 'JSON', 'XML', 'BINARY', 'LONGTEXT', 'DOUBLE'
  ]
  const ipAddresses = [
    '*************', '*************', '*************', '*************',
    '*********', '*********', '***********', '***********'
  ]
  const userAgents = [
    'Chrome 120.0.0.0 (Windows)',
    'Firefox ********* (Windows)',
    'Edge 120.0.0.0 (Windows)',
    'Safari ******** (macOS)',
    'Chrome 120.0.0.0 (macOS)'
  ]

  const accessRecords = []
  const now = new Date()

  // 生成25条访问记录
  for (let i = 0; i < 25; i++) {
    // 随机选择访问用户，有40%概率是当前用户
    let accessUser: string
    if (Math.random() < 0.4) {
      accessUser = currentUserName
    } else {
      accessUser = accessUsers[Math.floor(Math.random() * accessUsers.length)]
    }

    const accessType = accessTypes[Math.floor(Math.random() * accessTypes.length)]
    const dataTypeName = dataTypeNames[Math.floor(Math.random() * dataTypeNames.length)]
    const ipAddress = ipAddresses[Math.floor(Math.random() * ipAddresses.length)]
    const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)]

    // 生成访问时间（最近30天内）
    const accessTime = new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000)

    // 生成访问时长（1-300秒）
    const duration = Math.floor(Math.random() * 300) + 1

    accessRecords.push({
      id: i + 1,
      sequence: i + 1,
      dataTypeName,
      accessType,
      accessUser,
      accessTime: accessTime.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      duration: `${duration}秒`,
      ipAddress,
      userAgent,
      accessResult: Math.random() > 0.1 ? '成功' : '失败' // 90%成功率
    })
  }

  // 按时间倒序排列
  return accessRecords.sort((a, b) => new Date(b.accessTime).getTime() - new Date(a.accessTime).getTime())
}

// 生成统计数据
const generateStatisticsData = (row: any) => {
  const creators = ['张三', '李四', '王五', '赵六', '钱七', '孙八']
  const dataSources = ['数据库A', '数据库B', '外部API', '文件导入', '手动录入']
  const updateFrequencies = ['实时', '每小时', '每日', '每周', '每月']

  return {
    creator: creators[Math.floor(Math.random() * creators.length)],
    createTime: row.createTime,
    lastModifyDate: new Date(new Date(row.createTime).getTime() + Math.floor(Math.random() * 7) * 24 * 60 * 60 * 1000).toISOString(),
    dataVolume: row.dataVolume,
    averageValue: row.category === '数据型' ? (Math.random() * 1000).toFixed(2) : 'N/A',
    medianValue: row.category === '数据型' ? (Math.random() * 1000).toFixed(2) : 'N/A',
    modeValue: row.category === '数据型' ? Math.floor(Math.random() * 100) : row.category === '字符型' ? '常用值' : 'N/A',
    variance: row.category === '数据型' ? (Math.random() * 100).toFixed(2) : 'N/A',
    dataSource: dataSources[Math.floor(Math.random() * dataSources.length)],
    updateFrequency: updateFrequencies[Math.floor(Math.random() * updateFrequencies.length)]
  }
}

// 页面级更多操作处理
const handleMoreTopMenuClick = (command: string) => {
  switch (command) {
    case 'history':
      handleShowHistory()
      break
    case 'access':
      handleShowAccess()
      break
    case 'version':
      handleShowVersionManagement()
      break
    default:
      ElMessage.info(`功能 ${command} 正在开发中...`)
  }
}

// 显示历史记录
const handleShowHistory = () => {
  const allHistoryData = generateHistoryData()
  historyData.value = allHistoryData
  historyPagination.total = allHistoryData.length
  historyPagination.page = 1
  showHistoryDialog.value = true
}

// 显示访问记录
const handleShowAccess = () => {
  // 如果访问记录数据为空，则生成初始数据
  if (accessData.value.length === 0) {
    const allAccessData = generateAccessData()
    accessData.value = allAccessData
    accessPagination.total = allAccessData.length
  }

  accessPagination.page = 1
  showAccessDialog.value = true

  console.log('显示访问记录，总数:', accessData.value.length)
  console.log('访问记录数据:', accessData.value.slice(0, 3)) // 显示前3条记录
}

// 添加访问记录
const addAccessRecord = (dataTypeName: string, accessType: string) => {
  const currentUserName = getCurrentOperatorName()
  const now = new Date()

  // 模拟IP地址和浏览器信息
  const ipAddresses = ['*************', '*********', '***********']
  const userAgents = [
    'Chrome 120.0.0.0 (Windows)',
    'Firefox ********* (Windows)',
    'Edge 120.0.0.0 (Windows)'
  ]

  const newRecord = {
    id: Date.now(),
    sequence: 1, // 新记录总是排在第一位
    dataTypeName,
    accessType,
    accessUser: currentUserName,
    accessTime: now.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }),
    duration: '0秒', // 初始为0，可以后续更新
    ipAddress: ipAddresses[Math.floor(Math.random() * ipAddresses.length)],
    userAgent: userAgents[Math.floor(Math.random() * userAgents.length)],
    accessResult: '成功'
  }

  // 如果访问记录数据还没有初始化，先初始化
  if (accessData.value.length === 0) {
    accessData.value = generateAccessData()
  }

  // 添加新记录到开头
  accessData.value.unshift(newRecord)
  accessPagination.total = accessData.value.length

  // 重新排序序号
  accessData.value.forEach((record, index) => {
    record.sequence = index + 1
  })

  console.log('添加访问记录:', newRecord)
  console.log('当前访问记录总数:', accessData.value.length)
}

// 获取当前页历史数据
const getCurrentPageHistoryData = () => {
  const start = (historyPagination.page - 1) * historyPagination.size
  const end = start + historyPagination.size
  return historyData.value.slice(start, end)
}

// 历史记录分页变化
const onHistoryPaginationChange = (value: number, type: 'page' | 'size') => {
  if (type === 'page') {
    historyPagination.page = value
  } else {
    historyPagination.size = value
    historyPagination.page = 1
  }
}

// 获取当前页访问数据
const getCurrentPageAccessData = () => {
  const start = (accessPagination.page - 1) * accessPagination.size
  const end = start + accessPagination.size
  return accessData.value.slice(start, end)
}

// 访问记录分页变化
const onAccessPaginationChange = (value: number, type: 'page' | 'size') => {
  if (type === 'page') {
    accessPagination.page = value
  } else {
    accessPagination.size = value
    accessPagination.page = 1
  }
}

// 获取变更类型对应的颜色
const getChangeTypeColor = (type: string): 'success' | 'primary' | 'danger' | 'warning' | 'info' => {
  const colorMap: Record<string, 'success' | 'primary' | 'danger' | 'warning' | 'info'> = {
    '字段新增': 'success',
    '字段扩展': 'primary',
    '字段删除': 'danger',
    '约束添加': 'warning',
    '字段重命名': 'info',
    '字段调整': 'warning',
    '外键设置': 'primary',
    '字段禁用': 'danger',
    '类型变更': 'warning'
  }
  return colorMap[type] || 'info'
}

// 获取访问类型对应的颜色
const getAccessTypeColor = (type: string): 'success' | 'primary' | 'danger' | 'warning' | 'info' => {
  const colorMap: Record<string, 'success' | 'primary' | 'danger' | 'warning' | 'info'> = {
    '查看详情': 'info',
    '搜索查询': 'primary'
  }
  return colorMap[type] || 'info'
}

// 显示版本管理弹窗
const handleShowVersionManagement = () => {
  // 初始化可选数据类型列表
  availableDataTypes.value = tableData.value.map(item => item.dataTypeName)

  // 清空之前的选择
  selectedDataTypes.value = []
  selectedDataTypeName.value = ''
  rollbackDate.value = ''
  rollbackTime.value = ''

  // 加载已保存的版本更改记录
  loadVersionChangeRecords()

  showVersionDialog.value = true
}

// 添加数据类型到已选择列表
const addDataType = () => {
  if (selectedDataTypeName.value && !selectedDataTypes.value.includes(selectedDataTypeName.value)) {
    selectedDataTypes.value.push(selectedDataTypeName.value)
    selectedDataTypeName.value = ''
  }
}

// 移除已选择的数据类型
const removeDataType = (dataType: string) => {
  const index = selectedDataTypes.value.indexOf(dataType)
  if (index > -1) {
    selectedDataTypes.value.splice(index, 1)
  }
}

// 执行版本回溯
const executeRollback = () => {
  if (selectedDataTypes.value.length === 0) {
    ElMessage.warning('请至少选择一个数据类型')
    return
  }

  if (!rollbackDate.value || !rollbackTime.value) {
    ElMessage.warning('请选择回溯时间')
    return
  }

  // 生成回溯记录
  const rollbackRecord = generateVersionRecord('回溯')
  versionChangeRecords.value.unshift(rollbackRecord)

  // 保存到localStorage
  saveVersionChangeRecords()

  // 更新分页信息
  versionRecordsPagination.total = versionChangeRecords.value.length

  ElMessage.success('版本回溯执行成功')

  // 清空选择
  selectedDataTypes.value = []
  rollbackDate.value = ''
  rollbackTime.value = ''
}

// 执行版本删除
const executeDeleteVersion = () => {
  if (selectedDataTypes.value.length === 0) {
    ElMessage.warning('请至少选择一个数据类型')
    return
  }

  if (!rollbackDate.value || !rollbackTime.value) {
    ElMessage.warning('请选择要删除的版本时间')
    return
  }

  // 确认删除操作
  ElMessageBox.confirm(
    `确认删除 ${selectedDataTypes.value.join('、')} 在 ${rollbackDate.value} ${rollbackTime.value} 时间点的版本吗？`,
    '删除版本确认',
    {
      confirmButtonText: '确认删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 生成删除记录
    const deleteRecord = generateVersionRecord('删除')
    versionChangeRecords.value.unshift(deleteRecord)

    // 保存到localStorage
    saveVersionChangeRecords()

    // 更新分页信息
    versionRecordsPagination.total = versionChangeRecords.value.length

    ElMessage.success('版本删除成功')

    // 清空选择
    selectedDataTypes.value = []
    rollbackDate.value = ''
    rollbackTime.value = ''
  }).catch(() => {
    ElMessage.info('已取消删除操作')
  })
}

// 生成版本操作记录
const generateVersionRecord = (operationType: '回溯' | '删除') => {
  const now = new Date()
  const targetDateTime = new Date(`${rollbackDate.value} ${rollbackTime.value}`)
  const currentUserName = getCurrentOperatorName()

  // 根据操作类型生成不同的变更类型
  const changeTypes = operationType === '回溯'
    ? ['字段类型回溯', '字段长度回溯', '约束条件回溯', '默认值回溯']
    : ['版本删除', '数据清理', '历史清除', '记录移除']

  const originalValues = ['VARCHAR(100)', 'INT AUTO_INCREMENT', 'DECIMAL(10,2)', 'BOOLEAN DEFAULT TRUE']
  const newValues = operationType === '回溯'
    ? ['VARCHAR(50)', 'INT', 'DECIMAL(8,2)', 'BOOLEAN DEFAULT FALSE']
    : ['已删除', '已删除', '已删除', '已删除']

  const changes = selectedDataTypes.value.map((dataType, index) => {
    const changeType = changeTypes[index % changeTypes.length]
    const originalValue = originalValues[index % originalValues.length]
    const newValue = newValues[index % newValues.length]

    return {
      dataTypeName: dataType,
      changeType,
      originalValue,
      newValue,
      description: operationType === '回溯'
        ? `将 ${dataType} 数据类型从 ${originalValue} 回溯到 ${newValue}`
        : `删除 ${dataType} 在 ${targetDateTime.toLocaleString('zh-CN')} 时间点的版本记录`
    }
  })

  const description = operationType === '回溯'
    ? `对 ${selectedDataTypes.value.join('、')} 等 ${selectedDataTypes.value.length} 个数据类型执行了版本回溯操作，回溯到 ${targetDateTime.toLocaleString('zh-CN')} 时间点的状态。`
    : `删除了 ${selectedDataTypes.value.join('、')} 等 ${selectedDataTypes.value.length} 个数据类型在 ${targetDateTime.toLocaleString('zh-CN')} 时间点的版本记录。`

  return {
    id: Date.now(),
    operationType,
    rollbackTime: targetDateTime.toLocaleString('zh-CN'),
    operationTime: now.toLocaleString('zh-CN'),
    operator: currentUserName,
    selectedDataTypes: [...selectedDataTypes.value],
    changes,
    description
  }
}

// 保存版本更改记录到localStorage
const saveVersionChangeRecords = () => {
  try {
    localStorage.setItem('commonDataType_versionChangeRecords', JSON.stringify(versionChangeRecords.value))
  } catch (error) {
    console.error('保存版本更改记录失败:', error)
  }
}

// 加载版本更改记录
const loadVersionChangeRecords = () => {
  try {
    const saved = localStorage.getItem('commonDataType_versionChangeRecords')
    if (saved) {
      versionChangeRecords.value = JSON.parse(saved)
      versionRecordsPagination.total = versionChangeRecords.value.length
    }
  } catch (error) {
    console.error('加载版本更改记录失败:', error)
    versionChangeRecords.value = []
  }
}

// 获取当前页版本记录数据
const getCurrentPageVersionRecords = () => {
  const start = (versionRecordsPagination.page - 1) * versionRecordsPagination.size
  const end = start + versionRecordsPagination.size
  return versionChangeRecords.value.slice(start, end)
}

// 版本记录分页变化
const onVersionRecordsPaginationChange = (value: number, type: 'page' | 'size') => {
  if (type === 'page') {
    versionRecordsPagination.page = value
  } else {
    versionRecordsPagination.size = value
    versionRecordsPagination.page = 1
  }
}

// 返回上级页面
const handleGoBack = () => {
  router.push('/reportIntegrationSpotCheck')
}

// 块高度变化事件
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 120
}

// 生成模拟数据
const generateMockData = () => {
  const categories = ['数据型', '字符型', '日期型', '布尔型', '其他型']
  const importanceLevels = ['高', '中', '低']
  const tagColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
  const dataTypeNames = [
    'INT', 'VARCHAR', 'DECIMAL', 'DATE', 'BOOLEAN', 'TEXT', 'FLOAT', 'TIMESTAMP', 'CHAR', 'BLOB'
  ]

  const mockData = []
  const now = new Date()

  for (let i = 1; i <= 50; i++) {
    const category = categories[Math.floor(Math.random() * categories.length)]
    const importance = importanceLevels[Math.floor(Math.random() * importanceLevels.length)]
    const dataTypeName = dataTypeNames[Math.floor(Math.random() * dataTypeNames.length)]

    // 创建时间：从现在往前推随机天数
    const createTime = new Date(now.getTime() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000)

    mockData.push({
      id: i,
      index: i,
      tag: {
        text: `标签${i}`,
        color: tagColors[Math.floor(Math.random() * tagColors.length)]
      },
      dataTypeName: dataTypeName,
      category: category,
      dataVolume: Math.floor(Math.random() * 10000) + 1000,
      permissions: ['访问', '编辑', '删除'].slice(0, Math.floor(Math.random() * 3) + 1),
      favorite: Math.floor(Math.random() * 5) + 1,
      importance: importance,
      remark: `这是${dataTypeName}类型的备注信息`,
      dataTypeDescription: `${dataTypeName}数据类型的详细描述`,
      storageRequirement: `${Math.floor(Math.random() * 64) + 1}字节`,
      valueRange: category === '数据型' ? '0-999999' : category === '字符型' ? '1-255字符' : '标准范围',
      defaultValue: category === '数据型' ? '0' : category === '字符型' ? '空字符串' : '默认值',
      constraints: '非空约束，唯一性约束',
      createTime: createTime.toISOString()
    })
  }

  // 按创建时间降序排序（最新的在前面）
  return mockData.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
}

// 初始化数据
const initData = () => {
  const savedData = localStorage.getItem(STORAGE_KEY)
  if (savedData) {
    tableData.value = JSON.parse(savedData)
    // 确保已保存的数据也按创建时间排序
    tableData.value.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
  } else {
    tableData.value = generateMockData()
    saveToLocalStorage()
  }
  applyFilters()
}

// 保存到本地存储
const saveToLocalStorage = () => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(tableData.value))
}

// 应用筛选
const applyFilters = () => {
  let filtered = [...tableData.value]

  if (searchForm.value.dataTypeName) {
    filtered = filtered.filter(item =>
      item.dataTypeName.toLowerCase().includes(searchForm.value.dataTypeName.toLowerCase())
    )
  }

  if (searchForm.value.category) {
    filtered = filtered.filter(item => item.category === searchForm.value.category)
  }

  if (searchForm.value.importance) {
    filtered = filtered.filter(item => item.importance === searchForm.value.importance)
  }

  // 按创建时间降序排序
  filtered.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())

  filteredData.value = filtered
  pagination.total = filtered.length
  pagination.page = 1

  // 清空选中项
  selectedRows.value = []
}

// 获取当前页数据
const getCurrentPageData = () => {
  let data = [...filteredData.value]

  // 应用排序
  if (sortConfig.value.prop && sortConfig.value.order) {
    data = getSortedData(data)
  }

  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  pagination.total = data.length

  return data.slice(start, end).map((item, index) => ({
    ...item,
    index: start + index + 1
  }))
}

// 搜索
const onSearch = () => {
  pagination.page = 1
  reqParams.skipCount = 0
  reqParams.maxResultCount = pagination.size
  // 其他查询参数
  reqParams.dataTypeName = searchForm.value.dataTypeName
  reqParams.category = searchForm.value.category
  reqParams.importance = searchForm.value.importance

  // 添加搜索访问记录
  if (searchForm.value.dataTypeName) {
    addAccessRecord(searchForm.value.dataTypeName, '搜索查询')
  }

  applyFilters()
}

// 重置搜索
const onReset = () => {
  searchForm.value = {
    dataTypeName: '',
    category: '',
    importance: ''
  }
  reqParams.dataTypeName = ''
  reqParams.category = ''
  reqParams.importance = ''
  applyFilters()
}

// 新增
const onClickAdd = () => {
  currentRow.value = null
  isViewMode.value = false
  dialogForm.value = {
    dataTypeName: '',
    dataTypeDescription: '',
    category: '',
    storageRequirement: '',
    valueRange: '',
    defaultValue: '',
    constraints: '',
    permissions: []
  }
  showDialogForm.value = true
}

// 查看详情
const onView = (row: any) => {
  currentRow.value = row
  statisticsData.value = generateStatisticsData(row)
  dialogForm.value = {
    dataTypeName: row.dataTypeName,
    dataTypeDescription: row.dataTypeDescription,
    category: row.category,
    storageRequirement: row.storageRequirement,
    valueRange: row.valueRange,
    defaultValue: row.defaultValue,
    constraints: row.constraints,
    permissions: [...row.permissions]
  }
  isViewMode.value = true
  showDialogForm.value = true

  // 添加访问记录
  addAccessRecord(row.dataTypeName, '查看详情')
}

// 编辑
const onEdit = (row: any) => {
  currentRow.value = row
  dialogForm.value = {
    dataTypeName: row.dataTypeName,
    dataTypeDescription: row.dataTypeDescription,
    category: row.category,
    storageRequirement: row.storageRequirement,
    valueRange: row.valueRange,
    defaultValue: row.defaultValue,
    constraints: row.constraints,
    permissions: [...row.permissions]
  }
  isViewMode.value = false
  showDialogForm.value = true
}

// 删除
const onDelete = (row: any) => {
  ElMessageBox.confirm('确认删除该数据类型吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      saveToLocalStorage()
      applyFilters()
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 批量删除
const onBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }
  
  ElMessageBox.confirm(`确认删除选中的 ${selectedRows.value.length} 条数据吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const selectedIds = selectedRows.value.map(row => row.id)
    tableData.value = tableData.value.filter(item => !selectedIds.includes(item.id))
    saveToLocalStorage()
    applyFilters()
    selectedRows.value = []
    ElMessage.success('批量删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 表单提交
const onDialogConfirm = () => {
  dialogFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true
      
      if (currentRow.value) {
        // 编辑
        const index = tableData.value.findIndex(item => item.id === currentRow.value.id)
        if (index > -1) {
          Object.assign(tableData.value[index], {
            dataTypeName: dialogForm.value.dataTypeName,
            dataTypeDescription: dialogForm.value.dataTypeDescription,
            category: dialogForm.value.category,
            storageRequirement: dialogForm.value.storageRequirement,
            valueRange: dialogForm.value.valueRange,
            defaultValue: dialogForm.value.defaultValue,
            constraints: dialogForm.value.constraints,
            permissions: [...dialogForm.value.permissions],
            remark: `这是${dialogForm.value.dataTypeName}类型的备注信息`
          })
        }
        ElMessage.success('编辑成功')
      } else {
        // 新增
        const newId = Math.max(...tableData.value.map(item => item.id)) + 1
        const tagColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']

        const newItem = {
          id: newId,
          index: newId,
          tag: {
            text: `标签${newId}`,
            color: tagColors[Math.floor(Math.random() * tagColors.length)]
          },
          dataTypeName: dialogForm.value.dataTypeName,
          category: dialogForm.value.category,
          dataVolume: Math.floor(Math.random() * 10000) + 1000,
          permissions: [...dialogForm.value.permissions],
          favorite: Math.floor(Math.random() * 5) + 1,
          importance: ['高', '中', '低'][Math.floor(Math.random() * 3)],
          remark: `这是${dialogForm.value.dataTypeName}类型的备注信息`,
          dataTypeDescription: dialogForm.value.dataTypeDescription,
          storageRequirement: dialogForm.value.storageRequirement,
          valueRange: dialogForm.value.valueRange,
          defaultValue: dialogForm.value.defaultValue,
          constraints: dialogForm.value.constraints,
          createTime: new Date().toISOString()
        }

        // 将新项添加到数组开头（最新的在前面）
        tableData.value.unshift(newItem)
        ElMessage.success('新增成功')
      }

      saveToLocalStorage()
      applyFilters()
      showDialogForm.value = false
      loading.value = false
    }
  })
}

// 分页事件
const onPaginationChange = (val: number, type: string) => {
  if (type === 'page') {
    pagination.page = val
    reqParams.skipCount = (val - 1) * pagination.size
  } else {
    pagination.size = val
    pagination.page = 1
    reqParams.maxResultCount = pagination.size
    reqParams.skipCount = 0
  }
}

// 排序配置
const sortConfig = ref({
  prop: '',
  order: ''
})

// 表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 排序变化处理
const handleSortChange = ({ prop, order }: any) => {
  sortConfig.value = { prop, order }
}

// 获取排序后的数据
const getSortedData = (data: any[]) => {
  if (!sortConfig.value.prop || !sortConfig.value.order) {
    return data
  }

  return [...data].sort((a, b) => {
    const aVal = a[sortConfig.value.prop]
    const bVal = b[sortConfig.value.prop]

    if (sortConfig.value.order === 'ascending') {
      return aVal > bVal ? 1 : -1
    } else {
      return aVal < bVal ? 1 : -1
    }
  })
}



// 表格操作点击事件 - TableV2 格式统一处理
const onTableClickButton = ({row, btn}: any) => {
  if (btn.code === 'view') {
    onView(row)
  } else if (btn.code === 'edit') {
    onEdit(row)
  } else if (btn.code === 'delete') {
    onDelete(row)
  } else if (btn.code === 'export') {
    handleExportRow(row)
  } else if (btn.code === 'share') {
    handleShareRow(row)
  } else if (btn.code === 'copy') {
    handleCopyRow(row)
  }
}

// 更多操作处理（保留兼容性）
const handleMoreAction = (command: string, row: any) => {
  switch (command) {
    case 'export':
      handleExportRow(row)
      break
    case 'share':
      handleShareRow(row)
      break
    case 'copy':
      handleCopyRow(row)
      break
    default:
      ElMessage.info(`${command} 功能开发中...`)
  }
}

// 导出单行数据
const handleExportRow = async (row: any) => {
  try {
    ElMessage.info('正在导出数据...')

    // 动态导入ExcelJS和file-saver
    const [ExcelJS, FileSaver] = await Promise.all([
      import('exceljs'),
      import('file-saver')
    ])

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = '通用数据类型管理系统'
    workbook.lastModifiedBy = '系统'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('数据类型详情')

    // 设置列定义
    worksheet.columns = [
      { header: '序号', key: 'index', width: 10 },
      { header: '标签', key: 'tag', width: 15 },
      { header: '数据类型名称', key: 'dataTypeName', width: 20 },
      { header: '分类', key: 'category', width: 15 },
      { header: '数据量', key: 'dataVolume', width: 15 },
      { header: '权限', key: 'permissions', width: 20 },
      { header: '收藏', key: 'favorite', width: 10 },
      { header: '重要性', key: 'importance', width: 12 },
      { header: '备注', key: 'remark', width: 30 },
      { header: '创建时间', key: 'createTime', width: 20 }
    ]

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    }
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' }

    // 添加数据行
    worksheet.addRow({
      index: row.index,
      tag: row.tag.text,
      dataTypeName: row.dataTypeName,
      category: row.category,
      dataVolume: row.dataVolume,
      permissions: row.permissions.join(', '),
      favorite: row.favorite,
      importance: row.importance,
      remark: row.remark,
      createTime: new Date(row.createTime).toLocaleString('zh-CN')
    })

    // 设置数据行样式
    const dataRow = worksheet.getRow(2)
    dataRow.alignment = { horizontal: 'left', vertical: 'middle' }

    // 设置边框
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 下载文件
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const fileName = `数据类型_${row.dataTypeName}_${timestamp}.xlsx`
    FileSaver.saveAs(blob, fileName)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 分享单行数据
const handleShareRow = async (row: any) => {
  try {
    ElMessage.info('正在生成分享链接...')

    // 构建分享参数
    const shareParams = new URLSearchParams()
    shareParams.set('dataTypeId', row.id.toString())
    shareParams.set('dataTypeName', row.dataTypeName)
    shareParams.set('category', row.category)
    shareParams.set('share', Date.now().toString())
    shareParams.set('type', 'dataType')

    // 生成分享链接
    const shareUrl = `${window.location.origin}/reportIntegrationSpotCheck/commonDataTypeManagement?${shareParams.toString()}`

    // 准备分享内容
    const shareTitle = `数据类型详情 - ${row.dataTypeName}`
    const shareText = `数据类型名称：${row.dataTypeName}
分类：${row.category}
数据量：${row.dataVolume.toLocaleString()} 条
重要性：${row.importance}
权限：${row.permissions.join('、')}
收藏：${row.favorite} 星

备注：${row.remark}

创建时间：${new Date(row.createTime).toLocaleString('zh-CN')}

查看详细信息：${shareUrl}`

    // 尝试使用Web Share API（如果支持）
    if (navigator.share) {
      await navigator.share({
        title: shareTitle,
        text: shareText,
        url: shareUrl
      })
      ElMessage.success('分享成功')
    } else {
      // 备选方案：复制到剪贴板
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(shareText)
        ElMessage.success('分享内容已复制到剪贴板')

        // 显示分享信息
        ElMessageBox.alert(
          `数据类型"${row.dataTypeName}"的详细信息已复制到剪贴板，您可以粘贴分享给其他人。`,
          shareTitle,
          {
            confirmButtonText: '确定',
            type: 'success'
          }
        )
      } else {
        // 最后的备选方案：显示链接
        ElMessageBox.alert(
          `请复制以下链接分享：\n${shareUrl}`,
          shareTitle,
          {
            confirmButtonText: '确定',
            type: 'info'
          }
        )
      }
    }
  } catch (error: any) {
    console.error('分享失败:', error)
    if (error?.name === 'AbortError') {
      // 用户取消分享
      return
    }
    ElMessage.error('分享失败，请重试')
  }
}

// 复制单行数据
const handleCopyRow = async (row: any) => {
  try {
    // 这里将在后续实现具体的复制逻辑
    ElMessage.info('正在复制数据...')
    console.log('复制行数据:', row)
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请重试')
  }
}

// 下载导入模板
const handleDownloadTemplate = async () => {
  try {
    ElMessage.info('正在生成模板文件...')

    // 动态导入ExcelJS和file-saver
    const [ExcelJS, FileSaver] = await Promise.all([
      import('exceljs'),
      import('file-saver')
    ])

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = '通用数据类型管理系统'
    workbook.lastModifiedBy = '系统'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('数据类型导入模板')

    // 设置列定义
    worksheet.columns = [
      { header: '数据类型名称*', key: 'dataTypeName', width: 20 },
      { header: '数据类型描述', key: 'dataTypeDescription', width: 30 },
      { header: '类型分类*', key: 'category', width: 15 },
      { header: '存储需求', key: 'storageRequirement', width: 15 },
      { header: '数值范围', key: 'valueRange', width: 20 },
      { header: '默认值', key: 'defaultValue', width: 15 },
      { header: '约束条件', key: 'constraints', width: 25 },
      { header: '权限(多个用逗号分隔)', key: 'permissions', width: 25 }
    ]

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    }
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' }

    // 添加示例数据
    worksheet.addRow({
      dataTypeName: 'VARCHAR',
      dataTypeDescription: '可变长度字符串类型',
      category: '字符型',
      storageRequirement: '1-255字节',
      valueRange: '1-255字符',
      defaultValue: '空字符串',
      constraints: '非空约束',
      permissions: '访问,编辑'
    })

    worksheet.addRow({
      dataTypeName: 'INT',
      dataTypeDescription: '整数类型',
      category: '数据型',
      storageRequirement: '4字节',
      valueRange: '-2147483648到2147483647',
      defaultValue: '0',
      constraints: '非空约束，唯一性约束',
      permissions: '访问,编辑,删除'
    })

    // 设置数据行样式
    for (let i = 2; i <= 3; i++) {
      const dataRow = worksheet.getRow(i)
      dataRow.alignment = { horizontal: 'left', vertical: 'middle' }
    }

    // 设置边框
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    })

    // 添加说明信息
    worksheet.addRow([])
    worksheet.addRow(['说明：'])
    worksheet.addRow(['1. 带*号的字段为必填项'])
    worksheet.addRow(['2. 类型分类可选值：数据型、字符型、日期型、布尔型、其他型'])
    worksheet.addRow(['3. 权限可选值：访问、编辑、删除（多个权限用逗号分隔）'])
    worksheet.addRow(['4. 请删除示例数据后填入实际数据'])

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 下载文件
    const timestamp = new Date().toISOString().slice(0, 10)
    const fileName = `数据类型导入模板_${timestamp}.xlsx`
    FileSaver.saveAs(blob, fileName)

    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('模板下载失败:', error)
    ElMessage.error('模板下载失败，请重试')
  }
}

// 批量导出数据
const handleBatchExport = async () => {
  try {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要导出的数据')
      return
    }

    ElMessage.info('正在导出数据...')

    // 动态导入ExcelJS和file-saver
    const [ExcelJS, FileSaver] = await Promise.all([
      import('exceljs'),
      import('file-saver')
    ])

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = '通用数据类型管理系统'
    workbook.lastModifiedBy = '系统'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('数据类型列表')

    // 设置列定义
    worksheet.columns = [
      { header: '序号', key: 'index', width: 10 },
      { header: '标签', key: 'tag', width: 15 },
      { header: '数据类型名称', key: 'dataTypeName', width: 20 },
      { header: '分类', key: 'category', width: 15 },
      { header: '数据量', key: 'dataVolume', width: 15 },
      { header: '权限', key: 'permissions', width: 20 },
      { header: '收藏', key: 'favorite', width: 10 },
      { header: '重要性', key: 'importance', width: 12 },
      { header: '备注', key: 'remark', width: 30 },
      { header: '创建时间', key: 'createTime', width: 20 }
    ]

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    }
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' }

    // 添加数据行
    selectedRows.value.forEach((row, index) => {
      worksheet.addRow({
        index: index + 1,
        tag: row.tag.text,
        dataTypeName: row.dataTypeName,
        category: row.category,
        dataVolume: row.dataVolume,
        permissions: row.permissions.join(', '),
        favorite: row.favorite,
        importance: row.importance,
        remark: row.remark,
        createTime: new Date(row.createTime).toLocaleString('zh-CN')
      })
    })

    // 设置数据行样式
    for (let i = 2; i <= selectedRows.value.length + 1; i++) {
      const dataRow = worksheet.getRow(i)
      dataRow.alignment = { horizontal: 'left', vertical: 'middle' }
    }

    // 设置边框
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 下载文件
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const fileName = `数据类型批量导出_${selectedRows.value.length}条_${timestamp}.xlsx`
    FileSaver.saveAs(blob, fileName)

    ElMessage.success(`成功导出 ${selectedRows.value.length} 条数据`)
  } catch (error) {
    console.error('批量导出失败:', error)
    ElMessage.error('批量导出失败，请重试')
  }
}

// 打开导入弹窗
const handleOpenImport = () => {
  showImportDialog.value = true
  uploadFileList.value = []
  importErrors.value = []
  importProgress.value = 0
}

// 关闭导入弹窗
const handleCloseImport = () => {
  showImportDialog.value = false
  uploadFileList.value = []
  importErrors.value = []
  importProgress.value = 0
  importLoading.value = false
}

// 文件上传前的验证
const beforeUpload = (file: any) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel' ||
                  file.name.endsWith('.xlsx') ||
                  file.name.endsWith('.xls')

  if (!isExcel) {
    ElMessage.error('只能上传Excel文件(.xlsx/.xls)！')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过10MB！')
    return false
  }

  return true
}

// 文件上传变化处理
const handleUploadChange = (file: any, fileList: any[]) => {
  uploadFileList.value = fileList
}

// 文件移除处理
const handleRemoveFile = (file: any, fileList: any[]) => {
  uploadFileList.value = fileList
}

// 解析Excel文件并导入数据
const handleImportData = async () => {
  if (uploadFileList.value.length === 0) {
    ElMessage.warning('请先选择要导入的Excel文件')
    return
  }

  importLoading.value = true
  importErrors.value = []
  importProgress.value = 0

  try {
    const file = uploadFileList.value[0].raw
    const ExcelJS = await import('exceljs')

    // 读取Excel文件
    const workbook = new ExcelJS.Workbook()
    const arrayBuffer = await file.arrayBuffer()
    await workbook.xlsx.load(arrayBuffer)

    const worksheet = workbook.getWorksheet(1)
    if (!worksheet) {
      throw new Error('Excel文件中没有找到工作表')
    }

    const importData: any[] = []
    const errors: string[] = []
    let rowIndex = 1

    // 验证表头（支持更灵活的匹配）
    const headerRow = worksheet.getRow(1)
    const expectedHeaders = ['数据类型名称', '数据类型描述', '类型分类', '存储需求', '数值范围', '默认值', '约束条件', '权限']
    const actualHeaders = headerRow.values as any[]

    for (let i = 0; i < expectedHeaders.length; i++) {
      const expected = expectedHeaders[i]
      const rawActual = actualHeaders[i + 1]?.toString() || ''
      const actual = rawActual.replace(/\*/g, '').trim()

      console.log(`第${i + 1}列验证: 期望="${expected}", 原始="${rawActual}", 处理后="${actual}"`)

      // 对于权限列，支持多种格式（权限、权限(多个用逗号分隔)等）
      if (expected === '权限') {
        // 检查是否包含"权限"关键字
        if (actual.includes('权限')) {
          console.log(`权限列验证通过: actual="${actual}"`)
          continue
        } else {
          errors.push(`表头格式错误：第${i + 1}列应包含"权限"，实际为"${actual || '空'}"`)
        }
      } else {
        // 其他列进行精确匹配
        if (actual !== expected) {
          errors.push(`表头格式错误：第${i + 1}列应为"${expected}"，实际为"${actual || '空'}"`)
        }
      }
    }

    if (errors.length > 0) {
      console.error('表头验证失败:', errors)
      importErrors.value = errors
      importLoading.value = false
      return
    }

    // 处理数据行
    worksheet.eachRow((row, index) => {
      if (index === 1) return // 跳过表头

      rowIndex = index
      const values = row.values as any[]

      // 跳过空行
      if (!values || values.length <= 1 || !values[1]) return

      const rowData = {
        dataTypeName: values[1]?.toString().trim(),
        dataTypeDescription: values[2]?.toString().trim() || '',
        category: values[3]?.toString().trim(),
        storageRequirement: values[4]?.toString().trim() || '',
        valueRange: values[5]?.toString().trim() || '',
        defaultValue: values[6]?.toString().trim() || '',
        constraints: values[7]?.toString().trim() || '',
        permissions: values[8]?.toString().trim() || ''
      }

      // 验证必填字段
      if (!rowData.dataTypeName) {
        errors.push(`第${index}行：数据类型名称不能为空`)
      }

      if (!rowData.category) {
        errors.push(`第${index}行：类型分类不能为空`)
      } else {
        const validCategories = ['数据型', '字符型', '日期型', '布尔型', '其他型']
        if (!validCategories.includes(rowData.category)) {
          errors.push(`第${index}行：类型分类"${rowData.category}"无效，可选值：${validCategories.join('、')}`)
        }
      }

      // 验证权限格式
      if (rowData.permissions) {
        const permissions = rowData.permissions.split(',').map((p: string) => p.trim())
        const validPermissions = ['访问', '编辑', '删除']
        const invalidPermissions = permissions.filter((p: string) => !validPermissions.includes(p))
        if (invalidPermissions.length > 0) {
          errors.push(`第${index}行：权限"${invalidPermissions.join('、')}"无效，可选值：${validPermissions.join('、')}`)
        }
        rowData.permissions = permissions.filter((p: string) => validPermissions.includes(p))
      } else {
        rowData.permissions = []
      }

      if (errors.length === 0) {
        importData.push(rowData)
      }

      // 更新进度
      importProgress.value = Math.floor((index / worksheet.rowCount) * 100)
    })

    if (errors.length > 0) {
      importErrors.value = errors
      importLoading.value = false
      return
    }

    // 导入数据到现有列表
    let updateCount = 0
    let addCount = 0

    importData.forEach(importItem => {
      const existingIndex = tableData.value.findIndex(item =>
        item.dataTypeName === importItem.dataTypeName
      )

      if (existingIndex > -1) {
        // 更新现有数据
        Object.assign(tableData.value[existingIndex], {
          dataTypeDescription: importItem.dataTypeDescription,
          category: importItem.category,
          storageRequirement: importItem.storageRequirement,
          valueRange: importItem.valueRange,
          defaultValue: importItem.defaultValue,
          constraints: importItem.constraints,
          permissions: importItem.permissions,
          remark: `这是${importItem.dataTypeName}类型的备注信息`
        })
        updateCount++
      } else {
        // 添加新数据
        const newId = Math.max(...tableData.value.map(item => item.id)) + 1
        const tagColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']

        const newItem = {
          id: newId,
          index: newId,
          tag: {
            text: `标签${newId}`,
            color: tagColors[Math.floor(Math.random() * tagColors.length)]
          },
          dataTypeName: importItem.dataTypeName,
          category: importItem.category,
          dataVolume: Math.floor(Math.random() * 10000) + 1000,
          permissions: importItem.permissions,
          favorite: Math.floor(Math.random() * 5) + 1,
          importance: ['高', '中', '低'][Math.floor(Math.random() * 3)],
          remark: `这是${importItem.dataTypeName}类型的备注信息`,
          dataTypeDescription: importItem.dataTypeDescription,
          storageRequirement: importItem.storageRequirement,
          valueRange: importItem.valueRange,
          defaultValue: importItem.defaultValue,
          constraints: importItem.constraints,
          createTime: new Date().toISOString()
        }

        tableData.value.unshift(newItem)
        addCount++
      }
    })

    // 保存到本地存储并刷新数据
    saveToLocalStorage()
    applyFilters()

    ElMessage.success(`导入成功！更新 ${updateCount} 条数据，新增 ${addCount} 条数据`)
    handleCloseImport()

  } catch (error: any) {
    console.error('导入失败:', error)
    importErrors.value = [`导入失败：${error.message || '未知错误'}`]
  } finally {
    importLoading.value = false
  }
}

// 组件挂载
onMounted(() => {
  initData()
  // 初始化访问记录数据（空数组，等待用户操作时添加）
  accessData.value = []
  accessPagination.total = 0
})
</script>

<template>
  <div class="common-data-type-management">
    <Block
      title="常用数据类型管理"
      :enable-expand-content="true"
      :enableBackButton="false"
      :enable-fixed-height="true"
      :enable-close-button="false"
      @content-expand="() => {}"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button 
          size="small" 
          type="default" 
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
        
        <el-button size="small" type="primary" @click="onClickAdd">
          <el-icon style="margin-right: 4px">
            <Plus />
          </el-icon>
          新增数据类型
        </el-button>

        <el-button size="small" type="success" @click="handleOpenImport" style="margin-left: 8px">
          <el-icon style="margin-right: 4px">
            <Upload />
          </el-icon>
          导入
        </el-button>

        <el-button
          size="small"
          type="danger"
          @click="onBatchDelete"
          :disabled="selectedRows.length === 0"
          style="margin-left: 8px"
        >
          批量删除 ({{ selectedRows.length }})
        </el-button>

        <el-button
          size="small"
          type="success"
          @click="handleBatchExport"
          :disabled="selectedRows.length === 0"
          style="margin-left: 8px"
        >
          <el-icon style="margin-right: 4px">
            <Download />
          </el-icon>
          批量导出 ({{ selectedRows.length }})
        </el-button>

        <!-- 更多下拉菜单 -->
        <el-dropdown trigger="click" @command="handleMoreTopMenuClick" style="margin-left: 8px">
          <el-button size="small">
            更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="history">历史记录</el-dropdown-item>
              <el-dropdown-item command="access">访问记录</el-dropdown-item>
              <el-dropdown-item command="version">版本管理</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>

      <template #expand>
        <!-- 搜索区域 -->
        <div class="search" v-loading="searchLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="4"
            :label-width="100"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
            @reset="onReset"
          />
        </div>
      </template>

      <!-- 数据表格 -->
      <TableV2
        ref="tableRef"
        :columns="columns"
        :buttons="buttons"
        :defaultTableData="getCurrentPageData()"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :enable-index="false"
        :height="tableHeight"
        :loading="loading"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @click-button="onTableClickButton"
      >
        <!-- 序号列 -->
        <template #index="{ row }">
          {{ row.index }}
        </template>

        <!-- 标签列 -->
        <template #tag="{ row }">
          <el-tag :color="row.tag.color" style="color: white; border: none;">
            {{ row.tag.text }}
          </el-tag>
        </template>

        <!-- 数据量列 -->
        <template #dataVolume="{ row }">
          {{ row.dataVolume.toLocaleString() }}
        </template>

        <!-- 权限列 -->
        <template #permissions="{ row }">
          <el-tag
            v-for="permission in row.permissions"
            :key="permission"
            size="small"
            style="margin-right: 4px;"
            :type="permission === '访问' ? 'success' : permission === '编辑' ? 'warning' : 'danger'"
          >
            {{ permission }}
          </el-tag>
        </template>

        <!-- 收藏列 -->
        <template #favorite="{ row }">
          <div class="star-rating">
            <el-icon
              v-for="i in 5"
              :key="i"
              :class="i <= row.favorite ? 'star-filled' : 'star-empty'"
              size="16"
            >
              <Star />
            </el-icon>
          </div>
        </template>

        <!-- 重要性列 -->
        <template #importance="{ row }">
          <el-tag
            :type="row.importance === '高' ? 'danger' : row.importance === '中' ? 'warning' : 'info'"
            size="small"
          >
            {{ row.importance }}
          </el-tag>
        </template>


      </TableV2>

      <!-- 分页 -->
      <div class="pagination-container">
        <Pagination
          :total="pagination.total"
          :current-page="pagination.page"
          :page-size="pagination.size"
          @current-change="onPaginationChange($event, 'page')"
          @size-change="onPaginationChange($event, 'size')"
        />
      </div>
    </Block>

    <!-- 新增/编辑/查看对话框 -->
    <Dialog
      v-model="showDialogForm"
      :title="isViewMode ? '查看数据类型详情' : (currentRow ? '编辑数据类型' : '新增数据类型')"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      :width="isViewMode ? '900px' : '600px'"
      @closed="currentRow = null; isViewMode = false"
      @click-confirm="onDialogConfirm"
      :enable-confirm="!isViewMode"
      :close-on-click-modal="false"
      :max-height="isViewMode ? '80vh' : 'auto'"
    >
      <div v-if="isViewMode" class="detail-view">
        <div class="detail-scroll-container">
          <!-- 基本信息 -->
          <div class="detail-section">
            <h3 class="section-title">基本信息</h3>
            <Form
              ref="dialogFormRef"
              v-model="dialogForm"
              :props="dialogFormProps.map(prop => ({ ...prop, disabled: true }))"
              :rules="{}"
              :enable-button="false"
              :column-count="1"
              :label-width="120"
            />
          </div>

        <!-- 统计数据 -->
        <div class="detail-section">
          <h3 class="section-title">统计数据</h3>

          <!-- 基础信息卡片 -->
          <el-row :gutter="16" style="margin-bottom: 16px; margin-top: 10px;">
            <el-col :span="8">
              <el-card class="stat-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon creator-icon">
                    <el-icon size="24"><User /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">创建者</div>
                    <div class="stat-value">{{ statisticsData.creator }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="stat-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon time-icon">
                    <el-icon size="24"><Clock /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">创建时间</div>
                    <div class="stat-value">{{ new Date(statisticsData.createTime).toLocaleDateString('zh-CN') }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="stat-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon update-icon">
                    <el-icon size="24"><Refresh /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">最后修改</div>
                    <div class="stat-value">{{ new Date(statisticsData.lastModifyDate).toLocaleDateString('zh-CN') }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 数据统计卡片 -->
          <el-row :gutter="16" style="margin-bottom: 16px; margin-top: 10px;">
            <el-col :span="12">
              <el-card class="stat-card large-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon data-icon">
                    <el-icon size="28"><DataAnalysis /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">数据量</div>
                    <div class="stat-value large-value">{{ statisticsData.dataVolume?.toLocaleString() }}</div>
                    <div class="stat-desc">条记录</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="stat-card large-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon source-icon">
                    <el-icon size="28"><Coin /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">数据来源</div>
                    <div class="stat-value large-value">{{ statisticsData.dataSource }}</div>
                    <div class="stat-desc">{{ statisticsData.updateFrequency }}更新</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 数值统计 -->
          <div v-if="dialogForm.category === '数据型'">
            <h4 class="sub-title">数值统计</h4>
            <el-row :gutter="16" style="margin-bottom: 16px; margin-top: 10px;">
              <el-col :span="6">
                <el-card class="stat-card mini-card" shadow="hover">
                  <div class="mini-stat">
                    <div class="mini-label">平均值</div>
                    <div class="mini-value">{{ statisticsData.averageValue }}</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card mini-card" shadow="hover">
                  <div class="mini-stat">
                    <div class="mini-label">中位数</div>
                    <div class="mini-value">{{ statisticsData.medianValue }}</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card mini-card" shadow="hover">
                  <div class="mini-stat">
                    <div class="mini-label">众数</div>
                    <div class="mini-value">{{ statisticsData.modeValue }}</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card mini-card" shadow="hover">
                  <div class="mini-stat">
                    <div class="mini-label">方差</div>
                    <div class="mini-value">{{ statisticsData.variance }}</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <div v-else>
            <el-alert
              title="数值统计"
              description="当前数据类型不支持数值统计分析"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </div>
        </div>
      </div>

      <div v-else>
        <Form
          ref="dialogFormRef"
          v-model="dialogForm"
          :props="dialogFormProps"
          :rules="dialogFormRules"
          :enable-button="false"
          :column-count="1"
          :label-width="120"
        />
      </div>
    </Dialog>

    <!-- 导入弹窗 -->
    <Dialog
      v-model="showImportDialog"
      title="导入常用数据类型"
      :destroy-on-close="true"
      :loading="importLoading"
      loading-text="导入中"
      width="600px"
      @closed="handleCloseImport"
      @click-confirm="handleImportData"
      :close-on-click-modal="false"
    >
      <div class="import-dialog-content">
        <!-- 信息提示 -->
        <el-alert
          title="点击批量导入，就转到此弹窗。"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 20px"
        />

        <!-- 下载模板按钮 -->
        <div class="template-download" style="margin: 20px 0; text-align: center">
          <el-button type="primary" @click="handleDownloadTemplate">
            <el-icon style="margin-right: 4px">
              <Download />
            </el-icon>
            导入数据下载
          </el-button>
        </div>

        <!-- 文件上传区域 -->
        <div class="upload-area">
          <el-upload
            class="upload-dragger"
            drag
            :file-list="uploadFileList"
            :before-upload="beforeUpload"
            :on-change="handleUploadChange"
            :on-remove="handleRemoveFile"
            :auto-upload="false"
            accept=".xlsx,.xls"
            :limit="1"
          >
            <el-icon class="el-icon--upload" size="67" style="color: #409eff">
              <UploadFilled />
            </el-icon>
            <div class="el-upload__text">
              点击或将文件拖拽到这里上传
            </div>
          </el-upload>
        </div>

        <!-- 错误信息显示 -->
        <div v-if="importErrors.length > 0" class="error-messages">
          <el-alert
            title="导入错误"
            type="error"
            :closable="false"
            show-icon
          >
            <ul style="margin: 0; padding-left: 20px">
              <li v-for="error in importErrors" :key="error">{{ error }}</li>
            </ul>
          </el-alert>
        </div>

        <!-- 导入进度 -->
        <div v-if="importLoading && importProgress > 0" class="import-progress">
          <el-progress :percentage="importProgress" :show-text="true" />
        </div>
      </div>
    </Dialog>

    <!-- 历史记录弹窗 -->
    <Dialog
      v-model="showHistoryDialog"
      title="数据类型管理历史记录"
      :destroy-on-close="true"
      width="1200px"
      :enable-confirm="false"
      :close-on-click-modal="false"
      max-height="80vh"
    >
      <div class="history-dialog-content">
        <!-- 历史记录表格 -->
        <TableV2
          :columns="historyColumns"
          :defaultTableData="getCurrentPageHistoryData()"
          :enable-toolbar="false"
          :enable-own-button="false"
          :enable-selection="false"
          :enable-index="false"
          :height="400"
          :loading="false"
        >
          <!-- 序号列 -->
          <template #sequence="{ row }">
            {{ row.sequence }}
          </template>

          <!-- 字段名称列 -->
          <template #dataTypeName="{ row }">
            <span class="field-name">{{ row.dataTypeName }}</span>
          </template>

          <!-- 变更类型列 -->
          <template #operationType="{ row }">
            <el-tag
              :type="getChangeTypeColor(row.operationType)"
              size="small"
            >
              {{ row.operationType }}
            </el-tag>
          </template>

          <!-- 变更前列 -->
          <template #originalData="{ row }">
            <span v-if="row.originalData" class="data-text">{{ row.originalData }}</span>
            <span v-else class="no-data">-</span>
          </template>

          <!-- 变更后列 -->
          <template #newData="{ row }">
            <span v-if="row.newData" class="data-text">{{ row.newData }}</span>
            <span v-else class="no-data">-</span>
          </template>

          <!-- 操作人员列 -->
          <template #operator="{ row }">
            <span class="operator-name">{{ row.operator }}</span>
          </template>

          <!-- 操作时间列 -->
          <template #operationTime="{ row }">
            <span class="operation-time">{{ row.operationTime }}</span>
          </template>
        </TableV2>

        <!-- 分页 -->
        <div class="history-pagination">
          <Pagination
            :total="historyPagination.total"
            :current-page="historyPagination.page"
            :page-size="historyPagination.size"
            @current-change="onHistoryPaginationChange($event, 'page')"
            @size-change="onHistoryPaginationChange($event, 'size')"
          />
        </div>
      </div>
    </Dialog>

    <!-- 访问记录弹窗 -->
    <Dialog
      v-model="showAccessDialog"
      title="数据类型访问记录"
      :destroy-on-close="true"
      width="1400px"
      :enable-confirm="false"
      :close-on-click-modal="false"
      max-height="80vh"
    >
      <div class="access-dialog-content">
        <!-- 访问记录表格 -->
        <TableV2
          :columns="accessColumns"
          :defaultTableData="getCurrentPageAccessData()"
          :enable-toolbar="false"
          :enable-own-button="false"
          :enable-selection="false"
          :enable-index="false"
          :height="400"
          :loading="false"
        >
          <!-- 序号列 -->
          <template #sequence="{ row }">
            {{ row.sequence }}
          </template>

          <!-- 数据类型列 -->
          <template #dataTypeName="{ row }">
            <span class="data-type-name">{{ row.dataTypeName }}</span>
          </template>

          <!-- 访问类型列 -->
          <template #accessType="{ row }">
            <el-tag
              :type="getAccessTypeColor(row.accessType)"
              size="small"
            >
              {{ row.accessType }}
            </el-tag>
          </template>

          <!-- 访问用户列 -->
          <template #accessUser="{ row }">
            <span class="access-user">{{ row.accessUser }}</span>
          </template>

          <!-- 访问时间列 -->
          <template #accessTime="{ row }">
            <span class="access-time">{{ row.accessTime }}</span>
          </template>

          <!-- 访问时长列 -->
          <template #duration="{ row }">
            <span class="duration">{{ row.duration }}</span>
          </template>

          <!-- IP地址列 -->
          <template #ipAddress="{ row }">
            <span class="ip-address">{{ row.ipAddress }}</span>
          </template>

          <!-- 浏览器列 -->
          <template #userAgent="{ row }">
            <span class="user-agent">{{ row.userAgent }}</span>
          </template>

          <!-- 访问结果列 -->
          <template #accessResult="{ row }">
            <el-tag
              :type="row.accessResult === '成功' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.accessResult }}
            </el-tag>
          </template>
        </TableV2>

        <!-- 分页 -->
        <div class="access-pagination">
          <Pagination
            :total="accessPagination.total"
            :current-page="accessPagination.page"
            :page-size="accessPagination.size"
            @current-change="onAccessPaginationChange($event, 'page')"
            @size-change="onAccessPaginationChange($event, 'size')"
          />
        </div>
      </div>
    </Dialog>

    <!-- 版本管理弹窗 -->
    <Dialog
      v-model="showVersionDialog"
      title="常用数据类型版本管理"
      :destroy-on-close="true"
      width="1200px"
      :enable-confirm="false"
      :close-on-click-modal="false"
      max-height="85vh"
    >
      <div class="version-dialog-content">
        <!-- 数据类型选择区域 -->
        <el-card class="version-selection-card" shadow="never">
          <template #header>
            <div class="card-header">
              <div class="header-content">
                <span class="card-title">数据类型选择</span>
              </div>
            </div>
          </template>

          <el-row :gutter="16">
            <el-col :span="24">
              <div class="selected-types-section">
                <el-text class="section-label">已选择的数据类型：</el-text>
                <div class="selected-types-container">
                  <el-tag
                    v-for="dataType in selectedDataTypes"
                    :key="dataType"
                    closable
                    type="primary"
                    effect="light"
                    @close="removeDataType(dataType)"
                    class="selected-type-tag"
                  >
                    {{ dataType }}
                  </el-tag>
                  <el-empty
                    v-if="selectedDataTypes.length === 0"
                    description="暂未选择数据类型"
                    :image-size="60"
                    class="empty-selection"
                  />
                </div>
              </div>
            </el-col>
          </el-row>

          <el-divider />

          <el-row :gutter="16" align="middle">
            <el-col :span="2">
              <el-text class="selector-label">类型名称：</el-text>
            </el-col>
            <el-col :span="12">
              <el-select
                v-model="selectedDataTypeName"
                placeholder="请选择数据类型"
                style="width: 100%;"
                clearable
                filterable
              >
                <el-option
                  v-for="dataType in availableDataTypes.filter(type => !selectedDataTypes.includes(type))"
                  :key="dataType"
                  :label="dataType"
                  :value="dataType"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-button
                type="primary"
                @click="addDataType"
                :disabled="!selectedDataTypeName"
                :icon="Plus"
              >
                添加选择
              </el-button>
            </el-col>
          </el-row>
        </el-card>

        <!-- 时间回溯区域 -->
        <el-card class="time-rollback-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">版本时间设置</span>
            </div>
          </template>

          <el-row :gutter="16" align="middle">
            <el-col :span="2">
              <el-text class="time-label">版本时间：</el-text>
            </el-col>
            <el-col :span="7">
              <el-date-picker
                v-model="rollbackDate"
                type="date"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
              />
            </el-col>
            <el-col :span="7">
              <el-time-picker
                v-model="rollbackTime"
                placeholder="选择时间"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                style="width: 100%;"
              />
            </el-col>
            <el-col :span="3">
              <el-button
                type="danger"
                @click="executeDeleteVersion"
                :disabled="selectedDataTypes.length === 0 || !rollbackDate || !rollbackTime"
                size="small"
                style="width: 100%;"
              >
                删除版本
              </el-button>
            </el-col>
            <el-col :span="3">
              <el-button
                type="primary"
                @click="executeRollback"
                :disabled="selectedDataTypes.length === 0 || !rollbackDate || !rollbackTime"
                :icon="Refresh"
                size="small"
                style="width: 100%;"
              >
                回溯
              </el-button>
            </el-col>
          </el-row>
        </el-card>

        <!-- 版本更改记录显示区域 -->
        <el-card class="version-records-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">版本更改记录</span>
              <el-text type="info" size="small">显示历史版本回溯操作记录</el-text>
            </div>
          </template>

          <div class="records-content">
            <el-empty
              v-if="versionChangeRecords.length === 0"
              description="暂无版本更改记录"
              :image-size="100"
              class="empty-records"
            />

            <div v-else class="records-list">
              <el-timeline>
                <el-timeline-item
                  v-for="record in getCurrentPageVersionRecords()"
                  :key="record.id"
                  :timestamp="record.operationTime"
                  placement="top"
                  :type="record.operationType === '回溯' ? 'primary' : 'danger'"
                  size="large"
                >
                  <el-card class="record-card" shadow="hover">
                    <div class="record-header">
                      <div class="record-info">
                        <el-tag
                          :type="record.operationType === '回溯' ? 'primary' : 'danger'"
                          size="small"
                        >
                          {{ record.operationType }}操作
                        </el-tag>
                        <el-tag type="success" size="small">{{ record.operator }}</el-tag>
                        <el-text
                          :type="record.operationType === '回溯' ? 'primary' : 'danger'"
                          class="target-time"
                        >
                          {{ record.operationType === '回溯' ? '回溯到' : '删除时间' }}：{{ record.rollbackTime }}
                        </el-text>
                      </div>
                    </div>

                    <el-divider />

                    <div class="record-description">
                      <el-text>{{ record.description }}</el-text>
                    </div>

                    <div class="record-changes">
                      <el-text class="changes-title" type="info" size="small">变更详情：</el-text>
                      <div class="changes-list">
                        <el-row :gutter="8" v-for="(change, index) in record.changes" :key="index">
                          <el-col :span="24">
                            <div class="change-item">
                              <div class="change-header">
                                <el-tag size="small" type="warning">{{ change.changeType }}</el-tag>
                                <el-text class="change-field" type="primary">{{ change.dataTypeName }}</el-text>
                              </div>
                              <div class="change-detail">
                                <el-text class="change-from" type="danger">{{ change.originalValue }}</el-text>
                                <el-icon class="change-arrow" color="#909399"><ArrowRight /></el-icon>
                                <el-text class="change-to" type="success">{{ change.newValue }}</el-text>
                              </div>
                            </div>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>

            <!-- 分页 -->
            <div v-if="versionChangeRecords.length > 0" class="version-pagination">
              <Pagination
                :total="versionRecordsPagination.total"
                :current-page="versionRecordsPagination.page"
                :page-size="versionRecordsPagination.size"
                @current-change="onVersionRecordsPaginationChange($event, 'page')"
                @size-change="onVersionRecordsPaginationChange($event, 'size')"
              />
            </div>
          </div>
        </el-card>
      </div>
    </Dialog>
  </div>
</template>

<route>
{
  meta: {
    title: '常用数据类型管理'
  }
}
</route>

<style scoped lang="scss">
.common-data-type-management {
  .search {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }

  .table-container {
    margin-bottom: 16px;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0;
  }

  .star-rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;

    .star-filled {
      color: #f7ba2a;
      transition: color 0.3s ease;
    }

    .star-empty {
      color: #dcdfe6;
      transition: color 0.3s ease;
    }
  }

  // 响应式表格样式
  :deep(.el-table) {
    font-size: 14px;

    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 500;
          border-bottom: 2px solid #e4e7ed;
          padding: 12px 0;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          transition: background-color 0.3s ease;

          &:hover {
            background-color: #f5f7fa;
          }

          td {
            padding: 12px 0;
            border-bottom: 1px solid #ebeef5;
          }
        }
      }
    }

    // 响应式处理
    @media (max-width: 1200px) {
      .el-table__header th,
      .el-table__body td {
        padding: 8px 4px;
        font-size: 13px;
      }
    }

    @media (max-width: 768px) {
      .el-table__header th,
      .el-table__body td {
        padding: 6px 2px;
        font-size: 12px;
      }
    }
  }

  // 标签样式优化
  :deep(.el-tag) {
    margin-right: 4px;
    margin-bottom: 2px;
    border-radius: 4px;
    font-size: 12px;
    padding: 0 8px;
    height: 24px;
    line-height: 22px;
    transition: all 0.3s ease;

    &.el-tag--success {
      background-color: #f0f9ff;
      border-color: #b3d8ff;
      color: #409eff;
    }

    &.el-tag--warning {
      background-color: #fdf6ec;
      border-color: #f5dab1;
      color: #e6a23c;
    }

    &.el-tag--danger {
      background-color: #fef0f0;
      border-color: #fbc4c4;
      color: #f56c6c;
    }

    &.el-tag--info {
      background-color: #f4f4f5;
      border-color: #d3d4d6;
      color: #909399;
    }
  }

  // 按钮样式优化
  :deep(.el-button) {
    border-radius: 4px;
    transition: all 0.3s ease;
    font-weight: 400;

    &.el-button--small {
      padding: 7px 15px;
      font-size: 12px;
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  // 对话框样式优化
  :deep(.el-dialog) {
    border-radius: 8px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);

    .el-dialog__header {
      padding: 20px 20px 10px;
      border-bottom: 1px solid #ebeef5;

      .el-dialog__title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 10px 20px 20px;
      border-top: 1px solid #ebeef5;
    }
  }

  // 表单样式优化
  :deep(.el-form) {
    .el-form-item {
      margin-bottom: 18px;

      .el-form-item__label {
        color: #606266;
        font-weight: 500;
        line-height: 32px;
      }

      .el-form-item__content {
        .el-input,
        .el-select,
        .el-textarea {
          .el-input__inner,
          .el-textarea__inner {
            border-radius: 4px;
            transition: all 0.3s ease;

            &:focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
          }
        }

        .el-checkbox-group {
          .el-checkbox {
            margin-right: 16px;
            margin-bottom: 8px;

            .el-checkbox__label {
              color: #606266;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  // 详情视图样式
  .detail-view {
    .detail-scroll-container {
      max-height: 70vh;
      overflow-y: auto;
      padding-right: 8px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }

    .detail-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 3px solid #409eff;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -3px;
          left: 0;
          width: 60px;
          height: 3px;
          background: linear-gradient(90deg, #409eff, #67c23a);
          border-radius: 2px;
        }
      }

      .sub-title {
        font-size: 16px;
        font-weight: 500;
        color: #606266;
        margin: 20px 0 16px 0;
        padding-left: 12px;
        border-left: 4px solid #e6a23c;
      }
    }

    // 统计卡片样式
    .stat-card {
      border-radius: 12px;
      border: none;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
      }

      .stat-content {
        display: flex;
        align-items: center;
        padding: 8px;

        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          &.creator-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
          }

          &.time-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
          }

          &.update-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
          }

          &.data-icon {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
          }

          &.source-icon {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
          }
        }

        .stat-info {
          flex: 1;

          .stat-label {
            font-size: 14px;
            color: #909399;
            margin-bottom: 4px;
          }

          .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: #303133;

            &.large-value {
              font-size: 24px;
              font-weight: 700;
            }
          }

          .stat-desc {
            font-size: 12px;
            color: #c0c4cc;
            margin-top: 2px;
          }
        }
      }

      &.large-card .stat-content {
        padding: 16px;

        .stat-icon {
          width: 80px;
          height: 80px;
        }
      }

      &.mini-card {
        .mini-stat {
          text-align: center;
          padding: 16px 8px;

          .mini-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 8px;
          }

          .mini-value {
            font-size: 20px;
            font-weight: 600;
            color: #409eff;
          }
        }
      }
    }
  }

  // 导入弹窗样式
  .import-dialog-content {
    .template-download {
      text-align: center;
    }

    .upload-area {
      margin-bottom: 20px;

      .upload-dragger {
        :deep(.el-upload-dragger) {
          width: 100%;
          height: 180px;
          border: 2px dashed #d9d9d9;
          border-radius: 6px;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          transition: all 0.3s ease;

          &:hover {
            border-color: #409eff;
          }

          .el-icon--upload {
            margin: 40px 0 16px;
          }

          .el-upload__text {
            color: #606266;
            font-size: 14px;
            text-align: center;
          }
        }
      }
    }

    .error-messages {
      margin-bottom: 20px;

      ul {
        li {
          margin-bottom: 4px;
          color: #f56c6c;
          font-size: 13px;
        }
      }
    }

    .import-progress {
      margin-bottom: 20px;
    }
  }

  // 响应式布局
  @media (max-width: 768px) {
    .search {
      padding: 12px;
      margin-bottom: 12px;
    }

    .table-container {
      margin-bottom: 12px;
    }

    .pagination-container {
      padding: 12px 0;
      justify-content: center;
    }

    :deep(.el-button) {
      &.el-button--small {
        padding: 5px 10px;
        font-size: 11px;
      }
    }
  }

  @media (max-width: 480px) {
    :deep(.el-table) {
      .el-table__header th,
      .el-table__body td {
        &:nth-child(n+6) {
          display: none;
        }
      }
    }

    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto !important;
    }
  }

  // 历史记录弹窗样式
  .history-dialog-content {
    .field-name {
      color: #409eff;
      font-weight: 500;
      font-size: 13px;
    }

    .data-text {
      color: #606266;
      font-size: 13px;
      line-height: 1.4;
      word-break: break-word;
    }

    .no-data {
      color: #c0c4cc;
      font-style: italic;
    }

    .operator-name {
      color: #606266;
      font-size: 13px;
    }

    .operation-time {
      color: #909399;
      font-size: 12px;
    }

    .history-pagination {
      display: flex;
      justify-content: flex-end;
      padding: 16px 0 0 0;
      border-top: 1px solid #ebeef5;
      margin-top: 16px;
    }

    // 响应式处理
    @media (max-width: 1200px) {
      .field-name,
      .data-text,
      .operator-name {
        font-size: 12px;
      }

      .operation-time {
        font-size: 11px;
      }
    }

    @media (max-width: 768px) {
      .field-name,
      .data-text,
      .operator-name {
        font-size: 11px;
      }

      .operation-time {
        font-size: 10px;
      }

      .history-pagination {
        padding: 12px 0 0 0;
      }
    }
  }

  // 访问记录弹窗样式
  .access-dialog-content {
    .data-type-name {
      color: #409eff;
      font-weight: 500;
      font-size: 13px;
    }

    .access-user {
      color: #606266;
      font-size: 13px;
    }

    .access-time {
      color: #909399;
      font-size: 12px;
    }

    .duration {
      color: #67c23a;
      font-size: 12px;
      font-weight: 500;
    }

    .ip-address {
      color: #e6a23c;
      font-size: 12px;
      font-family: monospace;
    }

    .user-agent {
      color: #909399;
      font-size: 12px;
      word-break: break-word;
    }

    .access-pagination {
      display: flex;
      justify-content: flex-end;
      padding: 16px 0 0 0;
      border-top: 1px solid #ebeef5;
      margin-top: 16px;
    }

    // 响应式处理
    @media (max-width: 1200px) {
      .data-type-name,
      .access-user {
        font-size: 12px;
      }

      .access-time,
      .duration,
      .ip-address,
      .user-agent {
        font-size: 11px;
      }
    }

    @media (max-width: 768px) {
      .data-type-name,
      .access-user {
        font-size: 11px;
      }

      .access-time,
      .duration,
      .ip-address,
      .user-agent {
        font-size: 10px;
      }

      .access-pagination {
        padding: 12px 0 0 0;
      }
    }
  }

  // 版本管理弹窗样式
  .version-dialog-content {
    .version-selection-card,
    .time-rollback-card,
    .version-records-card {
      margin-bottom: 20px;

      :deep(.el-card__header) {
        padding: 16px 20px;
        background-color: #fafafa;
        border-bottom: 1px solid #e4e7ed;
      }

      :deep(.el-card__body) {
        padding: 20px;
      }
    }

    .card-header {
      .header-content {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }
    }

    .version-selection-card {
      .selected-types-section {
        margin-bottom: 16px;

        .section-label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: #606266;
        }

        .selected-types-container {
          min-height: 80px;
          padding: 12px;
          background-color: #f8f9fa;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
          display: flex;
          flex-wrap: wrap;
          align-items: flex-start;
          gap: 8px;

          .selected-type-tag {
            margin: 0;
          }

          .empty-selection {
            width: 100%;

            :deep(.el-empty__description) {
              margin-top: 8px;
            }
          }
        }
      }

      .selector-label {
        font-weight: 500;
        color: #606266;
      }
    }

    .time-rollback-card {
      .time-label {
        font-weight: 500;
        color: #606266;
      }
    }

    .version-records-card {
      .records-content {
        .empty-records {
          padding: 40px 0;
        }

        .records-list {
          :deep(.el-timeline) {
            padding-left: 0;
          }

          :deep(.el-timeline-item__wrapper) {
            padding-left: 28px;
          }

          .record-card {
            margin-bottom: 16px;

            .record-header {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .record-info {
                display: flex;
                align-items: center;
                gap: 12px;
              }

              .target-time {
                font-size: 13px;
                font-weight: 500;
              }
            }

            .record-description {
              margin: 12px 0;
              line-height: 1.6;
            }

            .record-changes {
              .changes-title {
                display: block;
                margin-bottom: 12px;
                font-weight: 500;
              }

              .changes-list {
                .change-item {
                  padding: 12px;
                  margin-bottom: 8px;
                  background-color: #f8f9fa;
                  border-radius: 6px;
                  border-left: 3px solid #409eff;

                  .change-header {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 8px;

                    .change-field {
                      font-weight: 500;
                    }
                  }

                  .change-detail {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-family: 'Courier New', monospace;
                    font-size: 13px;

                    .change-arrow {
                      margin: 0 4px;
                    }
                  }
                }
              }
            }
          }
        }

        .version-pagination {
          padding: 20px 0 0 0;
          display: flex;
          justify-content: center;
        }
      }
    }
  }
}
</style>
