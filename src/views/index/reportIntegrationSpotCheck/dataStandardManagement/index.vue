<script setup lang="ts" name="dataStandardManagement">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled, Download, Edit, Delete, View, Star, StarFilled } from '@element-plus/icons-vue'
import DialogComp from '@/components/common/dialog-comp.vue'
import { useDataStandardExcel } from './composables/useDataStandardExcel'

// 路由实例
const router = useRouter()

// 导入导出功能
const { isImporting, isExporting, exportToExcel, importFromExcel, generateTemplate } = useDataStandardExcel()

// 数据标准类型定义
interface DataStandardData {
  id: string
  index: number
  standardName: string
  category: string
  dataTotal: number
  dataVersion: string
  responsible: string
  permissions: {
    access: boolean
    edit: boolean
    delete: boolean
  }
  content: string
  remark: string
  passedDataRatio: number
  failedDataRatio: number
  complianceRate: number
  isFavorite: boolean
  status: 'normal' | 'abnormal'
  tagColor: string // 标签颜色
  createTime: string
  updateTime: string
}

// 异常数据类型定义
interface AbnormalData {
  id: string
  source: string
  field: string
  value: string
  triggerTime: string
  reason: string
}

// 评估数据类型定义
interface EvaluationItem {
  name: string
  description: string
  currentValue: number
  targetValue: number
  unit: string
  progress: number
  isReverse?: boolean // 是否为反向指标（值越小越好）
}

interface EvaluationData {
  conflictReports: EvaluationItem
  updateCycle: EvaluationItem
  qualityImprovement: EvaluationItem
  resolutionTime: EvaluationItem
}

// 标准规范数据类型定义
interface StandardRule {
  id: string
  sequence: string // 序号
  ruleName: string // 数据标准规范名称
  ruleDescription: string // 数据标准规范说明
  ruleExample: string // 标准规范举例
  createTime: string // 创建时间
}

// 访问记录数据类型定义
interface AccessRecord {
  id: string
  standardId: string // 数据标准ID
  standardName: string // 数据标准名称
  userName: string // 访问用户名称
  userId: string // 访问用户ID
  accessTime: string // 访问时间
  operation: string // 操作类型（查看详情）
  userRole?: string // 用户角色
  department?: string // 用户部门
}

// 修改历史记录数据类型定义
interface ModificationHistory {
  id: string
  standardId?: string // 数据标准ID（删除操作时可能为空）
  standardName: string // 数据标准名称
  operation: 'add' | 'edit' | 'delete' | 'import' // 操作类型
  operationName: string // 操作名称（中文）
  userName: string // 操作用户名称
  userId: string // 操作用户ID
  operationTime: string // 操作时间
  userRole?: string // 用户角色
  department?: string // 用户部门
  details?: string // 操作详情
  oldValue?: any // 修改前的值（编辑操作）
  newValue?: any // 修改后的值（新增、编辑操作）
}

// 搜索表单
const searchFormProp = ref([
  {
    label: '数据标准名称',
    prop: 'standardName',
    type: 'text',
    placeholder: '请输入数据标准名称'
  },
  { 
    label: '选择展示的分类', 
    prop: 'category', 
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: 'INT', value: 'INT' },
      { label: 'VARCHAR', value: 'VARCHAR' },
      { label: 'DATE', value: 'DATE' },
      { label: 'DECIMAL', value: 'DECIMAL' },
      { label: 'TEXT', value: 'TEXT' }
    ]
  }
])

const searchForm = ref({ standardName: '', category: '' })

// 权限标签定义
const PERMISSION_LABELS = {
  access: '访问',
  edit: '编辑', 
  delete: '删除'
}

// 加载状态
const loading = ref(false)
const searchLoading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0)
const currentRow = ref(null)

// 表格列配置
const columns = [
  { prop: 'index', label: '序号', minWidth: 80, fixed: 'left' },
  { prop: 'tagColor', label: '标签', minWidth: 80 },
  { prop: 'standardName', label: '数据标准名称', minWidth: 200, fixed: 'left' },
  { prop: 'category', label: '分类', minWidth: 100 },
  { prop: 'dataTotal', label: '涉及数据总数', minWidth: 140 },
  { prop: 'dataVersion', label: '数据版本', minWidth: 160, sortable: true },
  { prop: 'responsible', label: '负责人', minWidth: 100 },
  { prop: 'isFavorite', label: '收藏', minWidth: 80, sortable: true },
  { prop: 'status', label: '状态', minWidth: 120 }
]

// 异常数据表格列配置
const abnormalColumns = [
  { prop: 'source', label: '来源', minWidth: 120 },
  { prop: 'field', label: '字段', minWidth: 100 },
  { prop: 'value', label: '值', minWidth: 120 },
  { prop: 'triggerTime', label: '触发时间', minWidth: 160 },
  { prop: 'reason', label: '未通过原因', minWidth: 200 }
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表格数据
const tableData = ref<DataStandardData[]>([])
const selectedRows = ref<DataStandardData[]>([])

// 排序状态
const sortConfig = ref({
  prop: '',
  order: ''
})

// 弹窗状态
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const showDetailDialog = ref(false)
const showImportDialog = ref(false)
const showAbnormalDialog = ref(false)
const showEvaluateDialog = ref(false)
const showStandardRuleDialog = ref(false) // 标准规范列表弹窗
const showRuleDetailDialog = ref(false) // 标准规范详情弹窗
const showRuleAddDialog = ref(false) // 新增标准规范弹窗
const showRuleEditDialog = ref(false) // 编辑标准规范弹窗
const showColorPickerDialog = ref(false) // 颜色选择器弹窗

// 颜色选择器相关状态
const currentColorPickerRow = ref<DataStandardData | null>(null) // 当前选择颜色的行数据
const selectedColor = ref('') // 当前选中的颜色
const predefinedColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', 
  '#9C27B0', '#FF9800', '#4CAF50', '#2196F3', '#FF5722',
  '#795548', '#607D8B', '#FFC107', '#8BC34A', '#00BCD4'
]

// 导入相关状态
const importFile = ref<File | null>(null)
const uploadRef = ref()

// 异常数据相关状态
const abnormalData = ref<AbnormalData[]>([])
const abnormalPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})
const currentAbnormalStandard = ref<DataStandardData | null>(null)

// 标准规范相关状态
const standardRuleList = ref<StandardRule[]>([]) // 标准规范列表数据
const currentStandardRule = ref<StandardRule | null>(null) // 当前操作的标准规范
const ruleSearchForm = ref({ ruleName: '' }) // 标准规范搜索表单
const rulePagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})
const ruleAddForm = ref({
  ruleName: '',
  ruleDescription: '',
  ruleExample: ''
})
const ruleEditForm = ref({
  id: '',
  ruleName: '',
  ruleDescription: '',
  ruleExample: ''
})

// =============== 访问记录相关状态管理 ===============
const showAccessRecordDialog = ref(false) // 访问记录弹窗显示状态
const accessRecordList = ref<AccessRecord[]>([]) // 访问记录列表
const accessRecordPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 访问记录表格列配置
const accessColumns = ref([
  {
    label: '数据标准名称',
    prop: 'standardName',
    minWidth: 200
  },
  {
    label: '访问用户',
    prop: 'userName',
    width: 120,
    align: 'center'
  },
  {
    label: '用户角色',
    prop: 'userRole',
    width: 120,
    align: 'center'
  },
  {
    label: '所属部门',
    prop: 'department',
    width: 150,
    align: 'center'
  },
  {
    label: '操作类型',
    prop: 'operation',
    width: 100,
    align: 'center'
  },
  {
    label: '访问时间',
    prop: 'accessTime',
    width: 180,
    align: 'center'
  }
])

// 访问记录搜索表单配置
const accessSearchFormProp = ref([
  {
    label: '用户名称',
    prop: 'userName',
    type: 'text',
    placeholder: '请输入用户名称'
  },
  {
    label: '数据标准名称',
    prop: 'standardName',
    type: 'text',
    placeholder: '请输入数据标准名称'
  },
  {
    label: '开始时间',
    prop: 'startDate',
    type: 'date',
    placeholder: '请选择开始时间'
  },
  {
    label: '结束时间',
    prop: 'endDate',
    type: 'date',
    placeholder: '请选择结束时间'
  }
])

// =============== 修改历史记录相关状态管理 ===============
const showModificationHistoryDialog = ref(false) // 修改历史记录弹窗显示状态
const modificationHistoryList = ref<ModificationHistory[]>([]) // 修改历史记录列表
const modificationHistoryPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})
const modificationHistorySearchForm = ref({
  userName: '',
  operation: '',
  startDate: '',
  endDate: ''
})

// 修改历史记录表格列配置
const modificationHistoryColumns = ref([
  {
    label: '数据标准名称',
    prop: 'standardName',
    minWidth: 200
  },
  {
    label: '操作类型',
    prop: 'operationName',
    width: 100,
    align: 'center'
  },
  {
    label: '操作用户',
    prop: 'userName',
    width: 120,
    align: 'center'
  },
  {
    label: '用户角色',
    prop: 'userRole',
    width: 120,
    align: 'center'
  },
  {
    label: '操作时间',
    prop: 'operationTime',
    width: 180,
    align: 'center'
  },
  {
    label: '操作详情',
    prop: 'details',
    minWidth: 200
  }
])

// 修改历史记录搜索表单配置
const modificationHistorySearchFormProp = ref([
  {
    label: '用户名称',
    prop: 'userName',
    type: 'text',
    placeholder: '请输入用户名称'
  },
  {
    label: '操作类型',
    prop: 'operation',
    type: 'select',
    placeholder: '请选择操作类型',
    options: [
      { label: '全部', value: '' },
      { label: '新增', value: 'add' },
      { label: '编辑', value: 'edit' },
      { label: '删除', value: 'delete' },
      { label: '导入', value: 'import' }
    ]
  },
  {
    label: '开始时间',
    prop: 'startDate',
    type: 'date',
    placeholder: '请选择开始时间'
  },
  {
    label: '结束时间',
    prop: 'endDate',
    type: 'date',
    placeholder: '请选择结束时间'
  }
])

// 标准规范表格列配置
const ruleColumns = ref([
  {
    label: '序号',
    prop: 'sequence',
    width: 80,
    align: 'center'
  },
  {
    label: '数据标准规范名称',
    prop: 'ruleName',
    minWidth: 200
  },
  {
    label: '数据标准规范说明',
    prop: 'ruleDescription',
    minWidth: 250
  },
  {
    label: '创建时间',
    prop: 'createTime',
    width: 180,
    align: 'center'
  }
])

// 标准规范操作按钮配置
const ruleButtons = ref([
  {
    label: '查看',
    type: 'primary',
    size: 'small',
    action: 'detail'
  },
  {
    label: '编辑',
    type: 'success',
    size: 'small',
    action: 'edit'
  },
  {
    label: '删除',
    type: 'danger',
    size: 'small',
    action: 'delete'
  }
])

// 标准规范搜索表单配置
const ruleSearchFormProp = ref([
  {
    label: '数据标准规范名称',
    prop: 'ruleName',
    type: 'text',
    placeholder: '请输入数据标准规范名称'
  }
])

// 评估数据相关状态
const evaluationData = ref<EvaluationData>({
  conflictReports: {
    name: '标准冲突报告量',
    description: '每月收到的关于标准冲突或歧义的反馈数量',
    currentValue: 40,
    targetValue: 10,
    unit: '条',
    progress: 100, // 统一公式：40/10 = 400%，限制在100%
    isReverse: true // 值越小越好
  },
  updateCycle: {
    name: '标准更新周期',
    description: '平均多长时间更新一次标准',
    currentValue: 1,
    targetValue: 4,
    unit: '周',
    progress: 25, // 统一公式：1/4 = 25%
    isReverse: false // 值越大越好
  },
  qualityImprovement: {
    name: '数据质量问题下降率',
    description: '实施标准后数据质量问题下降百分比',
    currentValue: 20,
    targetValue: 50,
    unit: '%',
    progress: 40, // 统一公式：20/50 = 40%
    isReverse: false // 值越大越好
  },
  resolutionTime: {
    name: '标准问题解决平均时长',
    description: '从问题上报到解决的平均时间',
    currentValue: 2,
    targetValue: 0.5,
    unit: '小时',
    progress: 100, // 统一公式：2/0.5 = 400%，限制在100%
    isReverse: true // 值越小越好
  }
})
const currentEvaluateStandard = ref<DataStandardData | null>(null)

// 表单数据
const addForm = ref<Partial<DataStandardData>>({
  standardName: '',
  category: 'INT',
  dataTotal: 0,
  responsible: '',
  permissions: {
    access: true,
    edit: false,
    delete: false
  },
  content: '',
  remark: '',
  status: 'normal'
})

const editForm = ref<Partial<DataStandardData>>({})
const detailData = ref<DataStandardData | null>(null)

// 操作按钮配置
const buttons = [
  { label: '详情', code: 'detail' },
  { label: '评估', type: 'primary', code: 'evaluate' },
  { label: '编辑', type: 'warning', code: 'edit' },
  { label: '导出', code: 'export', more: true },
  { label: '分享', code: 'share', more: true },
  { label: '复制', code: 'copy', more: true },
  { label: '删除', type: 'danger', code: 'delete' }
]

// 生成异常数据
const generateAbnormalData = (standardId: string): AbnormalData[] => {
  console.log('生成异常数据 - 开始', standardId)
  const sources = ['数据库A', '数据库B', '外部接口', '文件导入', '手工录入']
  const fields = ['用户ID', '姓名', '手机号', '邮箱', '身份证号', '地址', '创建时间', '更新时间']
  const reasons = [
    '字段长度超出限制',
    '数据格式不符合规范',
    '必填字段为空',
    '数据类型不匹配',
    '重复数据',
    '数据值超出范围',
    '关联数据不存在',
    '数据编码错误'
  ]
  
  const abnormalList: AbnormalData[] = []
  const count = Math.floor(Math.random() * 20) + 10 // 10-30条异常数据
  console.log('生成数量:', count)
  
  for (let i = 1; i <= count; i++) {
    const triggerDate = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1, 
      Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), Math.floor(Math.random() * 60))
    
    const item = {
      id: `abnormal_${standardId}_${i.toString().padStart(3, '0')}`,
      source: sources[Math.floor(Math.random() * sources.length)],
      field: fields[Math.floor(Math.random() * fields.length)],
      value: `异常值${i}`,
      triggerTime: triggerDate.toLocaleString('zh-CN'),
      reason: reasons[Math.floor(Math.random() * reasons.length)]
    }
    console.log(`生成第${i}条数据:`, item)
    abnormalList.push(item)
  }
  
  console.log('最终生成的异常数据列表:', abnormalList)
  return abnormalList
}

// 生成模拟数据
const generateMockData = () => {
  const standardNames = [
    '用户基础信息数据标准',
    '订单交易数据标准', 
    '产品信息数据标准',
    '财务报表数据标准',
    '客户关系数据标准',
    '库存管理数据标准',
    '员工信息数据标准',
    '供应商数据标准',
    '日志数据标准',
    '统计报表数据标准'
  ]
  const categories = ['INT', 'VARCHAR', 'DATE', 'DECIMAL', 'TEXT']
  const responsiblePersons = ['张三', '李四', '王五', '赵六', '钱七']
  const statuses: ('normal' | 'abnormal')[] = ['normal', 'abnormal']
  const tagColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#9C27B0', '#FF9800', '#4CAF50']
  
  const data: DataStandardData[] = []
  
  for (let i = 1; i <= 50; i++) {
    const createDate = new Date(2024, Math.floor(i / 10), (i % 30) + 1, 14, i % 60, i % 60)
    
    data.push({
      id: `std_${i.toString().padStart(3, '0')}`,
      index: i,
      standardName: standardNames[Math.floor(Math.random() * standardNames.length)],
      category: categories[Math.floor(Math.random() * categories.length)],
      dataTotal: Math.floor(Math.random() * 100000) + 1000,
      dataVersion: createDate.toLocaleString('zh-CN'),
      responsible: responsiblePersons[Math.floor(Math.random() * responsiblePersons.length)],
      permissions: {
        access: Math.random() > 0.3,
        edit: Math.random() > 0.5,
        delete: Math.random() > 0.7
      },
      content: `这是${standardNames[Math.floor(Math.random() * standardNames.length)]}的详细内容描述，包含数据结构、字段定义、数据类型等信息。`,
      remark: `备注${i}：该数据标准需要定期维护和更新。`,
      passedDataRatio: Math.floor(Math.random() * 50) + 30,
      failedDataRatio: Math.floor(Math.random() * 30) + 10,
      complianceRate: Math.floor(Math.random() * 40) + 60,
      isFavorite: Math.random() > 0.7,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      tagColor: tagColors[Math.floor(Math.random() * tagColors.length)],
      createTime: createDate.toISOString(),
      updateTime: new Date().toISOString()
    })
  }
  
  return data
}

// 初始化数据
const initData = () => {
  loading.value = true
  setTimeout(() => {
    tableData.value = generateMockData()
    pagination.total = tableData.value.length
    loading.value = false
  }, 500)
}

// 获取过滤后的数据
const getFilteredData = () => {
  let filteredData = [...tableData.value]
  
  // 按数据标准名称筛选
  if (searchForm.value.standardName) {
    filteredData = filteredData.filter(item => 
      item.standardName.toLowerCase().includes(searchForm.value.standardName.toLowerCase())
    )
  }
  
  // 按分类筛选
  if (searchForm.value.category) {
    filteredData = filteredData.filter(item => item.category === searchForm.value.category)
  }
  
  return filteredData
}

// 获取当前页数据
const getCurrentPageData = () => {
  const filteredData = getFilteredData()
  
  // 应用排序
  if (sortConfig.value.prop && sortConfig.value.order) {
    filteredData.sort((a, b) => {
      let aVal = (a as any)[sortConfig.value.prop]
      let bVal = (b as any)[sortConfig.value.prop]
      
      // 特殊处理收藏字段
      if (sortConfig.value.prop === 'isFavorite') {
        aVal = aVal ? 1 : 0
        bVal = bVal ? 1 : 0
      }
      
      // 特殊处理数据版本字段（按时间排序）
      if (sortConfig.value.prop === 'dataVersion') {
        // 将时间字符串转换为时间戳进行比较
        const aTime = new Date(String(aVal)).getTime() || 0
        const bTime = new Date(String(bVal)).getTime() || 0
        aVal = aTime
        bVal = bTime
      }
      
      if (sortConfig.value.order === 'ascending') {
        return aVal > bVal ? 1 : -1
      } else {
        return aVal < bVal ? 1 : -1
      }
    })
  }
  
  // 更新分页总数
  pagination.total = filteredData.length
  
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filteredData.slice(start, end)
}

// 查询
const onSearch = () => {
  searchLoading.value = true
  setTimeout(() => {
    pagination.page = 1 // 重置到第一页
    searchLoading.value = false
  }, 300)
}

// 表格按钮点击事件
const onTableClickButton = ({ row, btn }: any) => {
  switch (btn.code) {
    case 'detail':
      handleDetail(row)
      break
    case 'evaluate':
      handleEvaluate(row)
      break
    case 'edit':
      handleEdit(row)
      break
    case 'export':
      handleExport(row)
      break
    case 'share':
      handleShare(row)
      break
    case 'copy':
      handleCopy(row)
      break
    case 'delete':
      handleDelete(row)
      break
    default:
      console.log('未知操作', btn, row)
  }
}

// 详情查看
const handleDetail = (row: DataStandardData) => {
  console.log('查看详情:', row)
  detailData.value = { ...row }
  showDetailDialog.value = true
  
  // 记录访问操作
  recordAccess(row)
}

// 加载已保存的评估数据
const loadEvaluationData = (standardId: string) => {
  const evaluationKey = `evaluation_${standardId}`
  const savedData = localStorage.getItem(evaluationKey)
  if (savedData) {
    try {
      const parsedData = JSON.parse(savedData)
      // 合并已保存的数据到当前评估数据
      Object.keys(parsedData).forEach(key => {
        if (evaluationData.value[key as keyof EvaluationData]) {
          Object.assign(evaluationData.value[key as keyof EvaluationData], parsedData[key])
        }
      })
      ElMessage.info('已加载该标准的历史评估数据')
    } catch (error) {
      console.error('加载评估数据失败:', error)
    }
  }
  // 重新计算所有进度
  updateAllProgress()
}

// 评估
const handleEvaluate = (row: DataStandardData) => {
  console.log('打开评估弹窗', row)
  currentEvaluateStandard.value = row
  // 加载该标准的历史评估数据
  loadEvaluationData(row.id)
  showEvaluateDialog.value = true
}

// 计算进度百分比
const calculateProgress = (item: EvaluationItem): number => {
  // 统一计算公式：进度 = 当前值 / 目标值
  const progress = (item.currentValue / item.targetValue) * 100
  return Math.min(100, Math.round(progress))
}

// 更新所有评估项的进度
const updateAllProgress = () => {
  Object.keys(evaluationData.value).forEach(key => {
    const item = evaluationData.value[key as keyof EvaluationData]
    item.progress = calculateProgress(item)
  })
}

// 更新评估数据（只允许更新当前值）
const updateEvaluationData = (key: keyof EvaluationData, field: 'currentValue', value: number) => {
  evaluationData.value[key][field] = value
  evaluationData.value[key].progress = calculateProgress(evaluationData.value[key])
}

// 评估确认
const handleConfirmEvaluate = () => {
  console.log('handleConfirmEvaluate 被调用', currentEvaluateStandard.value)
  if (!currentEvaluateStandard.value) {
    console.log('currentEvaluateStandard.value 为空，返回')
    return
  }
  
  try {
    // 保存评估数据到本地存储
    const evaluationKey = `evaluation_${currentEvaluateStandard.value.id}`
    localStorage.setItem(evaluationKey, JSON.stringify(evaluationData.value))
    console.log('评估数据已保存到localStorage', evaluationKey)
    
    ElMessage.success(`数据标准「${currentEvaluateStandard.value.standardName}」评估数据已保存`)
    showEvaluateDialog.value = false
    currentEvaluateStandard.value = null
    console.log('评估弹窗已关闭')
  } catch (error) {
    console.error('保存评估数据时出错:', error)
    ElMessage.error('保存评估数据失败')
  }
}

// 评估取消
const handleCancelEvaluate = () => {
  console.log('handleCancelEvaluate 被调用')
  showEvaluateDialog.value = false
  currentEvaluateStandard.value = null
  ElMessage.info('已取消评估操作')
  console.log('评估弹窗已取消关闭')
}

// 编辑
const handleEdit = (row: DataStandardData) => {
  console.log('打开编辑弹窗', row)
  editForm.value = { 
    ...row,
    permissions: { ...row.permissions }
  }
  showEditDialog.value = true
}

// 导出单行数据
const handleExport = async (row: DataStandardData) => {
  await exportToExcel([row], `数据标准_${row.standardName}`)
}

// 分享功能
const handleShare = async (row: DataStandardData) => {
  try {
    // 生成分享链接（这里可以根据实际需求调整链接格式）
    const shareUrl = `${window.location.origin}${window.location.pathname}?id=${row.id}&action=view`
    const shareText = `数据标准分享：${row.standardName}\n分类：${row.category}\n负责人：${row.responsible}\n链接：${shareUrl}`
    
    // 尝试使用现代浏览器的分享API
    if (navigator.share) {
      await navigator.share({
        title: `数据标准 - ${row.standardName}`,
        text: `分享数据标准：${row.standardName}`,
        url: shareUrl
      })
      ElMessage.success('分享成功')
    } else {
      // 降级到复制到剪贴板
      await navigator.clipboard.writeText(shareText)
      ElMessage.success('分享链接已复制到剪贴板')
    }
  } catch (error) {
    console.error('分享失败:', error)
    ElMessage.error('分享失败，请重试')
  }
}

// 复制功能
const handleCopy = async (row: DataStandardData) => {
  try {
    const copyText = `数据标准信息：
标准名称：${row.standardName}
分类：${row.category}
涉及数据总数：${row.dataTotal?.toLocaleString() || '0'}
数据版本：${row.dataVersion}
负责人：${row.responsible}
权限：访问-${row.permissions?.access ? '是' : '否'}，编辑-${row.permissions?.edit ? '是' : '否'}，删除-${row.permissions?.delete ? '是' : '否'}
标准内容：${row.content}
标准备注：${row.remark}
状态：${row.status === 'normal' ? '正常' : '异常'}
创建时间：${row.createTime}
更新时间：${row.updateTime}`
    
    await navigator.clipboard.writeText(copyText)
    ElMessage.success('数据标准信息已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请重试')
  }
}

// 查看异常数据
const handleViewAbnormal = (row: DataStandardData) => {
  console.log('查看异常数据 - 开始', row)
  currentAbnormalStandard.value = row
  const allAbnormalData = generateAbnormalData(row.id)
  console.log('生成的异常数据:', allAbnormalData)
  abnormalPagination.total = allAbnormalData.length
  
  // 分页获取数据
  const startIndex = (abnormalPagination.page - 1) * abnormalPagination.size
  const endIndex = startIndex + abnormalPagination.size
  abnormalData.value = allAbnormalData.slice(startIndex, endIndex)
  console.log('分页后的异常数据:', abnormalData.value)
  console.log('分页信息:', abnormalPagination)
  
  showAbnormalDialog.value = true
}

// 异常数据分页变化
const handleAbnormalPageChange = (page: number) => {
  abnormalPagination.page = page
  if (currentAbnormalStandard.value) {
    const allAbnormalData = generateAbnormalData(currentAbnormalStandard.value.id)
    const startIndex = (page - 1) * abnormalPagination.size
    const endIndex = startIndex + abnormalPagination.size
    abnormalData.value = allAbnormalData.slice(startIndex, endIndex)
  }
}

// 异常数据页大小变化
const handleAbnormalSizeChange = (size: number) => {
  abnormalPagination.size = size
  abnormalPagination.page = 1
  if (currentAbnormalStandard.value) {
    const allAbnormalData = generateAbnormalData(currentAbnormalStandard.value.id)
    abnormalPagination.total = allAbnormalData.length
    const startIndex = 0
    const endIndex = size
    abnormalData.value = allAbnormalData.slice(startIndex, endIndex)
  }
}

// 删除
const handleDelete = (row: DataStandardData) => {
  ElMessageBox.confirm(
    `确定要删除标准"${row.standardName}"吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      // 记录修改历史（在删除前记录）
      recordModificationHistory('delete', row, `删除数据标准：${row.standardName}，已确认不再使用`)
      
      tableData.value.splice(index, 1)
      pagination.total = tableData.value.length
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 新增
const handleAdd = () => {
  addForm.value = {
    standardName: '',
    category: 'INT',
    dataTotal: 0,
    responsible: '',
    permissions: {
      access: true,
      edit: false,
      delete: false
    },
    content: '',
    remark: '',
    status: 'normal'
  }
  showAddDialog.value = true
}

// 批量导入
const handleBatchImport = () => {
  showImportDialog.value = true
}



// 下载模板
const handleDownloadTemplate = async () => {
  await generateTemplate()
}

// 文件上传前的处理
const beforeUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件！')
    return false
  }
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB！')
    return false
  }
  importFile.value = file
  console.log('文件已选择:', file.name)
  return false // 阻止自动上传
}

// 文件选择变化处理
const handleFileChange = (file: any, fileList: any[]) => {
  if (file.raw) {
    const rawFile = file.raw as File
    const isExcel = rawFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                    rawFile.type === 'application/vnd.ms-excel'
    if (!isExcel) {
      ElMessage.error('只能上传 Excel 文件！')
      uploadRef.value?.clearFiles()
      return
    }
    const isLt10M = rawFile.size / 1024 / 1024 < 10
    if (!isLt10M) {
      ElMessage.error('上传文件大小不能超过 10MB！')
      uploadRef.value?.clearFiles()
      return
    }
    importFile.value = rawFile
    console.log('文件选择成功:', rawFile.name)
  }
}

// 智能导出 - 根据是否选择数据决定导出范围
const handleSmartExport = async () => {
  if (selectedRows.value.length > 0) {
    // 有选择数据时，导出选中的数据
    await exportToExcel(selectedRows.value, `数据标准管理_已选数据_${selectedRows.value.length}条`)
  } else {
    // 没有选择数据时，导出全部数据
    const allData = getFilteredData()
    await exportToExcel(allData, '数据标准管理_全量导出')
  }
}

// 确认导入
const handleConfirmImport = async () => {
  console.log('确认导入，当前文件:', importFile.value)
  if (!importFile.value) {
    ElMessage.warning('请先选择要导入的文件')
    return
  }
  
  try {
    const importedData = await importFromExcel(importFile.value)
    // 将导入的数据添加到现有数据中
    tableData.value.push(...importedData)
    // 重新计算索引
    tableData.value.forEach((item, index) => {
      item.index = index + 1
    })
    pagination.total = tableData.value.length
    
    // 记录修改历史
    recordModificationHistory('import', importedData, `批量导入数据标准，共导入${importedData.length}条记录`)
    
    // 重置导入状态
    importFile.value = null
    showImportDialog.value = false
    uploadRef.value?.clearFiles()
    
    ElMessage.success(`成功导入 ${importedData.length} 条数据`)
  } catch (error) {
    console.error('导入失败:', error)
  }
}

// 批量删除
const handleBatchDelete = () => {
  const hasSelection = selectedRows.value.length > 0
  const deleteCount = hasSelection ? selectedRows.value.length : tableData.value.length
  const deleteData = hasSelection ? selectedRows.value : tableData.value
  
  if (deleteCount === 0) {
    ElMessage.warning('没有数据可删除')
    return
  }
  
  const confirmMessage = hasSelection 
    ? `确定要删除选中的 ${deleteCount} 条数据吗？`
    : `未选择任何数据，确定要删除全部 ${deleteCount} 条数据吗？`
  
  ElMessageBox.confirm(
    confirmMessage,
    '确认批量删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: false
    }
  ).then(() => {
    // 记录修改历史（在删除前记录）
    const historyMessage = hasSelection 
      ? `批量删除${deleteCount}条选中的数据标准`
      : `批量删除全部${deleteCount}条数据标准`
    
    recordModificationHistory('delete', deleteData, historyMessage)
    
    if (hasSelection) {
      // 删除选中的数据
      const selectedIds = selectedRows.value.map(row => row.id)
      tableData.value = tableData.value.filter(item => !selectedIds.includes(item.id))
    } else {
      // 删除全部数据
      tableData.value = []
    }
    
    pagination.total = tableData.value.length
    selectedRows.value = []
    
    const successMessage = hasSelection 
      ? `成功删除${deleteCount}条选中数据`
      : `成功删除全部${deleteCount}条数据`
    
    ElMessage.success(successMessage)
  }).catch(() => {
    // 用户取消删除
  })
}

// 选择变化
const handleSelectionChange = (selection: DataStandardData[]) => {
  selectedRows.value = selection
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
  } else if (type === 'size') {
    pagination.size = val
    pagination.page = 1
  }
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 120
}

// 排序变化
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  sortConfig.value.prop = prop
  sortConfig.value.order = order
  // 排序变化后重新计算分页
  pagination.page = 1
}

// 确认新增
const handleConfirmAdd = () => {
  // 基本验证
  if (!addForm.value.standardName) {
    ElMessage.warning('请输入数据标准名称')
    return
  }
  if (!addForm.value.responsible) {
    ElMessage.warning('请选择负责人')
    return
  }
  
  // 生成新的数据项
  const newItem: DataStandardData = {
    id: `std_${Date.now()}`,
    index: tableData.value.length + 1,
    standardName: addForm.value.standardName || '',
    category: addForm.value.category || 'INT',
    dataTotal: addForm.value.dataTotal || 0,
    dataVersion: new Date().toLocaleString('zh-CN'),
    responsible: addForm.value.responsible || '',
    permissions: addForm.value.permissions || { access: true, edit: false, delete: false },
    content: addForm.value.content || '',
    remark: addForm.value.remark || '',
    passedDataRatio: Math.floor(Math.random() * 50) + 30,
    failedDataRatio: Math.floor(Math.random() * 30) + 10,
    complianceRate: Math.floor(Math.random() * 40) + 60,
    isFavorite: false,
    status: addForm.value.status || 'normal',
    tagColor: predefinedColors[Math.floor(Math.random() * predefinedColors.length)], // 随机分配标签颜色
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString()
  }
  
  // 添加到表格数据
  tableData.value.unshift(newItem)
  pagination.total = tableData.value.length
  
  // 记录修改历史
  recordModificationHistory('add', newItem, `新增数据标准：${newItem.standardName}，包含完整的字段定义和权限配置`)
  
  // 关闭弹窗并显示成功消息
  showAddDialog.value = false
  ElMessage.success('新增数据标准成功')
}

// 确认编辑
const handleConfirmEdit = () => {
  // 基本验证
  if (!editForm.value.standardName) {
    ElMessage.warning('请输入数据标准名称')
    return
  }
  if (!editForm.value.responsible) {
    ElMessage.warning('请选择负责人')
    return
  }
  
  // 查找并更新数据项
  const index = tableData.value.findIndex(item => item.id === editForm.value.id)
  if (index > -1) {
    // 保存旧值用于历史记录
    const oldValue = { ...tableData.value[index] }
    
    // 更新数据
    tableData.value[index] = {
      ...tableData.value[index],
      ...editForm.value,
      updateTime: new Date().toISOString()
    }
    
    // 记录修改历史
    recordModificationHistory('edit', tableData.value[index], `编辑数据标准：${tableData.value[index].standardName}，更新了字段信息和权限配置`, oldValue)
    
    // 如果当前详情弹窗显示的是同一条数据，也要更新
    if (detailData.value && detailData.value.id === editForm.value.id) {
      detailData.value = tableData.value[index]
    }
    
    ElMessage.success('编辑数据标准成功')
  } else {
    ElMessage.error('未找到要编辑的数据')
  }
  
  // 关闭弹窗
  showEditDialog.value = false
}

// 收藏/取消收藏
const toggleStar = (row: DataStandardData) => {
  row.isFavorite = !row.isFavorite
  ElMessage.success(row.isFavorite ? '已收藏' : '已取消收藏')
}

// =============== 颜色选择器相关功能函数 ===============

// 打开颜色选择器
const openColorPicker = (row: DataStandardData) => {
  currentColorPickerRow.value = row
  selectedColor.value = row.tagColor
  showColorPickerDialog.value = true
}

// 选择颜色
const selectColor = (color: string) => {
  selectedColor.value = color
}

// 确认颜色选择
const confirmColorChange = () => {
  if (currentColorPickerRow.value) {
    currentColorPickerRow.value.tagColor = selectedColor.value
    ElMessage.success('标签颜色已更新')
    showColorPickerDialog.value = false
    currentColorPickerRow.value = null
  }
}

// 取消颜色选择
const cancelColorChange = () => {
  showColorPickerDialog.value = false
  currentColorPickerRow.value = null
  selectedColor.value = ''
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '正常':
      return 'success'
    case '停用':
      return 'danger'
    case '待审核':
      return 'warning'
    default:
      return 'info'
  }
}

// =============== 标准规范相关功能函数 ===============

// 生成标准规范模拟数据
const generateStandardRuleData = (): StandardRule[] => {
  const rules: StandardRule[] = []
  const ruleNames = [
    '标准命名规则',
    '数据类型规范',
    '字段长度标准',
    '日期格式规范',
    '编码规则标准'
  ]
  const descriptions = [
    '数据标准名称的命名方式',
    '各种数据类型的使用规范',
    '字段长度的标准定义',
    '日期时间的格式标准',
    '编码字段的规则定义'
  ]
  const examples = [
    '参保人_基本信息_性别',
    'VARCHAR(50), INT, DATE等',
    '姓名字段长度不超过50个字符',
    'YYYY-MM-DD HH:mm:ss',
    '地区编码采用6位数字格式'
  ]

  for (let i = 0; i < 15; i++) {
    const index = i % ruleNames.length
    rules.push({
      id: `rule_${i + 1}`,
      sequence: String(i + 1).padStart(2, '0'),
      ruleName: ruleNames[index],
      ruleDescription: descriptions[index],
      ruleExample: examples[index],
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString('zh-CN')
    })
  }
  return rules
}

// 初始化标准规范数据
const initStandardRuleData = () => {
  standardRuleList.value = generateStandardRuleData()
  rulePagination.total = standardRuleList.value.length
}

// 获取过滤后的标准规范数据
const getFilteredRuleData = () => {
  let filtered = standardRuleList.value
  
  if (ruleSearchForm.value.ruleName) {
    filtered = filtered.filter(rule => 
      rule.ruleName.includes(ruleSearchForm.value.ruleName)
    )
  }
  
  rulePagination.total = filtered.length
  return filtered
}

// 获取当前页标准规范数据
const getCurrentPageRuleData = () => {
  const filtered = getFilteredRuleData()
  const start = (rulePagination.currentPage - 1) * rulePagination.pageSize
  const end = start + rulePagination.pageSize
  return filtered.slice(start, end)
}

// 打开标准规范列表弹窗
const handleOpenStandardRule = () => {
  initStandardRuleData()
  showStandardRuleDialog.value = true
}

// 标准规范搜索
const handleRuleSearch = () => {
  rulePagination.currentPage = 1
  getFilteredRuleData()
}

// 重置标准规范搜索
const handleRuleReset = () => {
  ruleSearchForm.value = { ruleName: '' }
  rulePagination.currentPage = 1
  getFilteredRuleData()
}

// 标准规范分页变化
const handleRulePageChange = (page: number) => {
  console.log('分页变化:', page)
  rulePagination.currentPage = page
}

// 标准规范页大小变化
const handleRuleSizeChange = (size: number) => {
  console.log('页大小变化:', size)
  rulePagination.pageSize = size
  rulePagination.currentPage = 1
}

// 查看标准规范详情
const handleRuleDetail = (rule: StandardRule) => {
  currentStandardRule.value = { ...rule }
  showRuleDetailDialog.value = true
}

// 新增标准规范
const handleRuleAdd = () => {
  ruleAddForm.value = {
    ruleName: '',
    ruleDescription: '',
    ruleExample: ''
  }
  showRuleAddDialog.value = true
}

// 确认新增标准规范
const handleConfirmRuleAdd = () => {
  console.log('确认新增标准规范', ruleAddForm.value)
  if (!ruleAddForm.value.ruleName.trim()) {
    ElMessage.error('请输入标准规范名称')
    return
  }
  if (!ruleAddForm.value.ruleDescription.trim()) {
    ElMessage.error('请输入标准规范说明')
    return
  }
  
  const newRule: StandardRule = {
    id: `rule_${Date.now()}`,
    sequence: String(standardRuleList.value.length + 1).padStart(2, '0'),
    ruleName: ruleAddForm.value.ruleName,
    ruleDescription: ruleAddForm.value.ruleDescription,
    ruleExample: ruleAddForm.value.ruleExample,
    createTime: new Date().toLocaleString('zh-CN')
  }
  
  standardRuleList.value.unshift(newRule)
  rulePagination.total = standardRuleList.value.length
  
  ElMessage.success('创建标准规范成功')
  showRuleAddDialog.value = false
}

// 取消新增标准规范
const handleCancelRuleAdd = () => {
  console.log('取消新增标准规范')
  showRuleAddDialog.value = false
}

// 编辑标准规范
const handleRuleEdit = (rule: StandardRule) => {
  ruleEditForm.value = {
    id: rule.id,
    ruleName: rule.ruleName,
    ruleDescription: rule.ruleDescription,
    ruleExample: rule.ruleExample
  }
  showRuleEditDialog.value = true
}

// 确认编辑标准规范
const handleConfirmRuleEdit = () => {
  console.log('确认编辑标准规范', ruleEditForm.value)
  if (!ruleEditForm.value.ruleName.trim()) {
    ElMessage.error('请输入标准规范名称')
    return
  }
  if (!ruleEditForm.value.ruleDescription.trim()) {
    ElMessage.error('请输入标准规范说明')
    return
  }
  
  const index = standardRuleList.value.findIndex(rule => rule.id === ruleEditForm.value.id)
  if (index !== -1) {
    standardRuleList.value[index] = {
      ...standardRuleList.value[index],
      ruleName: ruleEditForm.value.ruleName,
      ruleDescription: ruleEditForm.value.ruleDescription,
      ruleExample: ruleEditForm.value.ruleExample
    }
    
    ElMessage.success('编辑标准规范成功')
    showRuleEditDialog.value = false
  } else {
    ElMessage.error('未找到要编辑的标准规范')
  }
}

// 取消编辑标准规范
const handleCancelRuleEdit = () => {
  console.log('取消编辑标准规范')
  showRuleEditDialog.value = false
}

// 删除标准规范
const handleRuleDelete = (rule: StandardRule) => {
  ElMessageBox.confirm(
    `确定要删除标准规范"${rule.ruleName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = standardRuleList.value.findIndex(r => r.id === rule.id)
    if (index !== -1) {
      standardRuleList.value.splice(index, 1)
      rulePagination.total = standardRuleList.value.length
      
      // 如果当前页没有数据了，回到上一页
      const maxPage = Math.ceil(rulePagination.total / rulePagination.pageSize)
      if (rulePagination.currentPage > maxPage && maxPage > 0) {
        rulePagination.currentPage = maxPage
      }
      
      ElMessage.success('删除标准规范成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 标准规范表格按钮点击处理
const onRuleTableClickButton = ({ row, btn }: any) => {
  switch (btn.action) {
    case 'detail':
      handleRuleDetail(row)
      break
    case 'edit':
      handleRuleEdit(row)
      break
    case 'delete':
      handleRuleDelete(row)
      break
    default:
      console.log('未知操作:', btn.action)
  }
}

// =============== 访问记录相关功能函数 ===============

// 获取当前用户信息
const getCurrentUserInfo = () => {
  try {
    const userInfoStr = localStorage.getItem('currentUserInfo')
    if (userInfoStr) {
      return JSON.parse(userInfoStr)
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
  // 返回默认用户信息
  return {
    id: 'user_001',
    name: '当前用户',
    role: '管理员',
    department: '信息技术部'
  }
}

// 记录访问操作
const recordAccess = (standardData: DataStandardData) => {
  const currentUser = getCurrentUserInfo()
  const accessRecord: AccessRecord = {
    id: `access_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    standardId: standardData.id,
    standardName: standardData.standardName,
    userName: currentUser.name || '未知用户',
    userId: currentUser.id || 'unknown',
    accessTime: new Date().toLocaleString('zh-CN'),
    operation: '查看详情',
    userRole: currentUser.role || '普通用户',
    department: currentUser.department || '未知部门'
  }
  
  // 将访问记录存储到localStorage
  try {
    const existingRecords = localStorage.getItem('dataStandardAccessRecords')
    let records: AccessRecord[] = []
    if (existingRecords) {
      records = JSON.parse(existingRecords)
    }
    records.unshift(accessRecord) // 新记录添加到开头
    
    // 限制记录数量，最多保存1000条
    if (records.length > 1000) {
      records = records.slice(0, 1000)
    }
    
    localStorage.setItem('dataStandardAccessRecords', JSON.stringify(records))
    console.log('访问记录已保存:', accessRecord)
  } catch (error) {
    console.error('保存访问记录失败:', error)
  }
}

// 生成模拟访问记录数据
const generateMockAccessRecords = (): AccessRecord[] => {
  const mockUsers = [
    { name: '张三', role: '管理员', department: '信息技术部' },
    { name: '李四', role: '数据分析师', department: '数据中心' },
    { name: '王五', role: '业务专员', department: '业务部门' },
    { name: '赵六', role: '系统管理员', department: '运维部' },
    { name: '钱七', role: '质量管理员', department: '质量管理部' }
  ]
  
  // 模拟数据标准名称
  const mockStandards = [
    '用户基本信息标准',
    '订单数据标准',
    '产品信息标准',
    '财务数据标准',
    '客户关系标准',
    '库存管理标准',
    '销售数据标准',
    '人员信息标准',
    '供应商数据标准',
    '合同管理标准'
  ]
  
  const operations = ['查看详情']
  const records: AccessRecord[] = []
  
  // 生成30条模拟记录
  for (let i = 0; i < 30; i++) {
    const user = mockUsers[Math.floor(Math.random() * mockUsers.length)]
    const standardName = mockStandards[Math.floor(Math.random() * mockStandards.length)]
    const accessTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // 最近30天内的随机时间
    
    records.push({
      id: `mock_access_${i + 1}`,
      standardId: `standard_${i + 1}`,
      standardName: standardName,
      userName: user.name,
      userId: `user_${i + 1}`,
      accessTime: accessTime.toLocaleString('zh-CN'),
      operation: operations[Math.floor(Math.random() * operations.length)],
      userRole: user.role,
      department: user.department
    })
  }
  
  return records.sort((a, b) => new Date(b.accessTime).getTime() - new Date(a.accessTime).getTime())
}

// 初始化访问记录数据
const initAccessRecordData = () => {
  try {
    const existingRecords = localStorage.getItem('dataStandardAccessRecords')
    if (existingRecords) {
      accessRecordList.value = JSON.parse(existingRecords)
    } else {
      // 如果没有现有记录，生成模拟数据
      const mockRecords = generateMockAccessRecords()
      accessRecordList.value = mockRecords
      localStorage.setItem('dataStandardAccessRecords', JSON.stringify(mockRecords))
    }
    accessRecordPagination.total = accessRecordList.value.length
  } catch (error) {
    console.error('初始化访问记录数据失败:', error)
    accessRecordList.value = []
    accessRecordPagination.total = 0
  }
}

// 获取当前页访问记录数据
const getCurrentPageAccessRecords = () => {
  const allRecords = [...accessRecordList.value]
  accessRecordPagination.total = allRecords.length
  
  const start = (accessRecordPagination.currentPage - 1) * accessRecordPagination.pageSize
  const end = start + accessRecordPagination.pageSize
  
  return allRecords.slice(start, end)
}

// 打开访问记录弹窗
const handleOpenAccessRecord = () => {
  initAccessRecordData()
  showAccessRecordDialog.value = true
}



// 访问记录分页变化
const handleAccessPageChange = (page: number) => {
  accessRecordPagination.currentPage = page
}

// 访问记录页大小变化
const handleAccessSizeChange = (size: number) => {
  accessRecordPagination.pageSize = size
  accessRecordPagination.currentPage = 1
}

// =============== 修改历史记录相关功能函数 ===============

// 记录修改历史操作
const recordModificationHistory = (operation: 'add' | 'edit' | 'delete' | 'import', standardData: DataStandardData | DataStandardData[], details?: string, oldValue?: any) => {
  const currentUser = getCurrentUserInfo()
  const operationNames = {
    add: '新增',
    edit: '编辑', 
    delete: '删除',
    import: '导入'
  }
  
  // 处理批量操作（如批量导入、批量删除）
  const records: ModificationHistory[] = []
  const dataArray = Array.isArray(standardData) ? standardData : [standardData]
  
  dataArray.forEach((data, index) => {
    const historyRecord: ModificationHistory = {
      id: `history_${Date.now()}_${index}`,
      standardId: operation === 'delete' ? undefined : data.id,
      standardName: data.standardName,
      operation,
      operationName: operationNames[operation],
      userName: currentUser.name,
      userId: currentUser.id,
      operationTime: new Date().toLocaleString('zh-CN'),
      userRole: currentUser.role || '普通用户',
      department: currentUser.department || '未知部门',
      details: details || `${operationNames[operation]}数据标准：${data.standardName}`,
      oldValue: operation === 'edit' ? oldValue : undefined,
      newValue: operation === 'delete' ? undefined : data
    }
    records.push(historyRecord)
  })
  
  // 将修改历史记录存储到localStorage
  try {
    const existingRecords = localStorage.getItem('dataStandardModificationHistory')
    let allRecords: ModificationHistory[] = []
    if (existingRecords) {
      allRecords = JSON.parse(existingRecords)
    }
    allRecords.unshift(...records) // 新记录添加到开头
    
    // 限制记录数量，最多保存2000条
    if (allRecords.length > 2000) {
      allRecords = allRecords.slice(0, 2000)
    }
    
    localStorage.setItem('dataStandardModificationHistory', JSON.stringify(allRecords))
    console.log('修改历史记录已保存:', records)
  } catch (error) {
    console.error('保存修改历史记录失败:', error)
  }
}

// 生成模拟修改历史记录数据
const generateMockModificationHistory = (): ModificationHistory[] => {
  const mockUsers = [
    { name: '张三', id: 'user_001', role: '管理员', department: '数据管理部' },
    { name: '李四', id: 'user_002', role: '数据分析师', department: '业务分析部' },
    { name: '王五', id: 'user_003', role: '开发工程师', department: '技术开发部' },
    { name: '赵六', id: 'user_004', role: '产品经理', department: '产品规划部' },
    { name: '钱七', id: 'user_005', role: '质量工程师', department: '质量保证部' }
  ]
  
  const mockStandards = [
    '用户基础信息标准', '订单数据标准', '商品信息标准', '支付流水标准', '库存管理标准',
    '客户关系标准', '财务报表标准', '营销活动标准', '物流配送标准', '售后服务标准'
  ]
  
  const operations = [
    { type: 'add', name: '新增' },
    { type: 'edit', name: '编辑' },
    { type: 'delete', name: '删除' },
    { type: 'import', name: '导入' }
  ]
  
  const mockHistory: ModificationHistory[] = []
  
  // 生成50条模拟历史记录
  for (let i = 0; i < 50; i++) {
    const user = mockUsers[Math.floor(Math.random() * mockUsers.length)]
    const standard = mockStandards[Math.floor(Math.random() * mockStandards.length)]
    const operation = operations[Math.floor(Math.random() * operations.length)]
    
    // 生成随机时间（最近30天内）
    const randomDays = Math.floor(Math.random() * 30)
    const randomHours = Math.floor(Math.random() * 24)
    const randomMinutes = Math.floor(Math.random() * 60)
    const operationTime = new Date()
    operationTime.setDate(operationTime.getDate() - randomDays)
    operationTime.setHours(randomHours, randomMinutes, 0, 0)
    
    const details = {
      add: `新增数据标准：${standard}，包含完整的字段定义和验证规则`,
      edit: `修改数据标准：${standard}，更新了字段类型和验证条件`,
      delete: `删除数据标准：${standard}，已确认不再使用`,
      import: `批量导入数据标准，包含${standard}等${Math.floor(Math.random() * 10) + 1}条记录`
    }
    
    mockHistory.push({
      id: `mock_history_${i + 1}`,
      standardId: operation.type === 'delete' ? undefined : `std_${i + 1}`,
      standardName: standard,
      operation: operation.type as 'add' | 'edit' | 'delete' | 'import',
      operationName: operation.name,
      userName: user.name,
      userId: user.id,
      operationTime: operationTime.toLocaleString('zh-CN'),
      userRole: user.role,
      department: user.department,
      details: details[operation.type as keyof typeof details]
    })
  }
  
  // 按时间倒序排列
  return mockHistory.sort((a, b) => new Date(b.operationTime).getTime() - new Date(a.operationTime).getTime())
}

// 初始化修改历史记录数据
const initModificationHistoryData = () => {
  try {
    const existingRecords = localStorage.getItem('dataStandardModificationHistory')
    if (!existingRecords) {
      // 如果没有现有记录，生成模拟数据
      const mockData = generateMockModificationHistory()
      localStorage.setItem('dataStandardModificationHistory', JSON.stringify(mockData))
      modificationHistoryList.value = mockData
      console.log('已生成模拟修改历史记录数据')
    } else {
      modificationHistoryList.value = JSON.parse(existingRecords)
      console.log('已加载现有修改历史记录数据')
    }
    
    // 更新分页信息
    const filteredData = getFilteredModificationHistoryData()
    modificationHistoryPagination.total = filteredData.length
  } catch (error) {
    console.error('初始化修改历史记录数据失败:', error)
    modificationHistoryList.value = []
  }
}

// 获取过滤后的修改历史记录数据
const getFilteredModificationHistoryData = () => {
  let filteredData = [...modificationHistoryList.value]
  
  // 按用户名称过滤
  if (modificationHistorySearchForm.value.userName) {
    filteredData = filteredData.filter(record => 
      record.userName.includes(modificationHistorySearchForm.value.userName)
    )
  }
  
  // 按操作类型过滤
  if (modificationHistorySearchForm.value.operation) {
    filteredData = filteredData.filter(record => 
      record.operation === modificationHistorySearchForm.value.operation
    )
  }
  
  // 按时间范围过滤
  if (modificationHistorySearchForm.value.startDate) {
    const startDate = new Date(modificationHistorySearchForm.value.startDate)
    filteredData = filteredData.filter(record => 
      new Date(record.operationTime) >= startDate
    )
  }
  
  if (modificationHistorySearchForm.value.endDate) {
    const endDate = new Date(modificationHistorySearchForm.value.endDate)
    endDate.setHours(23, 59, 59, 999) // 设置为当天结束时间
    filteredData = filteredData.filter(record => 
      new Date(record.operationTime) <= endDate
    )
  }
  
  return filteredData
}

// 获取当前页修改历史记录数据
const getCurrentPageModificationHistoryData = () => {
  const filteredData = getFilteredModificationHistoryData()
  modificationHistoryPagination.total = filteredData.length
  
  const start = (modificationHistoryPagination.currentPage - 1) * modificationHistoryPagination.pageSize
  const end = start + modificationHistoryPagination.pageSize
  
  return filteredData.slice(start, end)
}

// 打开修改历史记录弹窗
const handleOpenModificationHistory = () => {
  initModificationHistoryData()
  showModificationHistoryDialog.value = true
}

// 修改历史记录搜索
const handleModificationHistorySearch = () => {
  modificationHistoryPagination.currentPage = 1
}

// 修改历史记录分页变化
const handleModificationHistoryPageChange = (page: number) => {
  modificationHistoryPagination.currentPage = page
}

// 修改历史记录页大小变化
const handleModificationHistorySizeChange = (size: number) => {
  modificationHistoryPagination.pageSize = size
  modificationHistoryPagination.currentPage = 1
}

// 获取操作类型标签类型
const getOperationTagType = (operation: string) => {
  switch (operation) {
    case 'add':
      return 'success'
    case 'edit':
      return 'warning'
    case 'delete':
      return 'danger'
    case 'import':
      return 'info'
    default:
      return 'info'
  }
}

// 初始化
onMounted(() => {
  initData()
  // 初始化评估数据进度
  updateAllProgress()
  // 初始化访问记录数据，确保一开始就有模拟数据
  initAccessRecordData()
})
</script>

<route>
{
  meta: {
    title: '数据标准管理',
    ignoreLabel: false
  }
}
</route>

<template>
  <div class="data-standard-management">
    <Block
      title="数据标准管理"
      :enable-expand-content="true"
      :enableBackButton="false"
      :enable-fixed-height="true"
      :enable-close-button="false"
      @content-expand="() => {}"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button size="small" type="primary" @click="handleAdd">添加</el-button>
        <el-button size="small" type="success" @click="handleBatchImport">导入</el-button>
        <el-button size="small" type="info" @click="handleSmartExport" :loading="isExporting">导出</el-button>
        <el-button size="small" type="danger" @click="handleBatchDelete">批量删除</el-button>
        <el-button size="small" type="primary" @click="handleOpenStandardRule">标准规范</el-button>
        <el-button size="small" type="warning" @click="handleOpenAccessRecord">访问记录</el-button>
        <el-button size="small" type="info" @click="handleOpenModificationHistory">修改历史记录</el-button>
      </template>

      <template #expand>
        <!-- 搜索区域 -->
        <div class="search" v-loading="searchLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="3"
            :label-width="124"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
          />
        </div>
      </template>

      <!-- 表格列表 -->
      <TableV2
        ref="tableRef"
        :columns="columns"
        :defaultTableData="getCurrentPageData()"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :enable-index="false"
        :height="tableHeight"
        :loading="loading"
        :buttons="buttons"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @click-button="onTableClickButton"
      >
        <!-- 序号列 -->
        <template #index="{ row }">
          {{ (pagination.page - 1) * pagination.size + row.index }}
        </template>

        <!-- 权限列自定义显示 -->
        <template #permissions="{ row }">
          <div class="permissions-display">
            <template v-for="(value, key) in row.permissions" :key="key">
              <el-tag
                v-if="typeof key === 'string' && key in PERMISSION_LABELS"
                :type="value ? 'success' : 'info'"
                size="small"
                style="margin-right: 4px; margin-bottom: 2px;"
              >
                {{ PERMISSION_LABELS[key as keyof typeof PERMISSION_LABELS] }}
              </el-tag>
            </template>
          </div>
        </template>

        <!-- 标签列自定义显示 -->
        <template #tagColor="{ row }">
          <div 
            class="tag-color-display" 
            :style="{ backgroundColor: row.tagColor }"
            @click="openColorPicker(row)"
            :title="'点击更换颜色'"
          >
          </div>
        </template>

        <!-- 收藏列自定义显示 -->
        <template #isFavorite="{ row }">
          <el-button
            type="text"
            @click="toggleStar(row)"
            style="padding: 0;"
          >
            <el-icon :color="row.isFavorite ? '#f56c6c' : '#c0c4cc'" size="18">
              <StarFilled v-if="row.isFavorite" />
              <Star v-else />
            </el-icon>
          </el-button>
        </template>

        <!-- 状态列 -->
        <template #status="{ row }">
          <div style="display: flex; align-items: center; gap: 8px;">
            <el-tag :type="row.status === 'normal' ? 'success' : 'danger'" size="small">
              {{ row.status === 'normal' ? '正常' : '异常' }}
            </el-tag>
            <el-button 
              v-if="row.status === 'abnormal'"
              type="text" 
              size="small" 
              @click="handleViewAbnormal(row)"
              style="padding: 0; margin-left: 4px;"
            >
              查看
            </el-button>
          </div>
        </template>
      </TableV2>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      />
    </Block>

    <!-- 详情弹窗 -->
    <DialogComp
      v-model:visible="showDetailDialog"
      title="数据标准详情"
      width="700px"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="() => showDetailDialog = false"
      @closed="() => showDetailDialog = false"
    >
      <div v-if="detailData" class="detail-content">
        <el-form label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="名称">
                <el-input :value="detailData.standardName" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分类">
                <el-input :value="detailData.category" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="涉及数据量">
                <el-input :value="detailData.dataTotal?.toLocaleString() || '0'" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="当前版本">
                <el-input :value="detailData.dataVersion" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="责任人">
                <el-input :value="detailData.responsible" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="权限">
                <div class="permission-checkboxes">
                  <el-checkbox :model-value="detailData.permissions?.access" disabled>访问</el-checkbox>
                  <el-checkbox :model-value="detailData.permissions?.edit" disabled>编辑</el-checkbox>
                  <el-checkbox :model-value="detailData.permissions?.delete" disabled>删除</el-checkbox>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="数据标准内容">
            <el-input :value="detailData.content" type="textarea" :rows="4" readonly />
          </el-form-item>
          <el-form-item label="备注">
            <el-input :value="detailData.remark" type="textarea" :rows="2" readonly />
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="已通过数据占比">
                <div class="progress-item">
                  <el-progress :percentage="detailData.passedDataRatio || 0" :show-text="false" />
                  <span class="progress-text">{{ detailData.passedDataRatio || 0 }}%</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="未通过数据占比">
                <div class="progress-item">
                  <el-progress :percentage="detailData.failedDataRatio || 0" :show-text="false" status="exception" />
                  <span class="progress-text">{{ detailData.failedDataRatio || 0 }}%</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="数据合规率">
            <div class="progress-item">
              <el-progress 
                :percentage="detailData.complianceRate || 0" 
                :show-text="false" 
                :status="(detailData.complianceRate || 0) >= 80 ? 'success' : (detailData.complianceRate || 0) >= 60 ? '' : 'exception'"
              />
              <span class="progress-text">{{ detailData.complianceRate || 0 }}%</span>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </DialogComp>

    <!-- 新增数据标准弹窗 -->
    <DialogComp
      v-model:visible="showAddDialog"
      title="新增数据标准"
      width="600px"
      @click-confirm="handleConfirmAdd"
      @click-cancel="() => showAddDialog = false"
      @closed="() => showAddDialog = false"
    >
      <el-form :model="addForm" label-width="120px">
        <el-form-item label="数据标准名称" required>
          <el-input v-model="addForm.standardName" placeholder="请输入数据标准名称" />
        </el-form-item>
        <el-form-item label="分类" required>
          <el-select v-model="addForm.category" placeholder="请选择分类" style="width: 100%">
            <el-option label="INT" value="INT" />
            <el-option label="VARCHAR" value="VARCHAR" />
            <el-option label="DATE" value="DATE" />
            <el-option label="DECIMAL" value="DECIMAL" />
            <el-option label="TEXT" value="TEXT" />
          </el-select>
        </el-form-item>
        <el-form-item label="涉及数据总数">
          <el-input-number v-model="addForm.dataTotal" :min="0" placeholder="请输入数据总数" style="width: 100%" />
        </el-form-item>
        <el-form-item label="负责人">
          <el-select v-model="addForm.responsible" placeholder="请选择负责人" style="width: 100%">
            <el-option label="张三" value="张三" />
            <el-option label="李四" value="李四" />
            <el-option label="王五" value="王五" />
            <el-option label="赵六" value="赵六" />
            <el-option label="钱七" value="钱七" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="addForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="正常" value="normal" />
            <el-option label="异常" value="abnormal" />
          </el-select>
        </el-form-item>
        <el-form-item label="标准权限" v-if="addForm.permissions">
          <div class="permission-checkboxes">
            <el-checkbox v-model="addForm.permissions.access" label="访问" />
            <el-checkbox v-model="addForm.permissions.edit" label="编辑" />
            <el-checkbox v-model="addForm.permissions.delete" label="删除" />
          </div>
        </el-form-item>
        <el-form-item label="标准内容">
          <el-input
            v-model="addForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="标准备注">
          <el-input
            v-model="addForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
    </DialogComp>

    <!-- 编辑弹窗 -->
    <DialogComp
      v-model:visible="showEditDialog"
      title="编辑数据标准"
      width="600px"
      @click-confirm="handleConfirmEdit"
      @click-cancel="() => showEditDialog = false"
      @closed="() => showEditDialog = false"
    >
      <el-form :model="editForm" label-width="120px">
        <el-form-item label="数据标准名称" required>
          <el-input v-model="editForm.standardName" placeholder="请输入数据标准名称" />
        </el-form-item>
        <el-form-item label="分类" required>
          <el-select v-model="editForm.category" placeholder="请选择分类" style="width: 100%">
            <el-option label="INT" value="INT" />
            <el-option label="VARCHAR" value="VARCHAR" />
            <el-option label="DATE" value="DATE" />
            <el-option label="DECIMAL" value="DECIMAL" />
            <el-option label="TEXT" value="TEXT" />
          </el-select>
        </el-form-item>
        <el-form-item label="涉及数据总数">
          <el-input-number v-model="editForm.dataTotal" :min="0" placeholder="请输入数据总数" style="width: 100%" />
        </el-form-item>
        <el-form-item label="负责人">
          <el-select v-model="editForm.responsible" placeholder="请选择负责人" style="width: 100%">
            <el-option label="张三" value="张三" />
            <el-option label="李四" value="李四" />
            <el-option label="王五" value="王五" />
            <el-option label="赵六" value="赵六" />
            <el-option label="钱七" value="钱七" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="editForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="正常" value="normal" />
            <el-option label="异常" value="abnormal" />
          </el-select>
        </el-form-item>
        <el-form-item label="标准权限" v-if="editForm.permissions">
          <div class="permission-checkboxes">
            <el-checkbox v-model="editForm.permissions.access" label="访问" />
            <el-checkbox v-model="editForm.permissions.edit" label="编辑" />
            <el-checkbox v-model="editForm.permissions.delete" label="删除" />
          </div>
        </el-form-item>
        <el-form-item label="标准内容">
          <el-input
            v-model="editForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="标准备注">
          <el-input
            v-model="editForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
    </DialogComp>

    <!-- 导入弹窗 -->
    <DialogComp
      v-model:visible="showImportDialog"
      title="批量导入数据标准"
      width="600px"
      confirm-text="确认导入"
      cancel-text="取消"
      :confirm-loading="isImporting"
      @click-confirm="handleConfirmImport"
      @click-cancel="() => showImportDialog = false"
      @closed="() => showImportDialog = false"
    >
      <div class="import-content">
        <div class="import-tips">
          <el-alert
            title="导入说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="tips-content">
                <p>1. 请先下载导入模板，按照模板格式填写数据</p>
                <p>2. 支持的文件格式：.xlsx、.xls</p>
                <p>3. 文件大小不能超过 10MB</p>
                <p>4. 必填字段：数据标准名称、分类</p>
              </div>
            </template>
          </el-alert>
        </div>
        
        <div class="template-download">
          <el-button 
            type="primary" 
            :icon="Download" 
            @click="handleDownloadTemplate"
            style="margin: 20px 0;"
          >
            下载导入模板
          </el-button>
        </div>
        
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          action="#"
          :auto-upload="false"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          accept=".xlsx,.xls"
          :limit="1"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 xlsx/xls 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
        
        <div v-if="importFile" class="file-info">
          <el-tag type="success" style="margin-top: 10px;">
            已选择文件：{{ importFile.name }}
          </el-tag>
        </div>
      </div>
    </DialogComp>

    <!-- 异常数据查看弹窗 -->
    <DialogComp
      v-model:visible="showAbnormalDialog"
      :title="`未合规数据一览 - ${currentAbnormalStandard?.standardName || ''}`"
      width="900px"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="() => showAbnormalDialog = false"
      @closed="() => showAbnormalDialog = false"
    >
      <div class="abnormal-content">
        <div class="abnormal-header">
          <el-alert
            title="异常数据说明"
            description="以下是检测到的不符合数据标准的异常记录，请及时处理以确保数据质量。"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>
        
        <div class="abnormal-table">
          <TableV2
            :defaultTableData="abnormalData"
            :columns="abnormalColumns"
            :height="400"
            :enable-toolbar="false"
            :enable-own-button="false"
            :enable-selection="false"
            :enable-index="false"
            border
            stripe
            style="width: 100%"
          />
        </div>
        
        <div class="abnormal-pagination">
          <Pagination
            :total="abnormalPagination.total"
            :current-page="abnormalPagination.page"
            :page-size="abnormalPagination.size"
            @current-change="handleAbnormalPageChange"
            @size-change="handleAbnormalSizeChange"
          />
        </div>
      </div>
    </DialogComp>

    <!-- 评估弹窗 -->
    <DialogComp
      v-model:visible="showEvaluateDialog"
      title="数据标准评估"
      width="600px"
      @click-confirm="handleConfirmEvaluate"
      @click-cancel="handleCancelEvaluate"
      @closed="() => showEvaluateDialog = false"
    >
      <div class="evaluate-content">
        <div class="evaluate-item" v-for="(item, key) in evaluationData" :key="key">
          <div class="evaluate-header">
            <div class="evaluate-icon">
              <el-icon><View /></el-icon>
            </div>
            <div class="evaluate-info">
              <div class="evaluate-name">{{ item.name }}</div>
              <div class="evaluate-description">{{ item.description }}</div>
            </div>
          </div>
          
          <div class="evaluate-inputs">
            <div class="input-group">
              <label class="input-label">当前值：</label>
              <el-input-number
                v-model="item.currentValue"
                :min="0"
                :precision="item.unit === '%' || item.unit === '小时' ? 1 : 0"
                :step="item.unit === '%' || item.unit === '小时' ? 0.1 : 1"
                size="small"
                @change="updateEvaluationData(key as keyof EvaluationData, 'currentValue', item.currentValue)"
              />
              <span class="unit-label">{{ item.unit }}</span>
            </div>
            
            <div class="target-display">
              <label class="target-label">目标值：</label>
              <span class="target-value">{{ item.targetValue }}{{ item.unit }}</span>
            </div>
          </div>
          
          <div class="evaluate-progress">
            <div class="progress-label">
              当前与评估标准差距
              <span class="progress-tip">
                {{ item.isReverse ? '(值越小越好)' : '(值越大越好)' }}
              </span>
            </div>
            <div class="progress-bar">
              <el-progress 
                :percentage="item.progress" 
                :stroke-width="8"
                :color="item.progress >= 80 ? '#67C23A' : item.progress >= 60 ? '#E6A23C' : '#F56C6C'"
                :show-text="true"
              />
            </div>
          </div>
        </div>
      </div>
    </DialogComp>

    <!-- 标准规范列表弹窗 -->
    <DialogComp
      v-model:visible="showStandardRuleDialog"
      title="数据标准规范"
      width="1200px"
      :visible-confirm-button="false"
      :visible-cancel-button="false"
      @click-cancel="showStandardRuleDialog = false"
      @closed="showStandardRuleDialog = false"
    >
      <div class="standard-rule-content">
        <!-- 搜索区域 -->
        <div class="rule-search-area">
          <Form
            :props="ruleSearchFormProp"
            v-model="ruleSearchForm"
            :column-count="2"
            :label-width="140"
            :enable-reset="true"
            confirm-text="查询"
            reset-text="重置"
            button-vertical="flowing"
            @submit="handleRuleSearch"
            @reset="handleRuleReset"
          />
          <div class="rule-action-buttons">
            <el-button type="primary" @click="handleRuleAdd">创建数据标准规范</el-button>
          </div>
        </div>

        <!-- 标准规范表格 -->
        <div class="rule-table-area">
          <TableV2
            :columns="ruleColumns"
            :defaultTableData="getCurrentPageRuleData()"
            :enable-toolbar="false"
            :enable-own-button="false"
            :enable-selection="false"
            :enable-index="false"
            :height="400"
            :loading="false"
            :buttons="ruleButtons"
            @click-button="onRuleTableClickButton"
          />
        </div>

        <!-- 分页 -->
        <div class="rule-pagination-area">
          <Pagination
            :current-page="rulePagination.currentPage"
            :page-size="rulePagination.pageSize"
            :total="rulePagination.total"
            @current-change="handleRulePageChange"
            @size-change="handleRuleSizeChange"
          />
        </div>
      </div>
    </DialogComp>

    <!-- 查看标准规范详情弹窗 -->
    <DialogComp
      v-model:visible="showRuleDetailDialog"
      title="查看数据标准规范"
      width="600px"
      :visible-confirm-button="false"
      cancel-button-text="关闭"
      @click-cancel="showRuleDetailDialog = false"
      @closed="showRuleDetailDialog = false"
    >
      <div v-if="currentStandardRule" class="rule-detail-content">
        <el-form :model="currentStandardRule" label-width="140px">
          <el-form-item label="标准规范名称" required>
            <el-input v-model="currentStandardRule.ruleName" placeholder="标准命名规则" readonly />
          </el-form-item>
          <el-form-item label="标准规范说明" required>
            <el-input
              v-model="currentStandardRule.ruleDescription"
              type="textarea"
              :rows="3"
              placeholder="数据标准名称的命名方式"
              readonly
            />
          </el-form-item>
          <el-form-item label="标准规范举例">
            <el-input
              v-model="currentStandardRule.ruleExample"
              type="textarea"
              :rows="2"
              placeholder="参保人_基本信息_性别"
              readonly
            />
          </el-form-item>
        </el-form>
      </div>
    </DialogComp>

    <!-- 创建标准规范弹窗 -->
    <DialogComp
      v-model:visible="showRuleAddDialog"
      title="创建数据标准规范"
      width="600px"
      confirm-button-text="确定"
      cancel-button-text="取消"
      @click-confirm="handleConfirmRuleAdd"
      @click-cancel="handleCancelRuleAdd"
      @closed="handleCancelRuleAdd"
    >
      <el-form :model="ruleAddForm" label-width="140px">
        <el-form-item label="标准规范名称" required>
          <el-input v-model="ruleAddForm.ruleName" placeholder="标准命名规则" />
        </el-form-item>
        <el-form-item label="标准规范说明" required>
          <el-input
            v-model="ruleAddForm.ruleDescription"
            type="textarea"
            :rows="3"
            placeholder="数据标准名称的命名方式"
          />
        </el-form-item>
        <el-form-item label="标准规范举例">
          <el-input
            v-model="ruleAddForm.ruleExample"
            type="textarea"
            :rows="2"
            placeholder="参保人_基本信息_性别"
          />
        </el-form-item>
      </el-form>
    </DialogComp>

    <!-- 编辑标准规范弹窗 -->
    <DialogComp
      v-model:visible="showRuleEditDialog"
      title="编辑数据标准规范"
      width="600px"
      confirm-button-text="确定"
      cancel-button-text="取消"
      @click-confirm="handleConfirmRuleEdit"
      @click-cancel="handleCancelRuleEdit"
      @closed="handleCancelRuleEdit"
    >
      <el-form :model="ruleEditForm" label-width="140px">
        <el-form-item label="标准规范名称" required>
          <el-input v-model="ruleEditForm.ruleName" placeholder="标准命名规则" />
        </el-form-item>
        <el-form-item label="标准规范说明" required>
          <el-input
            v-model="ruleEditForm.ruleDescription"
            type="textarea"
            :rows="3"
            placeholder="数据标准名称的命名方式"
          />
        </el-form-item>
        <el-form-item label="标准规范举例">
          <el-input
            v-model="ruleEditForm.ruleExample"
            type="textarea"
            :rows="2"
            placeholder="参保人_基本信息_性别"
          />
        </el-form-item>
      </el-form>
    </DialogComp>

    <!-- 访问记录弹窗 -->
    <DialogComp
      v-model:visible="showAccessRecordDialog"
      title="数据标准访问记录"
      width="1200px"
      :visible-confirm-button="false"
      cancel-button-text="关闭"
      @click-cancel="showAccessRecordDialog = false"
      @closed="showAccessRecordDialog = false"
    >
      <div class="access-record-content">
        <!-- 访问记录表格 -->
        <div class="access-table-container">
          <TableV2
            :columns="accessColumns"
            :defaultTableData="getCurrentPageAccessRecords()"
            :enable-toolbar="false"
            :enable-own-button="false"
            :enable-selection="false"
            :enable-index="true"
            :height="450"
            :loading="false"
            stripe
            border
          />
        </div>

        <!-- 分页 -->
        <div class="access-pagination">
          <Pagination
            v-model:current-page="accessRecordPagination.currentPage"
            v-model:page-size="accessRecordPagination.pageSize"
            :total="accessRecordPagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleAccessSizeChange"
            @current-change="handleAccessPageChange"
          />
        </div>
      </div>
    </DialogComp>

    <!-- 修改历史记录弹窗 -->
    <DialogComp
      v-model:visible="showModificationHistoryDialog"
      title="修改历史记录"
      width="1200px"
      :visible-confirm-button="false"
      :visible-cancel-button="false"
      @click-cancel="showModificationHistoryDialog = false"
      @closed="showModificationHistoryDialog = false"
    >
      <div class="modification-history-content">
        <!-- 搜索区域 -->
        <div class="history-search-area">
          <Form
            :props="modificationHistorySearchFormProp"
            v-model="modificationHistorySearchForm"
            :column-count="5"
            :label-width="80"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="handleModificationHistorySearch"
          />
        </div>

        <!-- 修改历史记录表格 -->
        <div class="history-table-area">
          <TableV2
            :columns="modificationHistoryColumns"
            :defaultTableData="getCurrentPageModificationHistoryData()"
            :enable-toolbar="false"
            :enable-own-button="false"
            :enable-selection="false"
            :enable-index="false"
            :height="400"
            :loading="false"
          >
            <!-- 操作类型列自定义显示 -->
            <template #operationName="{ row }">
              <el-tag
                :type="getOperationTagType(row.operation)"
                size="small"
              >
                {{ row.operationName }}
              </el-tag>
            </template>
          </TableV2>
        </div>

        <!-- 分页 -->
        <div class="history-pagination-area">
          <Pagination
            :current-page="modificationHistoryPagination.currentPage"
            :page-size="modificationHistoryPagination.pageSize"
            :total="modificationHistoryPagination.total"
            @current-change="handleModificationHistoryPageChange"
            @size-change="handleModificationHistorySizeChange"
          />
        </div>
      </div>
    </DialogComp>

    <!-- 颜色选择器弹窗 -->
    <DialogComp
      v-model:visible="showColorPickerDialog"
      title="选择标签颜色"
      width="400px"
      confirm-text="确定"
      cancel-text="取消"
      @click-confirm="confirmColorChange"
      @click-cancel="cancelColorChange"
      @closed="cancelColorChange"
    >
      <div class="color-picker-content">
        <div class="current-color-preview">
          <span class="preview-label">当前颜色：</span>
          <div 
            class="color-preview-box" 
            :style="{ backgroundColor: selectedColor }"
          ></div>
        </div>
        
        <div class="color-options">
          <div class="color-grid">
            <div 
              v-for="color in predefinedColors" 
              :key="color"
              class="color-option"
              :class="{ 'selected': selectedColor === color }"
              :style="{ backgroundColor: color }"
              @click="selectColor(color)"
              :title="color"
            ></div>
          </div>
        </div>
      </div>
    </DialogComp>
  </div>
</template>

<style scoped>
.data-standard-management {
  height: 100%;
}

/* 导入弹窗样式 */
.import-content {
  padding: 10px 0;
}

.import-tips {
  margin-bottom: 20px;
}

.tips-content p {
  margin: 5px 0;
  font-size: 14px;
}

.template-download {
  text-align: center;
  margin: 20px 0;
}

.upload-demo {
  margin-top: 20px;
}

.file-info {
  text-align: center;
  margin-top: 15px;
}

/* 权限复选框样式 */
.permission-checkboxes {
  display: flex;
  gap: 20px;
}

.search {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
}

.standard-name-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.star-icon {
  cursor: pointer;
  margin-left: 8px;
  color: #ccc;
  transition: color 0.3s;
}

.star-icon:hover {
  color: #E6A23C;
}

.star-icon.starred {
  color: #ffd700;
}

/* 访问记录弹窗样式 */
.access-record-content {
  padding: 10px 0;
}

.access-table-container {
  margin-bottom: 20px;
}

.access-pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 修改历史记录弹窗样式 */
.modification-history-content {
  padding: 10px 0;
}

.history-search-area {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

.history-table-area {
  margin-bottom: 20px;
}

.history-pagination-area {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 操作类型标签样式 */
.history-table-area .el-tag {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modification-history-content {
    padding: 5px 0;
  }
  
  .history-search-area {
    padding: 12px;
    margin-bottom: 15px;
  }
  
  .history-table-area {
    margin-bottom: 15px;
  }
  
  .history-pagination-area {
    margin-top: 15px;
  }
}

/* 权限配置样式 */
.permissions-display {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.permission-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.permission-checkboxes .el-checkbox {
  margin-right: 0;
}

.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

/* 进度条样式 */
.progress-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-weight: 500;
  color: #409eff;
}

/* 合规率样式 */
.compliance-rate {
  display: flex;
  align-items: center;
}

.rate-value {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.import-content {
  padding: 20px 0;
}

.upload-demo {
  width: 100%;
}

/* 异常数据弹窗样式 */
.abnormal-content {
  padding: 0;
}

.abnormal-header {
  margin-bottom: 20px;
}

.abnormal-table {
  margin-bottom: 20px;
}

.abnormal-pagination {
  display: flex;
  justify-content: center;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

.abnormal-pagination .el-pagination {
  justify-content: center;
}

.abnormal-pagination {
  margin-top: 20px;
  text-align: center;
}

/* 评估弹窗样式 */
.evaluate-content {
  padding: 10px 0;
}

.evaluate-item {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.evaluate-item:hover {
  background: #f0f8ff;
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.evaluate-item:last-child {
  margin-bottom: 0;
}

.evaluate-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.evaluate-icon {
  width: 32px;
  height: 32px;
  background: #409EFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
  font-size: 16px;
}

.evaluate-info {
  flex: 1;
}

.evaluate-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.evaluate-description {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.evaluate-value {
  display: flex;
  align-items: baseline;
  margin-left: 12px;
}

.value-number {
  font-size: 24px;
  font-weight: 700;
  color: #409EFF;
  margin-right: 4px;
}

.value-unit {
  font-size: 14px;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.evaluate-inputs {
  margin: 16px 0;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
}

.input-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  min-width: 60px;
}

.unit-label {
  font-size: 13px;
  color: #909399;
  margin-left: 4px;
}

.target-display {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
}

.target-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  min-width: 60px;
}

.target-value {
  font-size: 14px;
  color: #409EFF;
  font-weight: 600;
  background: #f0f8ff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e1f0ff;
}

.evaluate-progress {
  margin-top: 16px;
}

.progress-label {
  font-size: 13px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-tip {
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

.progress-bar {
  width: 100%;
}

.progress-bar :deep(.el-progress-bar__outer) {
  background-color: #e4e7ed;
}

.progress-bar :deep(.el-progress-bar__inner) {
  background: linear-gradient(90deg, #409EFF 0%, #66b1ff 100%);
}

.progress-bar :deep(.el-progress__text) {
  color: #409EFF;
  font-weight: 600;
}

/* 标准规范相关样式 */
.standard-rule-content {
  padding: 0;
}

.rule-search-area {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 16px;
}

.rule-action-buttons {
  margin-top: 12px;
  text-align: right;
}

.rule-table-area {
  margin-bottom: 16px;
}

.rule-pagination-area {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

.rule-detail-content {
  padding: 20px 0;
}

.rule-detail-content .el-form-item {
  margin-bottom: 20px;
}

.rule-detail-content .el-form-item__label {
  font-weight: 600;
  color: #303133;
}

.rule-detail-content span {
  color: #606266;
  line-height: 1.6;
  word-break: break-all;
}

/* 标准规范表格样式优化 */
.rule-table-area .el-table {
  font-size: 14px;
}

.rule-table-area .el-table th {
  background-color: #fafafa;
  font-weight: 600;
}

.rule-table-area .el-table td {
  padding: 12px 0;
}

.rule-table-area .el-button {
  margin: 0 2px;
}

/* 标准规范搜索区域样式 */
.rule-search-area .el-form-item {
  margin-bottom: 0;
  margin-right: 16px;
}

.rule-search-area .el-button {
  margin-left: 8px;
}

/* 标准规范表单样式 */
.rule-detail-content .el-form,
.showRuleAddDialog .el-form,
.showRuleEditDialog .el-form {
  max-width: 100%;
}

.rule-detail-content .el-form-item__content,
.showRuleAddDialog .el-form-item__content,
.showRuleEditDialog .el-form-item__content {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rule-search-area .el-form {
    display: block;
  }
  
  .rule-search-area .el-form-item {
    display: block;
    margin-bottom: 12px;
    margin-right: 0;
  }
  
  .rule-search-area .el-input {
    width: 100% !important;
  }
  
  .rule-table-area .el-table {
    font-size: 12px;
  }
  
  .rule-table-area .el-button {
    padding: 4px 8px;
    font-size: 12px;
  }
}

/* =============== 标签颜色选择器样式 =============== */

/* 标签颜色显示样式 */
.tag-color-display {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
  display: inline-block;
}

.tag-color-display:hover {
  border-color: #409eff;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 颜色选择器弹窗样式 */
.color-picker-content {
  padding: 16px 0;
}

.current-color-preview {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.preview-label {
  font-size: 14px;
  color: #606266;
  margin-right: 12px;
  font-weight: 500;
}

.color-preview-box {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 2px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-options {
  margin-top: 16px;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
  padding: 8px;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.color-option:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.color-option.selected {
  border-color: #409eff;
  transform: scale(1.15);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.4);
}

.color-option.selected::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .color-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }
  
  .color-option {
    width: 32px;
    height: 32px;
  }
  
  .color-preview-box {
    width: 28px;
    height: 28px;
  }
}
</style>
