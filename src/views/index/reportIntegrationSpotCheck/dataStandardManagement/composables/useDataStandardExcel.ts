import { ref } from 'vue'
import ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import { ElMessage } from 'element-plus'
import util from '@/plugin/util'

// 数据标准类型定义
interface DataStandardData {
  id: string
  index: number
  standardName: string
  category: string
  dataTotal: number
  dataVersion: string
  responsible: string
  permissions: {
    access: boolean
    edit: boolean
    delete: boolean
  }
  content: string
  remark: string
  passedDataRatio: number
  failedDataRatio: number
  complianceRate: number
  isFavorite: boolean
  status: 'normal' | 'abnormal'
  tagColor: string // 标签颜色
  createTime: string
  updateTime: string
}

export const useDataStandardExcel = () => {
  const isImporting = ref(false)
  const isExporting = ref(false)

  // Excel 列定义 - 与新增表单字段保持一致
  const excelColumns = [
    { header: '数据标准名称', key: 'standardName', width: 25 },
    { header: '分类', key: 'category', width: 15 },
    { header: '涉及数据总数', key: 'dataTotal', width: 15 },
    { header: '负责人', key: 'responsible', width: 15 },
    { header: '状态', key: 'status', width: 12 },
    { header: '访问权限', key: 'accessPermission', width: 12 },
    { header: '编辑权限', key: 'editPermission', width: 12 },
    { header: '删除权限', key: 'deletePermission', width: 12 },
    { header: '标准内容', key: 'content', width: 30 },
    { header: '标准备注', key: 'remark', width: 30 }
  ]

  // 分类选项
  const categoryOptions = ['INT', 'VARCHAR', 'DATE', 'DECIMAL', 'TEXT']
  
  // 负责人选项
  const responsibleOptions = ['张三', '李四', '王五', '赵六', '钱七']
  
  // 状态选项
  const statusOptions = [
    { label: '正常', value: 'normal' },
    { label: '异常', value: 'abnormal' }
  ]

  // 导出 Excel
  const exportToExcel = async (data: DataStandardData[], filename: string = '数据标准管理数据') => {
    try {
      isExporting.value = true

      const workbook = new ExcelJS.Workbook()
      workbook.creator = 'Data Standard Management System'
      workbook.lastModifiedBy = 'System'
      workbook.created = new Date()
      workbook.modified = new Date()

      // 创建工作表
      const worksheet = workbook.addWorksheet('数据标准管理')

      // 设置列
      worksheet.columns = excelColumns

      // 设置表头样式
      const headerRow = worksheet.getRow(1)
      headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4472C4' }
      }
      headerRow.alignment = { horizontal: 'center', vertical: 'middle' }
      headerRow.height = 25

      // 添加数据
      data.forEach((item, index) => {
        const row = worksheet.addRow({
          standardName: item.standardName,
          category: item.category,
          dataTotal: item.dataTotal || 0,
          responsible: item.responsible,
          status: getStatusLabel(item.status),
          accessPermission: item.permissions?.access ? '是' : '否',
          editPermission: item.permissions?.edit ? '是' : '否',
          deletePermission: item.permissions?.delete ? '是' : '否',
          content: item.content || '',
          remark: item.remark || ''
        })

        // 设置数据行样式
        row.alignment = { horizontal: 'left', vertical: 'middle' }
        row.height = 20

        // 交替行颜色
        if (index % 2 === 1) {
          row.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F2F2F2' }
          }
        }
      })

      // 设置边框
      worksheet.eachRow((row, rowNumber) => {
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }
        })
      })

      // 自动调整列宽
      worksheet.columns.forEach((column) => {
        if (column.width && column.width < 10) {
          column.width = 10
        }
      })

      // 生成文件
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })

      // 下载文件
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
      saveAs(blob, `${filename}_${timestamp}.xlsx`)

      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请重试')
    } finally {
      isExporting.value = false
    }
  }

  // 导入 Excel
  const importFromExcel = async (file: File): Promise<DataStandardData[]> => {
    return new Promise(async (resolve, reject) => {
      try {
        isImporting.value = true

        const workbook = new ExcelJS.Workbook()
        const arrayBuffer = await file.arrayBuffer()
        await workbook.xlsx.load(arrayBuffer)

        const worksheet = workbook.getWorksheet(1)
        if (!worksheet) {
          throw new Error('Excel 文件中没有找到工作表')
        }

        const importedData: DataStandardData[] = []
        const errors: string[] = []

        // 从第二行开始读取数据（第一行是表头）
        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber === 1) return // 跳过表头

          try {
            // 读取各列数据
            const standardName = String(row.getCell(1).value || '').trim()
            const category = String(row.getCell(2).value || '').trim()
            const dataTotal = row.getCell(3).value ? Number(row.getCell(3).value) : 0
            const responsible = String(row.getCell(4).value || '').trim()
            const statusLabel = String(row.getCell(5).value || '').trim()
            const accessPermissionText = String(row.getCell(6).value || '').trim()
            const editPermissionText = String(row.getCell(7).value || '').trim()
            const deletePermissionText = String(row.getCell(8).value || '').trim()
            const content = String(row.getCell(9).value || '').trim()
            const remark = String(row.getCell(10).value || '').trim()

            // 验证必填项
            if (!standardName) {
              errors.push(`第${rowNumber}行：数据标准名称不能为空`)
              return
            }
            if (!category) {
              errors.push(`第${rowNumber}行：分类不能为空`)
              return
            }

            // 验证分类是否有效
            if (!categoryOptions.includes(category)) {
              errors.push(`第${rowNumber}行：分类"${category}"无效，请选择：${categoryOptions.join('、')}`)
              return
            }

            // 验证负责人是否有效
            if (responsible && !responsibleOptions.includes(responsible)) {
              errors.push(`第${rowNumber}行：负责人"${responsible}"无效，请选择：${responsibleOptions.join('、')}`)
              return
            }

            // 转换状态
            const status = getStatusValue(statusLabel) as 'normal' | 'abnormal' | null
            if (statusLabel && !status) {
              errors.push(`第${rowNumber}行：状态"${statusLabel}"无效，请选择：正常、异常`)
              return
            }

            // 转换权限
            const accessPermission = ['是', 'true', '1', 'yes'].includes(accessPermissionText.toLowerCase())
            const editPermission = ['是', 'true', '1', 'yes'].includes(editPermissionText.toLowerCase())
            const deletePermission = ['是', 'true', '1', 'yes'].includes(deletePermissionText.toLowerCase())

            // 验证数据总数
            if (dataTotal < 0) {
              errors.push(`第${rowNumber}行：涉及数据总数不能为负数`)
              return
            }

            const dataStandard: DataStandardData = {
              id: util._guid(),
              index: importedData.length + 1,
              standardName,
              category,
              dataTotal,
              dataVersion: '1.0.0', // 默认版本
              responsible: responsible || '',
              permissions: {
                access: accessPermission,
                edit: editPermission,
                delete: deletePermission
              },
              content: content || '',
              remark: remark || '',
              passedDataRatio: Math.random() * 100, // 模拟数据
              failedDataRatio: Math.random() * 100, // 模拟数据
              complianceRate: Math.random() * 100, // 模拟数据
              isFavorite: false,
              status: status || 'normal',
              tagColor: '#409EFF', // 默认标签颜色
              createTime: new Date().toISOString(),
              updateTime: new Date().toISOString()
            }

            importedData.push(dataStandard)
          } catch (error) {
            errors.push(`第${rowNumber}行：数据格式错误`)
          }
        })

        if (errors.length > 0) {
          const errorMessage = errors.slice(0, 5).join('\n') + (errors.length > 5 ? '\n...' : '')
          throw new Error(`导入失败，发现以下错误：\n${errorMessage}`)
        }

        if (importedData.length === 0) {
          throw new Error('Excel 文件中没有有效的数据')
        }

        ElMessage.success(`成功导入 ${importedData.length} 条数据`)
        resolve(importedData)
      } catch (error) {
        console.error('导入失败:', error)
        const message = error instanceof Error ? error.message : '导入失败，请检查文件格式'
        ElMessage.error(message)
        reject(error)
      } finally {
        isImporting.value = false
      }
    })
  }

  // 生成导入模板
  const generateTemplate = async () => {
    try {
      const workbook = new ExcelJS.Workbook()
      workbook.creator = 'Data Standard Management System'

      const worksheet = workbook.addWorksheet('数据标准管理模板')
      worksheet.columns = excelColumns

      // 设置表头样式
      const headerRow = worksheet.getRow(1)
      headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4472C4' }
      }
      headerRow.alignment = { horizontal: 'center', vertical: 'middle' }
      headerRow.height = 25

      // 添加示例数据
      const exampleData = [
        {
          standardName: '用户ID标准',
          category: 'INT',
          dataTotal: 1000,
          responsible: '张三',
          status: '正常',
          accessPermission: '是',
          editPermission: '是',
          deletePermission: '否',
          content: '用户唯一标识符，整数类型，自增长',
          remark: '主键字段，不可为空'
        },
        {
          standardName: '用户名标准',
          category: 'VARCHAR',
          dataTotal: 800,
          responsible: '李四',
          status: '正常',
          accessPermission: '是',
          editPermission: '否',
          deletePermission: '否',
          content: '用户登录名，字符串类型，长度限制50字符',
          remark: '唯一约束，不可重复'
        }
      ]

      exampleData.forEach((item, index) => {
        const row = worksheet.addRow(item)
        row.alignment = { horizontal: 'left', vertical: 'middle' }
        row.height = 20

        // 示例数据使用浅蓝色背景
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'E6F3FF' }
        }
      })

      // 设置边框
      worksheet.eachRow((row) => {
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }
        })
      })

      // 添加说明工作表
      const instructionSheet = workbook.addWorksheet('填写说明')
      instructionSheet.columns = [
        { header: '字段名', key: 'field', width: 20 },
        { header: '是否必填', key: 'required', width: 12 },
        { header: '数据类型', key: 'type', width: 15 },
        { header: '可选值', key: 'options', width: 40 },
        { header: '说明', key: 'description', width: 50 }
      ]

      // 设置说明表头样式
      const instructionHeaderRow = instructionSheet.getRow(1)
      instructionHeaderRow.font = { bold: true, color: { argb: 'FFFFFF' } }
      instructionHeaderRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4472C4' }
      }
      instructionHeaderRow.alignment = { horizontal: 'center', vertical: 'middle' }
      instructionHeaderRow.height = 25

      // 添加字段说明
      const fieldInstructions = [
        {
          field: '数据标准名称',
          required: '是',
          type: '文本',
          options: '',
          description: '数据标准的名称，必须填写'
        },
        {
          field: '分类',
          required: '是',
          type: '选择',
          options: 'INT、VARCHAR、DATE、DECIMAL、TEXT',
          description: '数据标准的分类，必须从可选值中选择'
        },
        {
          field: '涉及数据总数',
          required: '否',
          type: '数字',
          options: '≥0的整数',
          description: '该标准涉及的数据总数，默认为0'
        },
        {
          field: '负责人',
          required: '否',
          type: '选择',
          options: '张三、李四、王五、赵六、钱七',
          description: '数据标准的负责人'
        },
        {
          field: '状态',
          required: '否',
          type: '选择',
          options: '正常、异常',
          description: '数据标准的状态，默认为正常'
        },
        {
          field: '访问权限',
          required: '否',
          type: '布尔',
          options: '是、否',
          description: '是否具有访问权限，默认为否'
        },
        {
          field: '编辑权限',
          required: '否',
          type: '布尔',
          options: '是、否',
          description: '是否具有编辑权限，默认为否'
        },
        {
          field: '删除权限',
          required: '否',
          type: '布尔',
          options: '是、否',
          description: '是否具有删除权限，默认为否'
        },
        {
          field: '标准内容',
          required: '否',
          type: '文本',
          options: '',
          description: '数据标准的详细内容描述'
        },
        {
          field: '标准备注',
          required: '否',
          type: '文本',
          options: '',
          description: '数据标准的备注信息'
        }
      ]

      fieldInstructions.forEach((item, index) => {
        const row = instructionSheet.addRow(item)
        row.alignment = { horizontal: 'left', vertical: 'middle' }
        row.height = 20

        // 交替行颜色
        if (index % 2 === 1) {
          row.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F2F2F2' }
          }
        }
      })

      // 设置说明表边框
      instructionSheet.eachRow((row) => {
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }
        })
      })

      // 生成文件
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })

      saveAs(blob, '数据标准管理导入模板.xlsx')
      ElMessage.success('模板下载成功')
    } catch (error) {
      console.error('生成模板失败:', error)
      ElMessage.error('生成模板失败，请重试')
    }
  }

  // 辅助函数：获取状态标签
  const getStatusLabel = (value: string) => {
    const status = statusOptions.find(s => s.value === value)
    return status ? status.label : value
  }

  // 辅助函数：获取状态值
  const getStatusValue = (label: string) => {
    const status = statusOptions.find(s => s.label === label)
    return status ? status.value : null
  }

  return {
    isImporting,
    isExporting,
    exportToExcel,
    importFromExcel,
    generateTemplate
  }
}
