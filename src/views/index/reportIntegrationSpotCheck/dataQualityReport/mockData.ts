import type { 
  QualityReportDataItem, 
  QualityRuleConfig, 
  QualityScoreInfo, 
  QualityIndicatorOption,
  ComparisonData,
  QualityIndicatorType 
} from './types'

/**
 * 数据质量指标选项
 */
export const qualityIndicatorOptions: QualityIndicatorOption[] = [
  {
    value: 'fieldDuplicateRate',
    label: '字段重复率',
    description: '检测数据字段中重复值的比例'
  },
  {
    value: 'standardValueMatchRate',
    label: '标准值匹配率',
    description: '数据值与标准规范匹配的比例'
  },
  {
    value: 'primaryKeyDuplicateRate',
    label: '主键重复率',
    description: '主键字段重复值的比例'
  },
  {
    value: 'crossSystemConsistencyRate',
    label: '跨系统一致率',
    description: '不同系统间数据一致性的比例'
  },
  {
    value: 'fieldFormatComplianceRate',
    label: '字段格式合规率',
    description: '字段格式符合规范要求的比例'
  }
]

/**
 * 生成模拟的数据质量报告数据
 */
export function generateMockQualityReportData(): QualityReportDataItem[] {
  const dataSources = ['用户管理系统', '财务管理系统', '人事管理系统', '库存管理系统', '订单管理系统']
  const tableNames = ['user_info', 'financial_records', 'employee_data', 'inventory_items', 'order_details']
  const fieldNames = ['user_id', 'name', 'email', 'phone', 'address', 'amount', 'date', 'status']
  const indicatorTypes: QualityIndicatorType[] = [
    'fieldDuplicateRate',
    'standardValueMatchRate', 
    'primaryKeyDuplicateRate',
    'crossSystemConsistencyRate',
    'fieldFormatComplianceRate'
  ]

  const data: QualityReportDataItem[] = []

  for (let i = 1; i <= 25; i++) {
    const currentValue = Math.round((Math.random() * 30 + 70) * 100) / 100 // 70-100之间的值
    const previousValue = Math.round((Math.random() * 30 + 65) * 100) / 100 // 65-95之间的值
    const changeRate = Math.round(((currentValue - previousValue) / previousValue) * 10000) / 100
    
    let status: 'normal' | 'warning' | 'error' = 'normal'
    if (currentValue < 80) status = 'warning'
    if (currentValue < 70) status = 'error'

    let changeType: 'increase' | 'decrease' | 'stable' = 'stable'
    if (Math.abs(changeRate) > 1) {
      changeType = changeRate > 0 ? 'increase' : 'decrease'
    }

    data.push({
      id: `quality_${i}`,
      sequence: i,
      dataSource: dataSources[Math.floor(Math.random() * dataSources.length)],
      tableName: tableNames[Math.floor(Math.random() * tableNames.length)],
      fieldName: fieldNames[Math.floor(Math.random() * fieldNames.length)],
      indicatorType: indicatorTypes[Math.floor(Math.random() * indicatorTypes.length)],
      currentValue,
      previousValue,
      changeRate,
      changeType,
      status,
      lastUpdateTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      remark: Math.random() > 0.7 ? '需要关注数据质量变化趋势' : undefined
    })
  }

  return data
}

/**
 * 生成模拟的数据质量规则配置
 */
export function generateMockQualityRuleConfigs(): QualityRuleConfig[] {
  return [
    {
      id: 'rule_1',
      name: '字段重复率检查规则',
      indicatorType: 'fieldDuplicateRate',
      calculationMethod: '重复记录数 / 总记录数 * 100%',
      thresholdRange: { min: 0, max: 100 },
      warningThreshold: 10,
      errorThreshold: 20,
      isEnabled: true,
      createTime: '2024-01-15T10:00:00Z',
      updateTime: '2024-01-20T15:30:00Z'
    },
    {
      id: 'rule_2',
      name: '标准值匹配率检查规则',
      indicatorType: 'standardValueMatchRate',
      calculationMethod: '匹配标准值记录数 / 总记录数 * 100%',
      thresholdRange: { min: 0, max: 100 },
      warningThreshold: 85,
      errorThreshold: 75,
      isEnabled: true,
      createTime: '2024-01-15T10:00:00Z',
      updateTime: '2024-01-20T15:30:00Z'
    },
    {
      id: 'rule_3',
      name: '主键重复率检查规则',
      indicatorType: 'primaryKeyDuplicateRate',
      calculationMethod: '重复主键记录数 / 总记录数 * 100%',
      thresholdRange: { min: 0, max: 100 },
      warningThreshold: 1,
      errorThreshold: 5,
      isEnabled: true,
      createTime: '2024-01-15T10:00:00Z',
      updateTime: '2024-01-20T15:30:00Z'
    },
    {
      id: 'rule_4',
      name: '跨系统一致率检查规则',
      indicatorType: 'crossSystemConsistencyRate',
      calculationMethod: '一致记录数 / 总记录数 * 100%',
      thresholdRange: { min: 0, max: 100 },
      warningThreshold: 90,
      errorThreshold: 80,
      isEnabled: true,
      createTime: '2024-01-15T10:00:00Z',
      updateTime: '2024-01-20T15:30:00Z'
    },
    {
      id: 'rule_5',
      name: '字段格式合规率检查规则',
      indicatorType: 'fieldFormatComplianceRate',
      calculationMethod: '格式合规记录数 / 总记录数 * 100%',
      thresholdRange: { min: 0, max: 100 },
      warningThreshold: 95,
      errorThreshold: 85,
      isEnabled: true,
      createTime: '2024-01-15T10:00:00Z',
      updateTime: '2024-01-20T15:30:00Z'
    }
  ]
}

/**
 * 生成模拟的数据质量得分信息
 */
export function generateMockQualityScoreInfo(): QualityScoreInfo {
  return {
    totalScore: 78,
    fieldDuplicateRate: 4,
    standardValueMatchRate: 4,
    primaryKeyDuplicateRate: 4,
    crossSystemConsistencyRate: 4,
    fieldFormatComplianceRate: 4,
    lastUpdateTime: new Date().toISOString()
  }
}

/**
 * 生成模拟的同比环比数据
 */
export function generateMockComparisonData(indicatorType: QualityIndicatorType): ComparisonData {
  const currentValue = Math.round((Math.random() * 30 + 70) * 100) / 100
  const previousMonthValue = Math.round((Math.random() * 30 + 65) * 100) / 100
  const previousYearValue = Math.round((Math.random() * 30 + 60) * 100) / 100

  const monthOverMonthRate = Math.round(((currentValue - previousMonthValue) / previousMonthValue) * 10000) / 100
  const yearOverYearRate = Math.round(((currentValue - previousYearValue) / previousYearValue) * 10000) / 100

  return {
    indicatorType,
    currentPeriod: {
      value: currentValue,
      period: '2024年1月'
    },
    previousPeriod: {
      value: previousMonthValue,
      period: '2023年12月'
    },
    yearOverYear: {
      value: previousYearValue,
      rate: yearOverYearRate,
      trend: yearOverYearRate > 1 ? 'up' : yearOverYearRate < -1 ? 'down' : 'stable'
    },
    monthOverMonth: {
      value: previousMonthValue,
      rate: monthOverMonthRate,
      trend: monthOverMonthRate > 1 ? 'up' : monthOverMonthRate < -1 ? 'down' : 'stable'
    }
  }
}
