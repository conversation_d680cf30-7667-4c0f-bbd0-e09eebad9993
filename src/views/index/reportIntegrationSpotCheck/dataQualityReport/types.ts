/**
 * 数据质量指标报告相关类型定义
 */

/**
 * 数据质量指标类型
 */
export type QualityIndicatorType = 
  | 'fieldDuplicateRate'      // 字段重复率
  | 'standardValueMatchRate'  // 标准值匹配率
  | 'primaryKeyDuplicateRate' // 主键重复率
  | 'crossSystemConsistencyRate' // 跨系统一致率
  | 'fieldFormatComplianceRate'  // 字段格式合规率

/**
 * 数据质量指标选项
 */
export interface QualityIndicatorOption {
  value: QualityIndicatorType
  label: string
  description?: string
}

/**
 * 数据质量报告数据项
 */
export interface QualityReportDataItem {
  id: string
  sequence: number
  dataSource: string
  tableName: string
  fieldName: string
  indicatorType: QualityIndicatorType
  currentValue: number
  previousValue: number
  changeRate: number
  changeType: 'increase' | 'decrease' | 'stable'
  status: 'normal' | 'warning' | 'error'
  lastUpdateTime: string
  remark?: string
}

/**
 * 数据质量规则配置
 */
export interface QualityRuleConfig {
  id: string
  name: string
  indicatorType: QualityIndicatorType
  calculationMethod: string
  dataRange: {
    type: 'all' | 'custom'
    value?: string
  }
  weight: number // 权重百分比
  primaryKey?: string // 选择主键
  thresholdRange: {
    min: number
    max: number
  }
  warningThreshold: number
  errorThreshold: number
  isEnabled: boolean
  createTime: string
  updateTime: string
}

/**
 * 规则配置表单数据
 */
export interface RuleConfigForm {
  fieldDuplicateRate: {
    calculationMethod: string
    dataRange: { type: string; value: string }
    weight: number
  }
  primaryKeyDuplicateRate: {
    calculationMethod: string
    dataRange: { type: string; value: string }
    weight: number
    primaryKey: string
  }
  fieldFormatComplianceRate: {
    calculationMethod: string
    dataRange: { type: string; value: string }
    weight: number
    primaryKey: string
  }
  standardValueMatchRate: {
    calculationMethod: string
    dataRange: { type: string; value: string }
    weight: number
  }
  crossSystemConsistencyRate: {
    calculationMethod: string
    dataRange: { type: string; value: string }
    weight: number
  }
  alertEnabled: boolean
}

/**
 * 数据质量得分信息
 */
export interface QualityScoreInfo {
  totalScore: number
  fieldDuplicateRate: number
  standardValueMatchRate: number
  primaryKeyDuplicateRate: number
  crossSystemConsistencyRate: number
  fieldFormatComplianceRate: number
  lastUpdateTime: string
}

/**
 * 报告操作类型
 */
export type ReportActionType = 'export' | 'print' | 'share'

/**
 * 报告导出格式
 */
export type ExportFormat = 'excel' | 'pdf'

/**
 * 分页信息
 */
export interface PaginationInfo {
  page: number
  size: number
  total: number
}

/**
 * 查询参数
 */
export interface QueryParams {
  dataSource?: string
  tableName?: string
  indicatorType?: QualityIndicatorType
  status?: string
  startDate?: string
  endDate?: string
}

/**
 * 表格列配置
 */
export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
}

/**
 * 图表数据点
 */
export interface ChartDataPoint {
  name: string
  value: number
  color?: string
}

/**
 * 同比环比数据
 */
export interface ComparisonData {
  indicatorType: QualityIndicatorType
  currentPeriod: {
    value: number
    period: string
  }
  previousPeriod: {
    value: number
    period: string
  }
  yearOverYear: {
    value: number
    rate: number
    trend: 'up' | 'down' | 'stable'
  }
  monthOverMonth: {
    value: number
    rate: number
    trend: 'up' | 'down' | 'stable'
  }
}
