# 数据质量指标报告功能

## 功能概述

数据质量指标报告功能是一个基于 Vue 3 + TypeScript + Element Plus 的前端演示模块，提供了完整的数据质量监控和分析功能。

## 主要功能

### 1. 页面切换功能
- 支持在"数据质量报告"和"数据质量指标报告"之间切换
- 使用 el-switch 组件实现切换控制
- 切换状态持久化到本地存储

### 2. 数据质量得分展示
- 圆形进度图表显示总体数据质量得分
- 根据得分自动调整颜色（绿色-优秀，黄色-良好，橙色-一般，红色-较差）
- 支持动画效果和阴影样式
- 响应式设计，适配不同屏幕尺寸

### 3. 指标同比环比分析
- 下拉选择器包含5个数据质量指标：
  - 字段重复率
  - 标准值匹配率
  - 主键重复率
  - 跨系统一致率
  - 字段格式合规率
- 动态显示环比和同比变化率
- 趋势指示（上升/下降/稳定）

### 4. 数据展示表格
- 使用 TableV2 组件实现数据展示
- 支持分页功能（每页20条记录）
- 包含以下列：序号、数据源、表名、字段名、指标类型、当前值、上期值、变化率、状态、更新时间
- 状态标识（正常/警告/异常）
- 响应式列宽设计

### 5. 报告操作功能
- **报告导出**：支持多种格式导出
  - Excel表格 (.xlsx) - 完整的数据表格，包含样式和格式
  - PDF文档 (.pdf) - 通过浏览器打印功能生成
  - 文本文件 (.txt) - 纯文本格式报告
  - JSON数据 (.json) - 结构化数据导出
- **报告打印**：调用浏览器打印功能
- **报告分享**：多种分享方式
  - Web Share API（移动设备原生分享）
  - 剪贴板复制（自动复制分享内容）
  - 分享链接生成（包含免登录参数）

### 6. 数据质量规则设置
- **规则配置界面**：全新的折叠面板式规则配置界面
  - 字段重复率配置
  - 主键重复率配置
  - 字段格式合规率配置
  - 标准值匹配率配置
  - 跨系统一致率配置
- **配置项功能**：
  - 计算方式自定义输入
  - 数据范围选择（全部/自定义）
  - 权重百分比设置
  - 主键/标准表选择
- **智能验证**：权重总和必须为100%的自动验证
- **超出阈值告警**：开启/关闭告警功能
- **配置持久化**：本地存储保存配置信息

### 7. 数据持久化
- 使用 localStorage 进行数据持久化
- 支持分页状态、选中指标、视图模式的保存
- 模拟数据自动初始化
- 完整的 CRUD 操作支持

### 8. 错误处理和加载状态
- 完整的错误边界处理
- 加载状态指示器
- 错误重试机制
- 用户友好的错误提示

## 技术实现

### 文件结构
```
dataQualityReport/
├── index.vue          # 主页面组件
├── types.ts           # TypeScript 类型定义
├── mockData.ts        # 模拟数据生成
├── storage.ts         # 本地存储管理
├── __tests__/         # 测试文件
│   └── index.test.ts
└── README.md          # 说明文档
```

### 核心技术栈
- **Vue 3**: Composition API
- **TypeScript**: 类型安全
- **Element Plus**: UI 组件库
- **ECharts**: 图表库
- **SCSS**: 样式预处理器

### 组件依赖
- Block: 页面布局组件
- TableV2: 数据表格组件
- Dialog: 对话框组件
- Pagination: 分页组件
- Form: 表单组件

## 使用方法

### 1. 页面访问
访问路由：`/reportIntegrationSpotCheck/dataQualityReport`

### 2. 功能操作
1. **切换视图模式**：使用页面右上角的开关
2. **选择指标**：使用指标下拉选择器
3. **查看数据**：表格自动加载和分页
4. **设置规则**：点击"数据质量规则设置"按钮
5. **导出报告**：点击相应的操作按钮

### 3. 数据管理
- 数据自动保存到本地存储
- 页面刷新后数据保持不丢失
- 支持数据重置和清空

## 响应式设计

### 桌面端 (>1200px)
- 完整功能展示
- 多列布局
- 完整的操作按钮

### 平板端 (768px-1200px)
- 调整间距和布局
- 保持主要功能

### 移动端 (<768px)
- 垂直布局
- 单列显示
- 简化操作界面

### 小屏设备 (<480px)
- 全宽按钮
- 缩小图表尺寸
- 优化触摸操作

## 数据模拟

### 模拟数据特点
- 25条质量报告数据记录
- 5种数据质量指标类型
- 真实的数据源和表名
- 随机生成的质量得分和变化率
- 完整的时间戳信息

### 数据更新
- 支持实时数据更新
- 自动计算同比环比
- 状态自动判断

## 测试覆盖

### 单元测试
- 组件渲染测试
- 功能交互测试
- 数据持久化测试
- 错误处理测试

### 集成测试
- 页面完整流程测试
- 组件间交互测试
- 数据流测试

## 性能优化

### 加载优化
- 懒加载图表组件
- 分页数据加载
- 防抖处理

### 渲染优化
- 虚拟滚动（大数据量时）
- 组件缓存
- 计算属性优化

## 扩展性

### 功能扩展
- 支持更多指标类型
- 支持自定义规则
- 支持数据导入导出

### 技术扩展
- 支持实时数据接口
- 支持多语言
- 支持主题切换

## 注意事项

1. **浏览器兼容性**：支持现代浏览器（Chrome 80+, Firefox 75+, Safari 13+）
2. **本地存储限制**：注意 localStorage 容量限制
3. **性能考虑**：大数据量时建议使用分页
4. **移动端适配**：在移动设备上测试触摸操作

## 开发规范

### 代码规范
- 遵循 Vue 3 Composition API 最佳实践
- 使用 TypeScript 严格模式
- 遵循 ESLint 和 Prettier 规范

### 提交规范
- 使用语义化提交信息
- 包含完整的测试用例
- 更新相关文档

## 问题反馈

如有问题或建议，请通过以下方式反馈：
- 创建 Issue
- 提交 Pull Request
- 联系开发团队
