<script setup lang="ts" name="dataQualityReport">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  CaretRight,
  ArrowUp,
  ArrowDown,
  Minus,
  Warning
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import type {
  QualityReportDataItem,
  QualityRuleConfig,
  QualityScoreInfo,
  QualityIndicatorType,
  TableColumn,
  PaginationInfo,
  ComparisonData,
  RuleConfigForm
} from './types'
import { qualityIndicatorOptions, generateMockComparisonData } from './mockData'
import {
  qualityReportDataStorage,
  qualityRuleConfigStorage,
  qualityScoreInfoStorage,
  paginationStorage,
  selectedIndicatorStorage,
  viewModeStorage
} from './storage'

// 页面状态
const loading = ref(false)
const tableHeight = ref(0)
const chartLoading = ref(false)
const dataError = ref(false)
const errorMessage = ref('')

// 视图模式切换
const isIndicatorReport = ref(false)

// 数据质量得分信息
const qualityScoreInfo = ref<QualityScoreInfo>({
  totalScore: 78,
  fieldDuplicateRate: 4,
  standardValueMatchRate: 4,
  primaryKeyDuplicateRate: 4,
  crossSystemConsistencyRate: 4,
  fieldFormatComplianceRate: 4,
  lastUpdateTime: new Date().toISOString()
})

// 选中的指标类型
const selectedIndicator = ref<QualityIndicatorType>('fieldDuplicateRate')

// 同比环比数据
const comparisonData = ref<ComparisonData | null>(null)

// 表格数据
const tableData = ref<QualityReportDataItem[]>([])
const tableRef = ref()

// 分页信息
const pagination = reactive<PaginationInfo>({
  page: 1,
  size: 20,
  total: 0
})

// 表格列配置
const columns: TableColumn[] = [
  { prop: 'dataSource', label: '数据源' },
  { prop: 'tableName', label: '表名' },
  { prop: 'fieldName', label: '字段名' },
  { prop: 'indicatorType', label: '指标类型' },
  { prop: 'currentValue', label: '当前值(%)', align: 'center' },
  { prop: 'previousValue', label: '上期值(%)', align: 'center' },
  { prop: 'changeRate', label: '变化率(%)', align: 'center' },
  { prop: 'status', label: '状态', align: 'center' },
  { prop: 'lastUpdateTime', label: '更新时间', align: 'center' }
]

// 数据质量规则设置对话框
const showRuleDialog = ref(false)
const ruleConfigs = ref<QualityRuleConfig[]>([])
const ruleForm = ref<RuleConfigForm>({
  fieldDuplicateRate: {
    calculationMethod: '请输入计算方式',
    dataRange: { type: '全部', value: '' },
    weight: 15
  },
  primaryKeyDuplicateRate: {
    calculationMethod: '请输入计算方式',
    dataRange: { type: '全部', value: '' },
    weight: 15,
    primaryKey: '字段1'
  },
  fieldFormatComplianceRate: {
    calculationMethod: '请输入计算方式',
    dataRange: { type: '全部', value: '' },
    weight: 15,
    primaryKey: '指标1'
  },
  standardValueMatchRate: {
    calculationMethod: '请输入计算方式',
    dataRange: { type: '全部', value: '' },
    weight: 15
  },
  crossSystemConsistencyRate: {
    calculationMethod: '请输入计算方式',
    dataRange: { type: '全部', value: '' },
    weight: 15
  },
  alertEnabled: true
})

// 折叠面板激活的规则名称
const activeRuleNames = ref<string[]>(['fieldDuplicateRate'])

// 报告相关数据

const reportChartRef = ref()
const trendChartRef = ref()
const trendTimeRange = ref('30days')
const trendIndicator = ref('fieldDuplicate')
const trendDateRange = ref(['2024-04-01', '2024-04-28'])
const comparisonIndicator = ref('fieldDuplicate')

// 质量问题数据
const qualityIssues = ref([
  {
    type: '数据完整性',
    description: '部分字段存在空值',
    count: 15,
    severity: '中等',
    suggestion: '建立数据验证规则，确保必填字段完整性'
  },
  {
    type: '数据准确性',
    description: '日期格式不统一',
    count: 8,
    severity: '高',
    suggestion: '统一日期格式标准，建立格式验证机制'
  },
  {
    type: '数据一致性',
    description: '跨系统数据不一致',
    count: 12,
    severity: '高',
    suggestion: '建立主数据管理机制，定期同步数据'
  },
  {
    type: '数据及时性',
    description: '数据更新延迟',
    count: 5,
    severity: '低',
    suggestion: '优化数据同步频率，建立实时监控'
  }
])

// 改进建议数据
const qualitySuggestions = ref([
  {
    title: '建立数据质量监控体系',
    priority: '高优先级',
    content: '建议建立完善的数据质量监控体系，包括实时监控、定期检查和异常告警机制，确保数据质量问题能够及时发现和处理。'
  },
  {
    title: '完善数据标准规范',
    priority: '高优先级',
    content: '制定统一的数据标准和规范，包括数据格式、编码规则、业务规则等，确保数据的一致性和准确性。'
  },
  {
    title: '加强数据治理培训',
    priority: '中优先级',
    content: '定期开展数据治理相关培训，提高业务人员的数据质量意识，建立数据质量责任制。'
  },
  {
    title: '优化数据采集流程',
    priority: '中优先级',
    content: '优化数据采集和录入流程，减少人工操作环节，降低数据错误率。'
  }
])

// 圆形图表
const chartRef = ref<HTMLDivElement>()
const chartInstance = ref<echarts.ECharts>()

// 计算属性：获取当前指标的标签
const currentIndicatorLabel = computed(() => {
  const option = qualityIndicatorOptions.find(opt => opt.value === selectedIndicator.value)
  return option?.label || '未知指标'
})

// 计算属性：扩展质量评分信息
const extendedQualityScoreInfo = computed(() => {
  const base = qualityScoreInfo.value
  return {
    ...base,
    completeness: base.fieldFormatComplianceRate || 85,
    accuracy: base.standardValueMatchRate || 78,
    consistency: base.crossSystemConsistencyRate || 82,
    timeliness: base.primaryKeyDuplicateRate || 90
  }
})

// 获取评分类型
const getScoreType = (score: number) => {
  if (score >= 90) return 'success'
  if (score >= 80) return 'warning'
  if (score >= 70) return 'danger'
  return 'info'
}

// 获取评分等级
const getScoreLevel = (score: number) => {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '一般'
  if (score >= 60) return '较差'
  return '差'
}

// 获取严重程度类型
const getSeverityType = (severity: string) => {
  switch (severity) {
    case '高': return 'danger'
    case '中等': return 'warning'
    case '低': return 'success'
    default: return 'info'
  }
}

// 获取优先级类型
const getPriorityType = (priority: string) => {
  switch (priority) {
    case '高优先级': return 'danger'
    case '中优先级': return 'warning'
    case '低优先级': return 'success'
    default: return 'info'
  }
}

// 计算属性：格式化的表格数据
const formattedTableData = computed(() => {
  return tableData.value.map(item => ({
    ...item,
    indicatorType: qualityIndicatorOptions.find(opt => opt.value === item.indicatorType)?.label || item.indicatorType,
    currentValue: item.currentValue.toFixed(2),
    previousValue: item.previousValue.toFixed(2),
    changeRate: item.changeRate > 0 ? `+${item.changeRate.toFixed(2)}` : item.changeRate.toFixed(2),
    lastUpdateTime: new Date(item.lastUpdateTime).toLocaleString('zh-CN'),
    status: item.status === 'normal' ? '正常' : item.status === 'warning' ? '警告' : '异常'
  }))
})

// 初始化数据
const initializeData = async () => {
  try {
    dataError.value = false
    loading.value = true

    // 初始化存储数据
    qualityReportDataStorage.initializeData()
    qualityRuleConfigStorage.initializeData()
    qualityScoreInfoStorage.initializeData()

    // 加载数据
    await Promise.all([
      loadQualityScoreInfo(),
      loadRuleConfigs(),
      loadSelectedIndicator(),
      loadViewMode()
    ])

    await loadTableData()

  } catch (error) {
    console.error('初始化数据失败:', error)
    dataError.value = true
    errorMessage.value = '数据加载失败，请刷新页面重试'
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

// 加载数据质量得分信息
const loadQualityScoreInfo = () => {
  const scoreInfo = qualityScoreInfoStorage.get()
  if (scoreInfo) {
    qualityScoreInfo.value = scoreInfo
  }
}

// 加载规则配置
const loadRuleConfigs = () => {
  try {
    // 从本地存储加载配置
    const savedConfig = localStorage.getItem('qualityRuleConfig')
    if (savedConfig) {
      const config = JSON.parse(savedConfig)
      ruleForm.value = { ...ruleForm.value, ...config }
    }

    // 加载模拟规则配置数据
    ruleConfigs.value = qualityRuleConfigStorage.getAll()
  } catch (error) {
    console.error('加载规则配置失败:', error)
    // 使用默认配置
    ruleConfigs.value = qualityRuleConfigStorage.getAll()
  }
}

// 加载表格数据
const loadTableData = () => {
  return new Promise((resolve, reject) => {
    loading.value = true

    setTimeout(() => {
      try {
        const result = qualityReportDataStorage.getPaginated(
          pagination.page,
          pagination.size,
          { indicatorType: selectedIndicator.value }
        )

        tableData.value = result.data
        pagination.total = result.total
        resolve(result)
      } catch (error) {
        console.error('加载表格数据失败:', error)
        reject(error)
      } finally {
        loading.value = false
      }
    }, 800) // 模拟加载延迟，增加真实感
  })
}

// 加载选中的指标
const loadSelectedIndicator = () => {
  selectedIndicator.value = selectedIndicatorStorage.getSelectedIndicator()
  updateComparisonData()
}

// 加载视图模式
const loadViewMode = () => {
  const mode = viewModeStorage.getViewMode()
  isIndicatorReport.value = mode === 'indicator'
}

// 更新同比环比数据
const updateComparisonData = () => {
  comparisonData.value = generateMockComparisonData(selectedIndicator.value)
}

// 视图模式切换
const onViewModeChange = (value: string | number | boolean) => {
  const boolValue = Boolean(value)
  isIndicatorReport.value = boolValue
  const mode = boolValue ? 'indicator' : 'report'
  viewModeStorage.save(mode)

  if (boolValue) {
    // 切换到指标报告模式
    nextTick(() => {
      initChart()
    })
  } else {
    // 切换到数据质量报告模式
    nextTick(() => {
      initReportChart()
      initTrendChart()
    })
  }
}

// 指标选择变化
const onIndicatorChange = (value: QualityIndicatorType) => {
  selectedIndicator.value = value
  selectedIndicatorStorage.save(value)
  updateComparisonData()
  loadTableData()
}

// 分页变化
const onPaginationChange = (val: number, type: 'page' | 'size') => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1
  }
  paginationStorage.save(pagination)
  loadTableData()
}

// Block高度变化事件
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 200 // 预留空间给其他组件
}

// 数据质量规则设置
const onClickRuleSettings = () => {
  loadRuleConfigs()
  showRuleDialog.value = true
}

// 保存规则配置
const onSaveRuleConfig = () => {
  try {
    // 验证权重总和是否为100%
    const totalWeight =
      ruleForm.value.fieldDuplicateRate.weight +
      ruleForm.value.primaryKeyDuplicateRate.weight +
      ruleForm.value.fieldFormatComplianceRate.weight +
      ruleForm.value.standardValueMatchRate.weight +
      ruleForm.value.crossSystemConsistencyRate.weight

    if (totalWeight !== 100) {
      ElMessage.warning(`权重总和应为100%，当前为${totalWeight}%`)
      return
    }

    // 保存配置到本地存储
    const configData = {
      ...ruleForm.value,
      updateTime: new Date().toISOString()
    }

    localStorage.setItem('qualityRuleConfig', JSON.stringify(configData))

    ElMessage.success('规则配置保存成功')
    showRuleDialog.value = false

    // 重新加载数据以应用新配置
    loadQualityScoreInfo()
  } catch (error) {
    console.error('保存规则配置失败:', error)
    ElMessage.error('保存规则配置失败，请重试')
  }
}

// 获取指标标签
const getIndicatorLabel = (indicatorType: string) => {
  const indicatorMap: Record<string, string> = {
    'fieldDuplicateRate': '字段重复率',
    'standardValueMatchRate': '标准值匹配率',
    'primaryKeyDuplicateRate': '主键重复率',
    'crossSystemConsistencyRate': '跨系统一致率',
    'fieldFormatComplianceRate': '字段格式合规率'
  }
  return indicatorMap[indicatorType] || indicatorType
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'normal': '正常',
    'warning': '警告',
    'error': '异常'
  }
  return statusMap[status] || status
}

// 报告导出
const onExportReport = async () => {
  try {
    loading.value = true

    // 显示导出选项对话框
    const exportFormat = await ElMessageBox.confirm(
      '请选择导出格式：\n\n1. Excel 表格 (.xlsx)\n2. PDF 文档 (.pdf)\n3. 文本文件 (.txt)\n4. JSON 数据 (.json)',
      '报告导出',
      {
        confirmButtonText: 'Excel导出',
        cancelButtonText: '取消',
        type: 'info'
      }
    ).then(() => 'excel').catch(() => {
      // 如果用户取消，默认导出Excel
      return 'excel'
    })

    ElMessage.info(`正在导出${exportFormat.toUpperCase()}格式报告...`)

    // 根据选择的格式执行不同的导出逻辑
    switch (exportFormat) {
      case 'excel':
        await exportToExcel()
        break
      case 'pdf':
        await exportToPDF()
        break
      case 'txt':
        await exportToText()
        break
      case 'json':
        await exportToJSON()
        break
      default:
        throw new Error('不支持的导出格式')
    }

  } catch (error: any) {
    if (error === 'cancel') {
      // 用户取消导出
      return
    }
    console.error('导出报告失败:', error)
    ElMessage.error('导出报告失败，请重试')
  } finally {
    loading.value = false
  }
}

// 报告打印
const onPrintReport = () => {
  window.print()
}

// Excel导出功能
const exportToExcel = async () => {
  try {
    // 动态导入ExcelJS
    const ExcelJS = await import('exceljs')

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = 'Data Quality Report System'
    workbook.lastModifiedBy = 'System'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('数据质量指标报告')

    // 设置列宽
    worksheet.columns = [
      { header: '数据源', key: 'dataSource' },
      { header: '表名', key: 'tableName' },
      { header: '字段名', key: 'fieldName' },
      { header: '指标类型', key: 'indicatorType' },
      { header: '当前值', key: 'currentValue' },
      { header: '上期值', key: 'previousValue' },
      { header: '变化率', key: 'changeRate' },
      { header: '状态', key: 'status' },
      { header: '更新时间', key: 'updateTime'  }
    ]

    // 添加标题行
    const titleRow = worksheet.addRow(['数据质量指标报告'])
    titleRow.font = { size: 16, bold: true }
    titleRow.alignment = { horizontal: 'center' }
    worksheet.mergeCells('A1:J1')

    // 添加报告信息
    const infoRow = worksheet.addRow([
      `报告生成时间：${new Date().toLocaleString('zh-CN')}`,
      '',
      `当前指标：${currentIndicatorLabel.value}`,
      '',
      `总体得分：${qualityScoreInfo.value.totalScore}分`
    ])
    infoRow.font = { size: 12 }
    worksheet.mergeCells('A2:B2')
    worksheet.mergeCells('C2:D2')
    worksheet.mergeCells('E2:F2')

    // 添加空行
    worksheet.addRow([])

    // 添加表头
    const headerRow = worksheet.addRow(['数据源', '表名', '字段名', '指标类型',
      '当前值', '上期值', '变化率', '状态', '更新时间'
    ])
    headerRow.font = { bold: true }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 添加数据行
    tableData.value.forEach((item, index) => {
      const row = worksheet.addRow([
        index + 1,
        item.dataSource,
        item.tableName,
        item.fieldName,
        getIndicatorLabel(item.indicatorType),
        item.currentValue,
        item.previousValue,
        `${item.changeRate > 0 ? '+' : ''}${item.changeRate.toFixed(2)}%`,
        getStatusLabel(item.status),
        new Date(item.lastUpdateTime).toLocaleString('zh-CN')
      ])

      // 根据状态设置行颜色
      if (item.status === 'error') {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFFFEAEA' }
        }
      } else if (item.status === 'warning') {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFFFF4E6' }
        }
      }
    })

    // 设置边框
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber >= 4) { // 从表头开始
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }
        })
      }
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 下载文件
    const fileName = `数据质量指标报告_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(link.href)

    ElMessage.success('Excel报告导出成功！')
  } catch (error) {
    console.error('Excel导出失败:', error)
    throw new Error('Excel导出失败')
  }
}

// PDF导出功能
const exportToPDF = async () => {
  try {
    // 显示提示信息
    await ElMessageBox.alert(
      '当前系统使用浏览器打印功能来生成PDF报告。如需完整的PDF导出功能，请联系系统管理员安装相关组件。',
      'PDF导出说明',
      {
        confirmButtonText: '继续打印',
        type: 'info'
      }
    )

    // 调用打印功能
    window.print()
    ElMessage.success('PDF导出已启动，请在打印对话框中选择"另存为PDF"')
  } catch (error) {
    console.error('PDF导出失败:', error)
    throw new Error('PDF导出失败')
  }
}

// 文本导出功能
const exportToText = async () => {
  try {
    let content = '数据质量指标报告\n'
    content += '='.repeat(50) + '\n\n'
    content += `报告生成时间：${new Date().toLocaleString('zh-CN')}\n`
    content += `当前指标：${currentIndicatorLabel.value}\n`
    content += `总体得分：${qualityScoreInfo.value.totalScore}分\n\n`

    // 添加指标统计信息
    content += '指标统计信息：\n'
    content += '-'.repeat(30) + '\n'
    content += `字段重复率：${qualityScoreInfo.value.fieldDuplicateRate}%\n`
    content += `标准值匹配率：${qualityScoreInfo.value.standardValueMatchRate}%\n`
    content += `主键重复率：${qualityScoreInfo.value.primaryKeyDuplicateRate}%\n`
    content += `跨系统一致率：${qualityScoreInfo.value.crossSystemConsistencyRate}%\n`
    content += `字段格式合规率：${qualityScoreInfo.value.fieldFormatComplianceRate}%\n\n`

    // 添加同比环比数据
    if (comparisonData.value) {
      content += '变化情况：\n'
      content += '-'.repeat(30) + '\n'
      content += `环比变化率：${comparisonData.value.monthOverMonth.rate > 0 ? '+' : ''}${comparisonData.value.monthOverMonth.rate.toFixed(2)}%\n`
      content += `同比变化率：${comparisonData.value.yearOverYear.rate > 0 ? '+' : ''}${comparisonData.value.yearOverYear.rate.toFixed(2)}%\n\n`
    }

    // 添加详细数据
    content += '详细数据：\n'
    content += '-'.repeat(30) + '\n'
    content += '序号\t数据源\t表名\t字段名\t指标类型\t当前值\t上期值\t变化率\t状态\t更新时间\n'

    tableData.value.forEach((item, index) => {
      content += `${index + 1}\t${item.dataSource}\t${item.tableName}\t${item.fieldName}\t${getIndicatorLabel(item.indicatorType)}\t${item.currentValue}\t${item.previousValue}\t${item.changeRate > 0 ? '+' : ''}${item.changeRate.toFixed(2)}%\t${getStatusLabel(item.status)}\t${new Date(item.lastUpdateTime).toLocaleString('zh-CN')}\n`
    })

    // 创建下载
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const fileName = `数据质量指标报告_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.txt`
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(link.href)

    ElMessage.success('文本报告导出成功！')
  } catch (error) {
    console.error('文本导出失败:', error)
    throw new Error('文本导出失败')
  }
}

// JSON导出功能
const exportToJSON = async () => {
  try {
    const reportData = {
      reportInfo: {
        title: '数据质量指标报告',
        generateTime: new Date().toISOString(),
        currentIndicator: selectedIndicator.value,
        currentIndicatorLabel: currentIndicatorLabel.value,
        totalScore: qualityScoreInfo.value.totalScore
      },
      qualityMetrics: qualityScoreInfo.value,
      comparisonData: comparisonData.value,
      detailData: tableData.value,
      pagination: {
        currentPage: pagination.page,
        pageSize: pagination.size,
        total: pagination.total
      }
    }

    const jsonString = JSON.stringify(reportData, null, 2)
    const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8' })
    const fileName = `数据质量指标报告_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.json`
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(link.href)

    ElMessage.success('JSON数据导出成功！')
  } catch (error) {
    console.error('JSON导出失败:', error)
    throw new Error('JSON导出失败')
  }
}

// 报告分享
const onShareReport = async () => {
  try {
    loading.value = true
    ElMessage.info('正在生成分享链接...')

    // 模拟生成分享链接的过程
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 构建分享参数
    const shareParams = new URLSearchParams()
    shareParams.set('indicator', selectedIndicator.value)
    shareParams.set('score', qualityScoreInfo.value.totalScore.toString())
    shareParams.set('share', Date.now().toString())
    shareParams.set('type', 'qualityReport')
    shareParams.set('free', 'true') // 添加免登录参数

    // 生成分享链接
    const shareUrl = `${window.location.origin}/reportIntegrationSpotCheck/dataQualityReport?${shareParams.toString()}`

    // 准备分享内容
    const shareTitle = `数据质量指标报告 - ${currentIndicatorLabel.value}`
    const shareText = `数据质量指标报告

当前指标：${currentIndicatorLabel.value}
总体得分：${qualityScoreInfo.value.totalScore}分

指标详情：
• 字段重复率：${qualityScoreInfo.value.fieldDuplicateRate}%
• 标准值匹配率：${qualityScoreInfo.value.standardValueMatchRate}%
• 主键重复率：${qualityScoreInfo.value.primaryKeyDuplicateRate}%
• 跨系统一致率：${qualityScoreInfo.value.crossSystemConsistencyRate}%
• 字段格式合规率：${qualityScoreInfo.value.fieldFormatComplianceRate}%

${comparisonData.value ? `变化情况：
• 环比变化率：${comparisonData.value.monthOverMonth.rate > 0 ? '+' : ''}${comparisonData.value.monthOverMonth.rate.toFixed(2)}%
• 同比变化率：${comparisonData.value.yearOverYear.rate > 0 ? '+' : ''}${comparisonData.value.yearOverYear.rate.toFixed(2)}%

` : ''}报告生成时间：${new Date().toLocaleString('zh-CN')}

查看完整报告：${shareUrl}`

    // 尝试使用Web Share API（如果支持）
    if (navigator.share) {
      await navigator.share({
        title: shareTitle,
        text: shareText,
        url: shareUrl
      })
      ElMessage.success('分享成功')
    } else {
      // 备选方案：复制到剪贴板
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(shareText)
        ElMessage.success('分享内容已复制到剪贴板')

        // 显示分享信息
        ElMessageBox.alert(
          `数据质量指标报告的详细信息已复制到剪贴板，您可以粘贴分享给其他人。`,
          shareTitle,
          {
            confirmButtonText: '确定',
            type: 'success'
          }
        )
      } else {
        // 最后的备选方案：显示链接
        ElMessageBox.alert(
          `请复制以下链接分享：\n${shareUrl}`,
          shareTitle,
          {
            confirmButtonText: '确定',
            type: 'info'
          }
        )
      }
    }
  } catch (error: any) {
    console.error('分享失败:', error)
    if (error?.name === 'AbortError') {
      // 用户取消分享
      return
    }
    ElMessage.error('分享失败，请重试')
  } finally {
    loading.value = false
  }
}

// 初始化圆形图表
const initChart = () => {
  if (!chartRef.value) return

  try {
    chartLoading.value = true

    if (chartInstance.value) {
      chartInstance.value.dispose()
    }

    chartInstance.value = echarts.init(chartRef.value)

  const score = qualityScoreInfo.value.totalScore
  let color = '#ff9500' // 默认橙色

  // 根据得分设置颜色
  if (score >= 90) {
    color = '#67c23a' // 绿色 - 优秀
  } else if (score >= 80) {
    color = '#e6a23c' // 黄色 - 良好
  } else if (score >= 70) {
    color = '#ff9500' // 橙色 - 一般
  } else {
    color = '#f56c6c' // 红色 - 较差
  }

  const option = {
    series: [
      {
        type: 'pie',
        radius: ['65%', '85%'],
        center: ['50%', '50%'],
        startAngle: 90,
        data: [
          {
            value: score,
            name: '得分',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 1,
                colorStops: [
                  { offset: 0, color: color },
                  { offset: 1, color: color + '80' }
                ]
              },
              shadowBlur: 10,
              shadowColor: color + '40'
            }
          },
          {
            value: 100 - score,
            name: '剩余',
            itemStyle: {
              color: '#f0f0f0'
            },
            label: {
              show: false
            },
            emphasis: {
              disabled: true
            }
          }
        ],
        label: {
          show: false
        },
        emphasis: {
          disabled: true
        },
        animation: true,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: 0
      }
    ],
    graphic: [
      {
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text: score.toString(),
          fontSize: 42,
          fontWeight: 'bold',
          fill: color,
          fontFamily: 'Arial, sans-serif'
        }
      },
      {
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text: '',//数据质量得分
          fontSize: 12,
          fill: '#666',
          y: 25
        }
      }
    ]
  }

    chartInstance.value.setOption(option)

    // 添加窗口大小变化监听
    const resizeHandler = () => {
      if (chartInstance.value) {
        chartInstance.value.resize()
      }
    }
    window.addEventListener('resize', resizeHandler)

  } catch (error) {
    console.error('初始化图表失败:', error)
    ElMessage.error('图表加载失败')
  } finally {
    chartLoading.value = false
  }
}

// 初始化报告图表
const initReportChart = () => {
  if (!reportChartRef.value) return

  const reportChart = echarts.init(reportChartRef.value)
  const score = qualityScoreInfo.value.totalScore

  // 根据分数设置颜色
  let color = '#ff9500'
  if (score >= 90) color = '#67c23a'
  else if (score >= 80) color = '#e6a23c'
  else if (score >= 70) color = '#ff9500'
  else color = '#f56c6c'

  const option = {
    series: [
      {
        name: '数据质量得分',
        type: 'pie',
        radius: ['60%', '80%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2,
          color: {
            type: 'linear',
            colorStops: [
              { offset: 0, color: color },
              { offset: 1, color: color + '60' }
            ]
          },
          shadowBlur: 8,
          shadowColor: color + '30'
        },
        label: {
          show: false
        },
        data: [
          { value: score, name: '得分' },
          { value: 100 - score, name: '剩余', itemStyle: { color: '#f5f5f5' } }
        ],
        animationType: 'scale',
        animationEasing: 'elasticOut'
      }
    ],
    graphic: {
      type: 'text',
      left: 'center',
      top: 'center',
      style: {
        text: score.toString(),
        fontSize: 36,
        fontWeight: 'bold',
        fill: color
      }
    }
  }

  reportChart.setOption(option)

  // 添加窗口大小变化监听
  const reportResizeHandler = () => {
    reportChart.resize()
  }
  window.addEventListener('resize', reportResizeHandler)
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return

  const trendChart = echarts.init(trendChartRef.value)

  // 生成趋势数据
  const generateTrendData = () => {
    const days = trendTimeRange.value === '7days' ? 7 : trendTimeRange.value === '30days' ? 30 : 90
    const dates = []
    const completenessData = []
    const accuracyData = []
    const consistencyData = []
    const timelinessData = []

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))

      // 生成模拟趋势数据，围绕当前值波动
      completenessData.push(85 + Math.random() * 10 - 5)
      accuracyData.push(78 + Math.random() * 10 - 5)
      consistencyData.push(82 + Math.random() * 10 - 5)
      timelinessData.push(90 + Math.random() * 8 - 4)
    }

    return { dates, completenessData, accuracyData, consistencyData, timelinessData }
  }

  const { dates, completenessData, accuracyData, consistencyData, timelinessData } = generateTrendData()

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      name: '时间',
      nameTextStyle: {
        color: '#666',
        fontSize: 12
      },
      boundaryGap: false,
      data: dates,
      axisLabel: {
        fontSize: 12,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '指标得分',
      nameTextStyle: {
        color: '#666',
        fontSize: 12
      },
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}分',
        fontSize: 12,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5'
        }
      }
    },
    series: [
      {
        name: '趋势',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          color: '#409eff',
          width: 2
        },
        itemStyle: {
          color: '#409eff'
        },
        data: completenessData
      }
    ]
  }

  trendChart.setOption(option)

  // 添加窗口大小变化监听
  const trendResizeHandler = () => {
    trendChart.resize()
  }
  window.addEventListener('resize', trendResizeHandler)
}

// 监听趋势时间范围变化
watch(trendTimeRange, () => {
  if (!isIndicatorReport.value) {
    nextTick(() => {
      initTrendChart()
    })
  }
})

// 组件挂载
onMounted(() => {
  initializeData()
  nextTick(() => {
    if (isIndicatorReport.value) {
      initChart()
    } else {
      initReportChart()
      initTrendChart()
    }
  })
})
</script>

<template>
  <div class="data-quality-report">
    <Block 
      :title="isIndicatorReport ? '数据质量指标报告' : '数据质量报告'"
      :enable-fixed-height="true" 
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <!-- 视图切换开关 -->
        <div style="display: flex; align-items: center; gap: 16px;">
          <span style="font-size: 14px; color: #666;">
            {{ isIndicatorReport ? '切换为数据质量报告' : '切换为数据质量指标报告' }}
          </span>
          <el-switch
            v-model="isIndicatorReport"
            @change="onViewModeChange"
            style="margin-right: 16px;"
          />
          
          <!-- 数据质量规则设置按钮 -->
          <el-button size="small" type="primary" @click="onClickRuleSettings">
            数据质量规则设置
          </el-button>
          <!-- 返回按钮 -->
          <el-button size="small" type="default" @click="$router.push('/reportIntegrationSpotCheck')">
            返回
          </el-button>
        </div>
    
      </template>

      <!-- 错误状态显示 -->
      <div v-if="dataError" class="error-state" style="text-align: center; padding: 40px;">
        <el-result
          icon="error"
          title="数据加载失败"
          :sub-title="errorMessage"
        >
          <template #extra>
            <el-button type="primary" @click="initializeData">重新加载</el-button>
          </template>
        </el-result>
      </div>

      <!-- 指标报告模式内容 -->
      <div v-else-if="isIndicatorReport" class="indicator-report-content">
        <!-- 操作按钮区域 -->
        <div class="action-buttons" style="margin-bottom: 20px;">
          <el-button size="small" type="primary" @click="onExportReport">报告导出</el-button>
          <el-button size="small" type="default" @click="onPrintReport">报告打印</el-button>
          <el-button size="small" type="default" @click="onShareReport">报告分享</el-button>
        </div>

        <!-- 数据质量得分和指标选择区域 -->
        <div class="score-and-indicator" style="display: flex; gap: 40px; margin-bottom: 30px;">
          <!-- 数据质量得分圆形图 -->
          <div class="score-chart" style="flex: 0 0 200px;">
            <div
              ref="chartRef"
              v-loading="chartLoading"
              element-loading-text="加载图表中..."
              style="width: 200px; height: 200px;"
            ></div>
            <div style="text-align: center; margin-top: 10px;">
              <div style="font-size: 16px; font-weight: bold;">数据质量指标得分</div>
            </div>
          </div>

          <!-- 指标信息和选择器 -->
          <div class="indicator-info" style="flex: 1;">
            <!-- 当前指标情况 -->
            <div style="margin-bottom: 20px;">
              <h3>当前指标情况</h3>
              <div class="indicator-stats">
                <div class="stat-item">数据质量指标得分: <strong>{{ qualityScoreInfo.totalScore }}</strong></div>
                <div class="stat-item">字段重复率: <strong>{{ qualityScoreInfo.fieldDuplicateRate }}%</strong></div>
                <div class="stat-item">标准值匹配率: <strong>{{ qualityScoreInfo.standardValueMatchRate }}%</strong></div>
                <div class="stat-item">主键重复率: <strong>{{ qualityScoreInfo.primaryKeyDuplicateRate }}%</strong></div>
                <div class="stat-item">跨系统一致率: <strong>{{ qualityScoreInfo.crossSystemConsistencyRate }}%</strong></div>
                <div class="stat-item">字段格式合规率: <strong>{{ qualityScoreInfo.fieldFormatComplianceRate }}%</strong></div>
              </div>
            </div>

            <!-- 指标同比环比选择 -->
            <div>
              <label style="font-weight: bold; margin-right: 10px;">指标同比环比变化:</label>
              <el-select 
                v-model="selectedIndicator" 
                @change="onIndicatorChange"
                style="width: 200px;"
              >
                <el-option
                  v-for="option in qualityIndicatorOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </div>

            <!-- 同比环比数据展示 -->
            <div v-if="comparisonData" style="margin-top: 20px;">
              <h4>{{ currentIndicatorLabel }}变化情况</h4>
              <div class="comparison-data">
                <div class="comparison-item">
                  <div class="comparison-label">环比变化率</div>
                  <div class="comparison-value"
                       :class="`trend-${comparisonData.monthOverMonth.trend}`">
                    {{ comparisonData.monthOverMonth.rate > 0 ? '+' : '' }}{{ comparisonData.monthOverMonth.rate.toFixed(2) }}%
                  </div>
                </div>
                <div class="comparison-item">
                  <div class="comparison-label">同比变化率</div>
                  <div class="comparison-value"
                       :class="`trend-${comparisonData.yearOverYear.trend}`">
                    {{ comparisonData.yearOverYear.rate > 0 ? '+' : '' }}{{ comparisonData.yearOverYear.rate.toFixed(2) }}%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据表格 -->
        <TableV2
          ref="tableRef"
          :defaultTableData="formattedTableData"
          :columns="columns"
          :enable-toolbar="false"
          :enable-own-button="false"
          :height="tableHeight"
          :loading="loading"
        />

        <!-- 分页 -->
        <Pagination
          :total="pagination.total"
          :current-page="pagination.page"
          :page-size="pagination.size"
          @current-change="onPaginationChange($event, 'page')"
          @size-change="onPaginationChange($event, 'size')"
        />
      </div>

      <!-- 数据质量报告模式内容 -->
      <div v-else class="quality-report-content" style="display: flex; gap: 30px; height: 100%; padding: 20px;">
        <!-- 左侧区域 -->
        <div class="report-left-section" style="flex: 0 0 500px; display: flex; flex-direction: column; gap: 20px;">
          <!-- 圆形图表区域 -->
          <div style="background: white; border-radius: 12px; padding: 30px; text-align: center;">
            <div style="position: relative; width: 300px; height: 300px; margin: 0 auto;">
              <div ref="reportChartRef" style="width: 100%; height: 100%;"></div>
              <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                <div style="font-size: 72px; font-weight: bold; color: #ff9500; line-height: 1;"></div>
                <!-- {{ qualityScoreInfo.totalScore }} -->
              </div>
            </div>
            <div style="font-size: 16px; color: #666; margin-top: 15px;">数据质量指标得分</div>
          </div>

          <!-- 趋势分析区域 -->
          <div style="background: white; border-radius: 12px; padding: 20px;">
            <h3 style="margin: 0 0 20px 0; font-size: 16px; color: #333; font-weight: 600;">趋势分析</h3>

            <!-- 选择器区域 -->
            <div style="display: flex; gap: 15px; margin-bottom: 20px; align-items: center;">
              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 14px; color: #666;">选择指标：</span>
                <el-select v-model="trendIndicator" style="width: 120px;" size="small">
                  <el-option label="字段重复率" value="fieldDuplicate" />
                  <el-option label="主键重复率" value="primaryKeyDuplicate" />
                  <el-option label="字段格式合规率" value="fieldFormat" />
                  <el-option label="标准值匹配率" value="standardValue" />
                  <el-option label="跨系统一致率" value="crossSystem" />
                </el-select>
              </div>

              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 14px; color: #666;">选择时间范围：</span>
                <el-date-picker
                  v-model="trendDateRange"
                  type="daterange"
                  size="small"
                  style="width: 240px;"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </div>
            </div>

            <!-- 折线图 -->
            <div ref="trendChartRef" style="width: 100%; height: 200px;"></div>
          </div>
        </div>

        <!-- 右侧信息区域 -->
        <div class="report-right-section" style="flex: 1; display: flex; flex-direction: column; gap: 20px;">
          <!-- 当前指标情况 -->
          <div style="background: white; border-radius: 12px; padding: 20px;">
            <h3 style="margin: 0 0 20px 0; font-size: 16px; color: #333; font-weight: 600;">当前指标情况</h3>

            <!-- 数据质量指标得分 -->
            <div style="margin-bottom: 20px;">
              <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 14px; color: #666;">数据质量指标得分：</span>
                <span style="font-size: 18px; font-weight: bold; color: #ff9500;">{{ qualityScoreInfo.totalScore }}</span>
              </div>
            </div>

            <!-- 指标列表 -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
              <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                <span style="font-size: 14px; color: #666;">字段重复率：</span>
                <span style="font-size: 14px; font-weight: 500; color: #333;">{{ qualityScoreInfo.fieldDuplicateRate }}%</span>
              </div>
              <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                <span style="font-size: 14px; color: #666;">标准值匹配：</span>
                <span style="font-size: 14px; font-weight: 500; color: #333;">{{ qualityScoreInfo.standardValueMatchRate }}%</span>
              </div>
              <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                <span style="font-size: 14px; color: #666;">主键重复率：</span>
                <span style="font-size: 14px; font-weight: 500; color: #333;">{{ qualityScoreInfo.primaryKeyDuplicateRate }}%</span>
              </div>
              <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                <span style="font-size: 14px; color: #666;">跨系统一致率：</span>
                <span style="font-size: 14px; font-weight: 500; color: #333;">{{ qualityScoreInfo.crossSystemConsistencyRate }}%</span>
              </div>
              <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                <span style="font-size: 14px; color: #666;">字段格式合规率：</span>
                <span style="font-size: 14px; font-weight: 500; color: #333;">{{ qualityScoreInfo.fieldFormatComplianceRate }}%</span>
              </div>
            </div>
          </div>

          <!-- 指标同比环比变化 -->
          <div style="background: white; border-radius: 12px; padding: 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
              <h3 style="margin: 0; font-size: 16px; color: #333; font-weight: 600;">指标同比环比变化</h3>
              <el-select v-model="comparisonIndicator" style="width: 150px;" size="small">
                <el-option label="字段重复率" value="fieldDuplicate" />
                <el-option label="主键重复率" value="primaryKeyDuplicate" />
                <el-option label="字段格式合规率" value="fieldFormat" />
                <el-option label="标准值匹配率" value="standardValue" />
                <el-option label="跨系统一致率" value="crossSystem" />
              </el-select>
            </div>

            <!-- 同比变化幅度 -->
            <div style="margin-bottom: 20px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                <span style="font-size: 14px; color: #666;">同比变化幅度</span>
                <span style="font-size: 14px; font-weight: 500; color: #ff9500;">21%</span>
              </div>
              <el-progress
                :percentage="21"
                color="#ff9500"
                :stroke-width="8"
                :show-text="false"
              />
              <div style="display: flex; align-items: center; gap: 5px; margin-top: 5px;">
                <el-icon style="color: #ff9500; font-size: 12px;"><Warning /></el-icon>
                <span style="font-size: 12px; color: #666;">同比增长21%，需关注</span>
              </div>
            </div>

            <!-- 环比变化幅度 -->
            <div>
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                <span style="font-size: 14px; color: #666;">环比变化幅度</span>
                <span style="font-size: 14px; font-weight: 500; color: #ff9500;">21%</span>
              </div>
              <el-progress
                :percentage="21"
                color="#ff9500"
                :stroke-width="8"
                :show-text="false"
              />
              <div style="display: flex; align-items: center; gap: 5px; margin-top: 5px;">
                <el-icon style="color: #ff9500; font-size: 12px;"><Warning /></el-icon>
                <span style="font-size: 12px; color: #666;">环比增长21%，需关注</span>
              </div>
            </div>
          </div>

          <!-- 数据质量规则设置 -->
          <div style="background: #f0f8ff; border-radius: 12px; padding: 20px; text-align: center;">
            <h3 style="margin: 0 0 15px 0; font-size: 16px; color: #333; font-weight: 600;">数据质量规则设置</h3>
            <div style="display: flex; gap: 10px; justify-content: center;">
              <el-button type="primary" size="small" @click="onExportReport">报告导出</el-button>
              <el-button type="primary" size="small" @click="onPrintReport">报告打印</el-button>
              <el-button type="primary" size="small" @click="onShareReport">报告分享</el-button>
            </div>
          </div>
        </div>

        <!-- 趋势分析折线图 -->
        <div style="background: white; border-radius: 12px; padding: 20px; margin-top: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="margin: 0; font-size: 18px; color: #333; font-weight: 600;">数据质量趋势分析</h3>
            <el-select v-model="trendTimeRange" style="width: 150px;">
              <el-option label="最近7天" value="7days" />
              <el-option label="最近30天" value="30days" />
              <el-option label="最近90天" value="90days" />
            </el-select>
          </div>
          <div ref="trendChartRef" style="width: 100%; height: 400px;"></div>
        </div>
      </div>
    </Block>

    <!-- 数据质量规则设置对话框 -->
    <Dialog
      v-model="showRuleDialog"
      title="数据质量指标规则设置"
      width="600px"
      :destroy-on-close="true"
      confirm-text="确定"
      cancel-text="取消"
      @click-confirm="onSaveRuleConfig"
      @click-cancel="showRuleDialog = false"
    >
      <div class="rule-dialog-content">
        <!-- 字段重复率 -->
        <el-collapse v-model="activeRuleNames" accordion>
          <el-collapse-item title="字段重复率" name="fieldDuplicateRate">
            <template #title>
              <div class="rule-title">
                <el-icon><CaretRight /></el-icon>
                <span>字段重复率</span>
                <span class="rule-desc">字段中重复值的比例</span>
              </div>
            </template>
            <div class="rule-config">
              <div class="config-row">
                <label>计算方式</label>
                <el-input
                  v-model="ruleForm.fieldDuplicateRate.calculationMethod"
                  placeholder="请输入计算方式"
                />
              </div>
              <div class="config-row">
                <label>数据范围</label>
                <div class="range-inputs">
                  <el-select v-model="ruleForm.fieldDuplicateRate.dataRange.type" style="width: 80px;">
                    <el-option label="全部" value="全部" />
                    <el-option label="自定义" value="自定义" />
                  </el-select>
                  <el-input
                    v-model="ruleForm.fieldDuplicateRate.dataRange.value"
                    placeholder="值"
                    style="width: 100px; margin-left: 8px;"
                  />
                </div>
                <div class="weight-input">
                  <label>权重</label>
                  <el-input
                    v-model="ruleForm.fieldDuplicateRate.weight"
                    type="number"
                    style="width: 60px;"
                  />
                  <span>%</span>
                </div>
              </div>
            </div>
          </el-collapse-item>

          <!-- 主键重复率 -->
          <el-collapse-item title="主键重复率" name="primaryKeyDuplicateRate">
            <template #title>
              <div class="rule-title">
                <el-icon><CaretRight /></el-icon>
                <span>主键重复率</span>
                <span class="rule-desc">主键字段中重复值的比例</span>
              </div>
            </template>
            <div class="rule-config">
              <div class="config-row">
                <label>计算方式</label>
                <el-input
                  v-model="ruleForm.primaryKeyDuplicateRate.calculationMethod"
                  placeholder="请输入计算方式"
                />
              </div>
              <div class="config-row">
                <label>数据范围</label>
                <div class="range-inputs">
                  <el-select v-model="ruleForm.primaryKeyDuplicateRate.dataRange.type" style="width: 80px;">
                    <el-option label="全部" value="全部" />
                    <el-option label="自定义" value="自定义" />
                  </el-select>
                  <el-input
                    v-model="ruleForm.primaryKeyDuplicateRate.dataRange.value"
                    placeholder="值"
                    style="width: 100px; margin-left: 8px;"
                  />
                </div>
                <div class="weight-input">
                  <label>权重</label>
                  <el-input
                    v-model="ruleForm.primaryKeyDuplicateRate.weight"
                    type="number"
                    style="width: 60px;"
                  />
                  <span>%</span>
                </div>
              </div>
              <div class="config-row">
                <label>选择主键</label>
                <el-select v-model="ruleForm.primaryKeyDuplicateRate.primaryKey" style="width: 200px;">
                  <el-option label="字段1" value="字段1" />
                  <el-option label="字段2" value="字段2" />
                  <el-option label="字段3" value="字段3" />
                </el-select>
              </div>
            </div>
          </el-collapse-item>

          <!-- 字段格式合规率 -->
          <el-collapse-item title="字段格式合规率" name="fieldFormatComplianceRate">
            <template #title>
              <div class="rule-title">
                <el-icon><CaretRight /></el-icon>
                <span>字段格式合规率</span>
                <span class="rule-desc">字段格式符合规范的比例</span>
              </div>
            </template>
            <div class="rule-config">
              <div class="config-row">
                <label>计算方式</label>
                <el-input
                  v-model="ruleForm.fieldFormatComplianceRate.calculationMethod"
                  placeholder="请输入计算方式"
                />
              </div>
              <div class="config-row">
                <label>数据范围</label>
                <div class="range-inputs">
                  <el-select v-model="ruleForm.fieldFormatComplianceRate.dataRange.type" style="width: 80px;">
                    <el-option label="全部" value="全部" />
                    <el-option label="自定义" value="自定义" />
                  </el-select>
                  <el-input
                    v-model="ruleForm.fieldFormatComplianceRate.dataRange.value"
                    placeholder="值"
                    style="width: 100px; margin-left: 8px;"
                  />
                </div>
                <div class="weight-input">
                  <label>权重</label>
                  <el-input
                    v-model="ruleForm.fieldFormatComplianceRate.weight"
                    type="number"
                    style="width: 60px;"
                  />
                  <span>%</span>
                </div>
              </div>
              <div class="config-row">
                <label>选择标准表</label>
                <el-select v-model="ruleForm.fieldFormatComplianceRate.primaryKey" style="width: 200px;">
                  <el-option label="指标1" value="指标1" />
                  <el-option label="指标2" value="指标2" />
                  <el-option label="指标3" value="指标3" />
                </el-select>
              </div>
            </div>
          </el-collapse-item>

          <!-- 标准值匹配率 -->
          <el-collapse-item title="标准值匹配率" name="standardValueMatchRate">
            <template #title>
              <div class="rule-title">
                <el-icon><CaretRight /></el-icon>
                <span>标准值匹配率</span>
                <span class="rule-desc">数据与标准值匹配的比例</span>
              </div>
            </template>
            <div class="rule-config">
              <div class="config-row">
                <label>计算方式</label>
                <el-input
                  v-model="ruleForm.standardValueMatchRate.calculationMethod"
                  placeholder="请输入计算方式"
                />
              </div>
              <div class="config-row">
                <label>数据范围</label>
                <div class="range-inputs">
                  <el-select v-model="ruleForm.standardValueMatchRate.dataRange.type" style="width: 80px;">
                    <el-option label="全部" value="全部" />
                    <el-option label="自定义" value="自定义" />
                  </el-select>
                  <el-input
                    v-model="ruleForm.standardValueMatchRate.dataRange.value"
                    placeholder="值"
                    style="width: 100px; margin-left: 8px;"
                  />
                </div>
                <div class="weight-input">
                  <label>权重</label>
                  <el-input
                    v-model="ruleForm.standardValueMatchRate.weight"
                    type="number"
                    style="width: 60px;"
                  />
                  <span>%</span>
                </div>
              </div>
            </div>
          </el-collapse-item>

          <!-- 跨系统一致率 -->
          <el-collapse-item title="跨系统一致率" name="crossSystemConsistencyRate">
            <template #title>
              <div class="rule-title">
                <el-icon><CaretRight /></el-icon>
                <span>跨系统一致率</span>
                <span class="rule-desc">不同系统间数据一致性的比例</span>
              </div>
            </template>
            <div class="rule-config">
              <div class="config-row">
                <label>计算方式</label>
                <el-input
                  v-model="ruleForm.crossSystemConsistencyRate.calculationMethod"
                  placeholder="请输入计算方式"
                />
              </div>
              <div class="config-row">
                <label>数据范围</label>
                <div class="range-inputs">
                  <el-select v-model="ruleForm.crossSystemConsistencyRate.dataRange.type" style="width: 80px;">
                    <el-option label="全部" value="全部" />
                    <el-option label="自定义" value="自定义" />
                  </el-select>
                  <el-input
                    v-model="ruleForm.crossSystemConsistencyRate.dataRange.value"
                    placeholder="值"
                    style="width: 100px; margin-left: 8px;"
                  />
                </div>
                <div class="weight-input">
                  <label>权重</label>
                  <el-input
                    v-model="ruleForm.crossSystemConsistencyRate.weight"
                    type="number"
                    style="width: 60px;"
                  />
                  <span>%</span>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>

        <!-- 超出阈值告警 -->
        <div class="alert-setting">
          <label>超出阈值告警</label>
          <el-radio-group v-model="ruleForm.alertEnabled">
            <el-radio :label="true">开启</el-radio>
            <el-radio :label="false">关闭</el-radio>
          </el-radio-group>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<route>
{
  meta: {
    title: '数据质量指标报告',
  },
}
</route>

<style scoped lang="scss">
.data-quality-report {
  .action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;

    .el-button {
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .score-chart {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }
  }

  .indicator-info {
    h3, h4 {
      color: #333;
      margin-bottom: 15px;
      font-weight: 600;
    }

    .indicator-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 20px;

      .stat-item {
        padding: 12px 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #409eff;
        transition: all 0.3s ease;

        &:hover {
          background: #e3f2fd;
          transform: translateX(4px);
        }

        strong {
          color: #409eff;
          font-size: 16px;
        }
      }
    }

    .comparison-data {
      display: flex;
      gap: 30px;
      margin-top: 20px;

      .comparison-item {
        flex: 1;
        padding: 16px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        text-align: center;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        .comparison-label {
          color: #666;
          font-size: 14px;
          margin-bottom: 8px;
        }

        .comparison-value {
          font-size: 20px;
          font-weight: bold;

          &.trend-up {
            color: #67c23a;
          }

          &.trend-down {
            color: #f56c6c;
          }

          &.trend-stable {
            color: #909399;
          }
        }
      }
    }
  }

  .rule-dialog-content {
    max-height: 500px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .rule-item {
    background-color: #fafafa;
    transition: all 0.3s ease;
    border: 1px solid #e4e7ed;

    &:hover {
      background-color: #f0f9ff;
      border-color: #409eff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
    }

    .rule-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      h4 {
        margin: 0;
        color: #303133;
        font-weight: 600;
      }
    }

    .rule-method {
      color: #606266;
      margin-bottom: 8px;
      font-size: 14px;

      strong {
        color: #409eff;
      }
    }

    .rule-thresholds {
      display: flex;
      gap: 20px;
      font-size: 14px;
      color: #909399;
      flex-wrap: wrap;

      span {
        strong {
          color: #303133;
        }
      }
    }
  }

  .error-state {
    .el-result {
      padding: 40px 20px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .data-quality-report {
    .score-and-indicator {
      gap: 30px !important;
    }

    .indicator-info {
      .indicator-stats {
        grid-template-columns: repeat(auto-fit, minWidth(180px, 1fr)) !important;
      }

      .comparison-data {
        gap: 20px !important;
      }
    }
  }
}

@media (max-width: 768px) {
  .data-quality-report {
    .score-and-indicator {
      flex-direction: column !important;
      gap: 20px !important;
    }

    .score-chart {
      flex: none !important;
      align-self: center;
      width: 100%;
      max-width: 300px;
    }

    .indicator-info {
      .indicator-stats {
        grid-template-columns: 1fr !important;
      }

      .comparison-data {
        flex-direction: column !important;
        gap: 15px !important;
      }
    }

    .action-buttons {
      justify-content: center;

      .el-button {
        flex: 1;
        min-width: 100px;
      }
    }

    .rule-item {
      .rule-thresholds {
        flex-direction: column !important;
        gap: 8px !important;
      }
    }
  }
}

@media (max-width: 480px) {
  .data-quality-report {
    .action-buttons {
      flex-direction: column;

      .el-button {
        width: 100%;
      }
    }

    .score-chart {
      padding: 15px;

      div[ref="chartRef"] {
        width: 180px !important;
        height: 180px !important;
      }
    }
  }

  .rule-dialog-content {
    .rule-title {
      display: flex;
      align-items: center;
      gap: 8px;

      .rule-desc {
        color: #909399;
        font-size: 12px;
        margin-left: auto;
      }
    }

    .rule-config {
      padding: 16px 0;

      .config-row {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        gap: 12px;

        label {
          min-width: 80px;
          color: #606266;
          font-size: 14px;
        }

        .range-inputs {
          display: flex;
          align-items: center;
          flex: 1;
        }

        .weight-input {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-left: auto;

          label {
            min-width: auto;
          }

          span {
            color: #909399;
          }
        }
      }
    }

    .alert-setting {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      margin-top: 20px;
      background-color: #f5f7fa;
      border-radius: 8px;
      border: 1px solid #e4e7ed;

      label {
        color: #606266;
        font-weight: 500;
      }
    }

    :deep(.el-collapse) {
      border: none;

      .el-collapse-item {
        margin-bottom: 8px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        overflow: hidden;

        &.is-active {
          border-color: #409eff;
        }

        .el-collapse-item__header {
          background-color: #fafafa;
          border: none;
          padding: 12px 16px;

          &.is-active {
            background-color: #ecf5ff;
          }
        }

        .el-collapse-item__content {
          padding: 0 16px;
          border: none;
        }
      }
    }
  }

  // 数据质量报告样式
  .quality-report-content {
    display: flex;
    gap: 30px;
    height: 100%;

    .report-left-section {
      flex: 0 0 400px;
      display: flex;
      align-items: center;
      justify-content: center;

      .score-chart-wrapper {
        position: relative;
        width: 300px;
        height: 300px;

        .report-chart {
          width: 100%;
          height: 100%;
        }

        .score-text {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;

          .score-number {
            font-size: 48px;
            font-weight: bold;
            color: #409eff;
            line-height: 1;
          }

          .score-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
          }
        }
      }
    }

    .report-right-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 30px;

      .current-indicator-section {
        h3 {
          margin: 0 0 20px 0;
          font-size: 18px;
          color: #333;
          font-weight: 600;
        }

        .indicator-stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 15px;

          .stat-card {
            background: white;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;

            &:hover {
              border-color: #409eff;
              box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
            }

            .stat-label {
              font-size: 14px;
              color: #666;
              margin-bottom: 8px;
            }

            .stat-value {
              font-size: 24px;
              font-weight: bold;
              color: #409eff;
            }
          }
        }
      }

      .comparison-analysis-section {
        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          h3 {
            margin: 0;
            font-size: 18px;
            color: #333;
            font-weight: 600;
          }
        }

        .comparison-data-display {
          display: flex;
          gap: 30px;

          .comparison-item {
            flex: 1;
            background: white;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 20px;
            text-align: center;

            .comparison-label {
              font-size: 14px;
              color: #666;
              margin-bottom: 10px;
            }

            .comparison-value {
              font-size: 20px;
              font-weight: bold;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 5px;

              &.trend-up {
                color: #67c23a;
              }

              &.trend-down {
                color: #f56c6c;
              }

              &.trend-stable {
                color: #909399;
              }

              .el-icon {
                font-size: 16px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
