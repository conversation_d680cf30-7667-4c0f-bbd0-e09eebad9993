import type { 
  QualityReportDataItem, 
  QualityRuleConfig, 
  QualityScoreInfo,
  PaginationInfo,
  QualityIndicatorType 
} from './types'
import { 
  generateMockQualityReportData, 
  generateMockQualityRuleConfigs, 
  generateMockQualityScoreInfo 
} from './mockData'

/**
 * 本地存储键名常量
 */
export const STORAGE_KEYS = {
  QUALITY_REPORT_DATA: 'dataQualityReport_reportData',
  QUALITY_RULE_CONFIGS: 'dataQualityReport_ruleConfigs',
  QUALITY_SCORE_INFO: 'dataQualityReport_scoreInfo',
  PAGINATION_INFO: 'dataQualityReport_pagination',
  SELECTED_INDICATOR: 'dataQualityReport_selectedIndicator',
  VIEW_MODE: 'dataQualityReport_viewMode' // 'report' | 'indicator'
} as const

/**
 * 通用本地存储工具类
 */
class LocalStorageManager<T> {
  private key: string

  constructor(key: string) {
    this.key = key
  }

  /**
   * 获取数据
   */
  get(): T | null {
    try {
      const data = localStorage.getItem(this.key)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.error(`获取${this.key}数据失败:`, error)
      return null
    }
  }

  /**
   * 保存数据
   */
  save(data: T): boolean {
    try {
      localStorage.setItem(this.key, JSON.stringify(data))
      return true
    } catch (error) {
      console.error(`保存${this.key}数据失败:`, error)
      return false
    }
  }

  /**
   * 删除数据
   */
  remove(): boolean {
    try {
      localStorage.removeItem(this.key)
      return true
    } catch (error) {
      console.error(`删除${this.key}数据失败:`, error)
      return false
    }
  }
}

/**
 * 数组类型本地存储工具类
 */
class ArrayStorageManager<T> extends LocalStorageManager<T[]> {
  /**
   * 获取所有数据
   */
  getAll(): T[] {
    return this.get() || []
  }

  /**
   * 根据ID获取单条数据
   */
  getById(id: string): T | undefined {
    const data = this.getAll()
    return data.find((item: any) => item.id === id)
  }

  /**
   * 添加数据
   */
  add(item: T): boolean {
    const data = this.getAll()
    data.push(item)
    return this.save(data)
  }

  /**
   * 更新数据
   */
  update(id: string, updates: Partial<T>): boolean {
    const data = this.getAll()
    const index = data.findIndex((item: any) => item.id === id)
    if (index !== -1) {
      data[index] = { ...data[index], ...updates }
      return this.save(data)
    }
    return false
  }

  /**
   * 删除数据
   */
  delete(id: string): boolean {
    const data = this.getAll()
    const filteredData = data.filter((item: any) => item.id !== id)
    return this.save(filteredData)
  }

  /**
   * 获取数据总数
   */
  count(): number {
    return this.getAll().length
  }

  /**
   * 清空所有数据
   */
  clear(): boolean {
    return this.save([])
  }
}

/**
 * 数据质量报告数据存储管理器
 */
export class QualityReportDataStorage extends ArrayStorageManager<QualityReportDataItem> {
  constructor() {
    super(STORAGE_KEYS.QUALITY_REPORT_DATA)
  }

  /**
   * 初始化数据（如果本地没有数据则使用模拟数据）
   */
  initializeData(): void {
    if (this.count() === 0) {
      console.log('初始化数据质量报告数据...')
      this.save(generateMockQualityReportData())
    }
  }

  /**
   * 根据指标类型筛选数据
   */
  getByIndicatorType(indicatorType: QualityIndicatorType): QualityReportDataItem[] {
    return this.getAll().filter(item => item.indicatorType === indicatorType)
  }

  /**
   * 根据状态筛选数据
   */
  getByStatus(status: string): QualityReportDataItem[] {
    return this.getAll().filter(item => item.status === status)
  }

  /**
   * 分页获取数据
   */
  getPaginated(page: number, size: number, filters?: any): {
    data: QualityReportDataItem[]
    total: number
  } {
    let allData = this.getAll()

    // 应用筛选条件
    if (filters) {
      if (filters.indicatorType) {
        allData = allData.filter(item => item.indicatorType === filters.indicatorType)
      }
      if (filters.status) {
        allData = allData.filter(item => item.status === filters.status)
      }
      if (filters.dataSource) {
        allData = allData.filter(item => item.dataSource.includes(filters.dataSource))
      }
    }

    const total = allData.length
    const startIndex = (page - 1) * size
    const endIndex = startIndex + size
    const data = allData.slice(startIndex, endIndex)

    return { data, total }
  }
}

/**
 * 数据质量规则配置存储管理器
 */
export class QualityRuleConfigStorage extends ArrayStorageManager<QualityRuleConfig> {
  constructor() {
    super(STORAGE_KEYS.QUALITY_RULE_CONFIGS)
  }

  /**
   * 初始化数据
   */
  initializeData(): void {
    if (this.count() === 0) {
      console.log('初始化数据质量规则配置数据...')
      this.save(generateMockQualityRuleConfigs())
    }
  }

  /**
   * 根据指标类型获取规则配置
   */
  getByIndicatorType(indicatorType: QualityIndicatorType): QualityRuleConfig | undefined {
    return this.getAll().find(config => config.indicatorType === indicatorType)
  }

  /**
   * 启用/禁用规则
   */
  toggleEnabled(id: string): boolean {
    const config = this.getById(id)
    if (!config) return false
    
    return this.update(id, { isEnabled: !config.isEnabled })
  }
}

/**
 * 数据质量得分信息存储管理器
 */
export class QualityScoreInfoStorage extends LocalStorageManager<QualityScoreInfo> {
  constructor() {
    super(STORAGE_KEYS.QUALITY_SCORE_INFO)
  }

  /**
   * 初始化数据
   */
  initializeData(): void {
    if (!this.get()) {
      console.log('初始化数据质量得分信息...')
      this.save(generateMockQualityScoreInfo())
    }
  }

  /**
   * 更新得分信息
   */
  updateScore(updates: Partial<QualityScoreInfo>): boolean {
    const current = this.get() || generateMockQualityScoreInfo()
    const updated = { ...current, ...updates, lastUpdateTime: new Date().toISOString() }
    return this.save(updated)
  }
}

/**
 * 分页信息存储管理器
 */
export class PaginationStorage extends LocalStorageManager<PaginationInfo> {
  constructor() {
    super(STORAGE_KEYS.PAGINATION_INFO)
  }

  /**
   * 获取分页信息（带默认值）
   */
  getPagination(): PaginationInfo {
    return this.get() || { page: 1, size: 20, total: 0 }
  }
}

/**
 * 选中指标存储管理器
 */
export class SelectedIndicatorStorage extends LocalStorageManager<QualityIndicatorType> {
  constructor() {
    super(STORAGE_KEYS.SELECTED_INDICATOR)
  }

  /**
   * 获取选中的指标（带默认值）
   */
  getSelectedIndicator(): QualityIndicatorType {
    return this.get() || 'fieldDuplicateRate'
  }
}

/**
 * 视图模式存储管理器
 */
export class ViewModeStorage extends LocalStorageManager<'report' | 'indicator'> {
  constructor() {
    super(STORAGE_KEYS.VIEW_MODE)
  }

  /**
   * 获取视图模式（带默认值）
   */
  getViewMode(): 'report' | 'indicator' {
    return this.get() || 'indicator'
  }
}

// 导出存储管理器实例
export const qualityReportDataStorage = new QualityReportDataStorage()
export const qualityRuleConfigStorage = new QualityRuleConfigStorage()
export const qualityScoreInfoStorage = new QualityScoreInfoStorage()
export const paginationStorage = new PaginationStorage()
export const selectedIndicatorStorage = new SelectedIndicatorStorage()
export const viewModeStorage = new ViewModeStorage()
