// 规则配置相关类型定义

// 导航菜单项类型
export interface NavigationItem {
  id: string
  label: string
  icon?: string
  children?: NavigationItem[]
  path?: string
}

// 重复判断规则配置项类型
export interface DuplicateRule {
  id: string
  createTime: string
  reportAType: 'business' | 'temporary' // 报表A类型：业务表 | 临时表
  reportAField: string // 报表A字段
  reportBType: 'business' | 'temporary' // 报表B类型：业务表 | 临时表
  reportBField: string // 报表B字段
  matchType: 'exact' | 'fuzzy' | 'regex' // 匹配方式：完全匹配 | 模糊匹配 | 正则匹配
}

// 重复数据统计规则配置项类型
export interface DuplicateStatisticsRule {
  id: string
  createTime: string
  reportAType: 'business' | 'temporary' // 报表A类型：业务表 | 临时表
  reportAFields: string[] // 报表A参与统计字段（多选）
  reportBType: 'business' | 'temporary' // 报表B类型：业务表 | 临时表
  reportBFields: string[] // 报表B参与统计字段（多选）
  outputIndicators: string[] // 输出指标（多选）
  matchType: 'exact' | 'fuzzy' | 'regex' // 匹配方式：完全匹配 | 模糊匹配 | 正则匹配
  outputFormats: string[] // 输出格式（多选）
}

// 对比配置规则配置项类型
export interface CompareConfigRule {
  id: string
  createTime: string
  reportType: 'business' | 'temporary' // 报表类型：业务表 | 临时表
  matchType: 'exact' | 'fuzzy' | 'regex' // 匹配方式：完全匹配 | 模糊匹配 | 正则匹配
  thresholdOperator: 'gt' | 'lt' | 'gte' | 'lte' | 'eq' // 阈值操作符：大于 | 小于 | 不大于 | 不小于 | 等于
  thresholdValue: number // 阈值数值
  relatedFields: string[] // 字段关联表（多选）
}

// 精准计算配置规则配置项类型
export interface PreciseCalcConfigRule {
  id: string
  createTime: string
  calcRuleType: 'direct_assign' | 'formula_calc' | 'condition_calc' | 'aggregate_calc' // 计算规则类型：直接赋值 | 公式计算 | 条件计算 | 聚合计算
  calcRule: string // 计算规则
  thresholdOperator: 'gt' | 'lt' | 'gte' | 'lte' | 'eq' // 阈值操作符：大于 | 小于 | 不大于 | 不小于 | 等于
  thresholdValue: number // 阈值数值
  reportAType: 'business' | 'temporary' // 报表A类型：业务表 | 临时表
  reportBType: 'business' | 'temporary' // 报表B类型：业务表 | 临时表
  reportAField: string // 报表A关联字段
  reportBField: string // 报表B关联字段
}

// 数据校验设置规则配置项类型
export interface DataValidationSettingRule {
  id: string
  createTime: string
  validationField: string // 进行校验字段
  validationType: string // 校验类型
  errorReminder: 'bold' | 'highlight' // 错误提醒：加粗 | 高亮
  validationResult: 'xls' | 'pdf' // 校验结果：xls | pdf
  validationFrequency: 'daily' | 'weekly' | 'monthly' // 校验频率：每日 | 每周 | 每月
  validationTemplate: 'finance_tax' | 'medical_insurance' | 'agriculture' // 校验模板：财税类 | 医保类 | 农业类
  ruleDetails: string // 规则详情
}

// 筛选条件类型
export interface FilterCondition {
  id: string
  logicOperator: 'and' | 'or' // 逻辑条件：且 | 或
  filterRange: string // 筛选范围
}

// 标识业务表规则配置项类型
export interface BusinessTableIdentificationRule {
  id: string
  createTime: string
  participatingField: string // 参与的字段（单选）
  ruleDescription: string // 规则描述
  filterConditions: FilterCondition[] // 筛选条件列表
}

// 临时表转业务表规则配置项类型
export interface TempTableToBusinessRule {
  id: string
  createTime: string
  participatingField: string // 参与的字段（单选）
  ruleDescription: string // 规则描述
  filterConditions: FilterCondition[] // 筛选条件列表
}

// 数据可视化设置规则配置项类型
export interface DataVisualizationSettingRule {
  id: string
  createTime: string
  chartType: 'pie' | 'line' | 'bar' // 图表类型：饼图 | 折线图 | 柱状图
  chartTitle: string // 图表标题
  colorScheme: string // 颜色组合
  fontSize: number // 字体大小
  fontFamily: string // 字体类型
  // 表设置
  tableSettings: {
    clickDisplay: boolean // 点击显示功能
    enabled: boolean // 开启/关闭
  }
  // 分享设置
  shareSettings: {
    exportEnabled: boolean // 导出
    linkEnabled: boolean // 链接
  }
  sharePermissions: string // 分享权限
  validityPeriod: number // 有效期限（天）
  exportFormat: 'xls' | 'pdf' // 导出格式
  // 轴设置（折线图和柱状图）
  axisSettings?: {
    xAxisField: string // X轴字段名
    yAxisField: string // Y轴字段名
    showDataLabels: boolean // 显示数据标签
    showGridLines: boolean // 显示网格线
    connectionStyle?: string // 连接样式（仅折线图）
  }
}

// 重复数据设置规则配置项类型
export interface DuplicateDataSettingRule {
  id: string
  createTime: string

  // 报表类型
  reportType: 'business' | 'temporary'

  // 1. 重复数据筛选设置
  filterFields: string[] // 筛选字段选择（多选）
  ruleConfigType: 'complete_match' | 'custom' // 规则配置类型：完全匹配 | 自定义
  weightThreshold: number // 重复权重值（0-100）

  // 2. 重复数据分类设置
  classificationMethod: 'date' | 'custom' // 分类依据选择：日期 | 自定义
  customClassificationRule: string // 设置条件式分类

  // 3. 重复数据排序设置
  sortField: string // 选择排序字段
  sortOrder: 'asc' | 'desc' // 排序顺序：升序 | 降序

  // 4. 重复数据展示规则设置
  displayFields: string[] // 展示字段选择（多选）
  filterConditions: string[] // 过滤条件选择（多选）
  viewType: 'pie' | 'bar' // 视图选择：饼状图 | 柱状图

  // 5. 重复审核规则设置
  colorSelection: {
    red: boolean // 红色选择
    blue: boolean // 蓝色选择
  }
  regionPercentage: number // 重复审核区间分分（百分比）

  // 6. 重复数据导出设置
  exportFormat: 'xls' | 'json' | 'pdf' // 导出格式选择
  exportFileSelection: string // 导出文件名选择

  // 7. 重复数据打印设置
  printMargin: number // 边距设置
  pageSize: string // 页面/页面大小
  paperOrientation: 'portrait' | 'landscape' // 纸张方向：纵向 | 横向

  // 8. 重复数据分享设置
  shareChannels: {
    email: boolean // 邮件
    link: boolean // 链接
    browser: boolean // 浏览器
    download: boolean // 下载
    edit: boolean // 编辑
  }
  sharePermissions: string // 分享权限
  validPeriod: string // 有效期

  // 9. 重复数据处理设置
  processingConditions: string[] // 执行条件（多选）
  processingRule: string // 则生成
  processingTemplate: string // 处理设置模板

  // 10. 重复数据修复进度跟踪设置
  progressUpdateFrequency: string // 进度更新新频率
  progressNotificationMethod: 'popup' | 'silent' // 进度通知方式：弹窗 | 表格

  // 11. 重复数据修复结果跟踪设置
  validationSelection: string // 验证标准选择
  validationOptions: {
    linkValidation: boolean // 链接日志
    performanceAnalysis: boolean // 性能分析
    basicInfo: boolean // 基本信息
  }
  validationPeriodSetting: string // 验证期间设置
}

// 重复数据设置规则表单
export interface DuplicateDataSettingRuleForm {
  reportType: 'business' | 'temporary'
  filterFields: string[]
  ruleConfigType: 'complete_match' | 'custom'
  weightThreshold: number
  classificationMethod: 'date' | 'custom'
  customClassificationRule: string
  sortField: string
  sortOrder: 'asc' | 'desc'
  displayFields: string[]
  filterConditions: string[]
  viewType: 'pie' | 'bar'
  colorSelection: {
    red: boolean
    blue: boolean
  }
  regionPercentage: number
  exportFormat: 'xls' | 'json' | 'pdf'
  exportFileSelection: string
  printMargin: number
  pageSize: string
  paperOrientation: 'portrait' | 'landscape'
  shareChannels: {
    email: boolean
    link: boolean
    browser: boolean
    download: boolean
    edit: boolean
  }
  sharePermissions: string
  validPeriod: string
  processingConditions: string[]
  processingRule: string
  processingTemplate: string
  progressUpdateFrequency: string
  progressNotificationMethod: 'popup' | 'silent'
  validationSelection: string
  validationOptions: {
    linkValidation: boolean
    performanceAnalysis: boolean
    basicInfo: boolean
  }
  validationPeriodSetting: string
}

// 表格列配置
export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
}

// 分页配置
export interface PaginationConfig {
  page: number
  size: number
  total: number
}

// 重复判断查询表单
export interface DuplicateSearchForm {
  reportAType: string
  reportAField: string
  reportBType: string
  reportBField: string
  matchType: string
}

// 重复判断规则表单
export interface DuplicateRuleForm {
  reportAType: 'business' | 'temporary'
  reportAField: string
  reportBType: 'business' | 'temporary'
  reportBField: string
  matchType: 'exact' | 'fuzzy' | 'regex'
}

// 对比配置规则表单
export interface CompareConfigRuleForm {
  reportType: 'business' | 'temporary'
  matchType: 'exact' | 'fuzzy' | 'regex'
  thresholdOperator: 'gt' | 'lt' | 'gte' | 'lte' | 'eq'
  thresholdValue: number
  relatedFields: string[]
}

// 精准计算配置规则表单
export interface PreciseCalcConfigRuleForm {
  calcRuleType: 'direct_assign' | 'formula_calc' | 'condition_calc' | 'aggregate_calc'
  calcRule: string
  thresholdOperator: 'gt' | 'lt' | 'gte' | 'lte' | 'eq'
  thresholdValue: number
  reportAType: 'business' | 'temporary'
  reportBType: 'business' | 'temporary'
  reportAField: string
  reportBField: string
}

// 数据校验设置规则表单
export interface DataValidationSettingRuleForm {
  validationField: string
  validationType: string
  errorReminder: 'bold' | 'highlight'
  validationResult: 'xls' | 'pdf'
  validationFrequency: 'daily' | 'weekly' | 'monthly'
  validationTemplate: 'finance_tax' | 'medical_insurance' | 'agriculture'
  ruleDetails: string
}

// 标识业务表规则表单
export interface BusinessTableIdentificationRuleForm {
  participatingField: string
  ruleDescription: string
  filterConditions: FilterCondition[]
}

// 临时表转业务表规则表单
export interface TempTableToBusinessRuleForm {
  participatingField: string
  ruleDescription: string
  filterConditions: FilterCondition[]
}

// 数据可视化设置规则表单
export interface DataVisualizationSettingRuleForm {
  chartType: 'pie' | 'line' | 'bar'
  chartTitle: string
  colorScheme: string
  fontSize: number
  fontFamily: string
  tableSettings: {
    clickDisplay: boolean
    enabled: boolean
  }
  shareSettings: {
    exportEnabled: boolean
    linkEnabled: boolean
  }
  sharePermissions: string
  validityPeriod: number
  exportFormat: 'xls' | 'pdf'
  axisSettings?: {
    xAxisField: string
    yAxisField: string
    showDataLabels: boolean
    showGridLines: boolean
    connectionStyle?: string
  }
}

// 表单验证规则
export interface FormRules {
  [key: string]: Array<{
    required?: boolean
    message: string
    trigger: string
    min?: number
    max?: number
  }>
}

// 操作按钮配置
export interface ActionButton {
  label: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  code: string
  icon?: string
  popconfirm?: string
}

// 本地存储键名
export const STORAGE_KEYS = {
  DUPLICATE_RULE_LIST: 'duplicate_rule_list',
  DUPLICATE_RULE_PAGINATION: 'duplicate_rule_pagination',
  DUPLICATE_RULE_SEARCH: 'duplicate_rule_search',
  COMPARE_CONFIG_LIST: 'compare_config_list',
  COMPARE_CONFIG_PAGINATION: 'compare_config_pagination',
  PRECISE_CALC_CONFIG_LIST: 'precise_calc_config_list',
  PRECISE_CALC_CONFIG_PAGINATION: 'precise_calc_config_pagination',
  DATA_VALIDATION_SETTING_LIST: 'data_validation_setting_list',
  DATA_VALIDATION_SETTING_PAGINATION: 'data_validation_setting_pagination',
  BUSINESS_TABLE_IDENTIFICATION_LIST: 'business_table_identification_list',
  BUSINESS_TABLE_IDENTIFICATION_PAGINATION: 'business_table_identification_pagination',
  TEMP_TABLE_TO_BUSINESS_LIST: 'temp_table_to_business_list',
  TEMP_TABLE_TO_BUSINESS_PAGINATION: 'temp_table_to_business_pagination',
  DATA_VISUALIZATION_SETTING_LIST: 'data_visualization_setting_list',
  DATA_VISUALIZATION_SETTING_PAGINATION: 'data_visualization_setting_pagination',
  DUPLICATE_DATA_SETTING_LIST: 'duplicate_data_setting_list',
  DUPLICATE_DATA_SETTING_PAGINATION: 'duplicate_data_setting_pagination',
  DATA_CONSISTENCY_CHECK_LIST: 'data_consistency_check_list',
  DATA_CONSISTENCY_CHECK_PAGINATION: 'data_consistency_check_pagination',
  EXCEPTION_DATA_PROCESSING_LIST: 'exception_data_processing_list',
  EXCEPTION_DATA_PROCESSING_PAGINATION: 'exception_data_processing_pagination'
} as const

// 报表类型选项
export const REPORT_TYPE_OPTIONS = [
  { label: '业务表', value: 'business' },
  { label: '临时表', value: 'temporary' }
] as const

// 匹配方式选项
export const MATCH_TYPE_OPTIONS = [
  { label: '完全匹配', value: 'exact' },
  { label: '模糊匹配', value: 'fuzzy' },
  { label: '正则匹配', value: 'regex' }
] as const

// 阈值操作符选项
export const THRESHOLD_OPERATOR_OPTIONS = [
  { label: '大于', value: 'gt' },
  { label: '小于', value: 'lt' },
  { label: '不大于', value: 'lte' },
  { label: '不小于', value: 'gte' },
  { label: '等于', value: 'eq' }
] as const

// 预设字段选项
export const PRESET_FIELD_OPTIONS = [
  { label: '年龄', value: 'age' },
  { label: '性别', value: 'gender' },
  { label: '户籍', value: 'household' },
  { label: '地址', value: 'address' },
  { label: '电话', value: 'phone' },
  { label: '民族', value: 'ethnicity' }
] as const

// 计算规则类型选项
export const CALC_RULE_TYPE_OPTIONS = [
  { label: '直接赋值', value: 'direct_assign' },
  { label: '公式计算', value: 'formula_calc' },
  { label: '条件计算', value: 'condition_calc' },
  { label: '聚合计算', value: 'aggregate_calc' }
] as const

// 校验字段选项
export const VALIDATION_FIELD_OPTIONS = [
  { label: '字段A', value: 'field_a' },
  { label: '字段B', value: 'field_b' },
  { label: '字段C', value: 'field_c' },
  { label: '字段D', value: 'field_d' },
  { label: '字段E', value: 'field_e' },
  { label: '字段F', value: 'field_f' }
] as const

// 错误提醒选项
export const ERROR_REMINDER_OPTIONS = [
  { label: '加粗', value: 'bold' },
  { label: '高亮', value: 'highlight' }
] as const

// 校验结果选项
export const VALIDATION_RESULT_OPTIONS = [
  { label: 'XLS', value: 'xls' },
  { label: 'PDF', value: 'pdf' }
] as const

// 校验频率选项
export const VALIDATION_FREQUENCY_OPTIONS = [
  { label: '每日', value: 'daily' },
  { label: '每周', value: 'weekly' },
  { label: '每月', value: 'monthly' }
] as const

// 校验模板选项
export const VALIDATION_TEMPLATE_OPTIONS = [
  { label: '财税类', value: 'finance_tax' },
  { label: '医保类', value: 'medical_insurance' },
  { label: '农业类', value: 'agriculture' }
] as const

// 逻辑条件选项
export const LOGIC_OPERATOR_OPTIONS = [
  { label: '且', value: 'and' },
  { label: '或', value: 'or' }
] as const

// 字段选项（模拟数据）
export const FIELD_OPTIONS = [
  { label: '业务表', value: 'business' },
  { label: '临时表', value: 'temporary' },
  { label: '电话', value: 'phone' },
  { label: '手机', value: 'mobile' },
  { label: '身份证', value: 'idcard' },
  { label: '姓名', value: 'name' },
  { label: '地址', value: 'address' },
  { label: '邮箱', value: 'email' }
] as const

// 图表类型选项
export const CHART_TYPE_OPTIONS = [
  { label: '饼图', value: 'pie' },
  { label: '折线图', value: 'line' },
  { label: '柱状图', value: 'bar' }
] as const

// 颜色组合选项
export const COLOR_SCHEME_OPTIONS = [
  { label: '蓝色组合', value: 'blue', colors: ['#409EFF', '#67C23A', '#E6A23C'] },
  { label: '绿色组合', value: 'green', colors: ['#67C23A', '#409EFF', '#E6A23C'] },
  { label: '橙色组合', value: 'orange', colors: ['#E6A23C', '#409EFF', '#67C23A'] },
  { label: '红色组合', value: 'red', colors: ['#F56C6C', '#409EFF', '#67C23A'] }
] as const

// 字体选项
export const FONT_FAMILY_OPTIONS = [
  { label: '微软雅黑', value: 'Microsoft YaHei' },
  { label: '宋体', value: 'SimSun' },
  { label: '黑体', value: 'SimHei' },
  { label: 'Arial', value: 'Arial' }
] as const

// 分享权限选项
export const SHARE_PERMISSION_OPTIONS = [
  { label: '公开', value: 'public' },
  { label: '仅查看', value: 'view_only' },
  { label: '编辑权限', value: 'edit' }
] as const

// 连接样式选项（折线图）
export const CONNECTION_STYLE_OPTIONS = [
  { label: '实线', value: 'solid' },
  { label: '虚线', value: 'dashed' },
  { label: '点线', value: 'dotted' }
] as const

// 轴字段选项
export const AXIS_FIELD_OPTIONS = [
  { label: '时间', value: 'time' },
  { label: '数量', value: 'quantity' },
  { label: '金额', value: 'amount' },
  { label: '百分比', value: 'percentage' }
] as const

// 重复数据设置相关选项

// 规则配置类型选项
export const RULE_CONFIG_TYPE_OPTIONS = [
  { label: '完全匹配', value: 'complete_match' },
  { label: '自定义', value: 'custom' }
] as const

// 分类方法选项
export const CLASSIFICATION_METHOD_OPTIONS = [
  { label: '日期', value: 'date' },
  { label: '自定义', value: 'custom' }
] as const

// 排序顺序选项
export const SORT_ORDER_OPTIONS = [
  { label: '升序', value: 'asc' },
  { label: '降序', value: 'desc' }
] as const

// 视图类型选项
export const VIEW_TYPE_OPTIONS = [
  { label: '饼状图', value: 'pie' },
  { label: '柱状图', value: 'bar' }
] as const

// 导出格式选项（重复数据设置）
export const DUPLICATE_EXPORT_FORMAT_OPTIONS = [
  { label: 'XLS', value: 'xls' },
  { label: 'JSON', value: 'json' },
  { label: 'PDF', value: 'pdf' }
] as const

// 页面大小选项
export const PAGE_SIZE_OPTIONS = [
  { label: 'A4', value: 'A4' },
  { label: 'A3', value: 'A3' },
  { label: 'Letter', value: 'Letter' }
] as const

// 纸张方向选项
export const PAPER_ORIENTATION_OPTIONS = [
  { label: '纵向', value: 'portrait' },
  { label: '横向', value: 'landscape' }
] as const

// 进度通知方式选项
export const PROGRESS_NOTIFICATION_OPTIONS = [
  { label: '弹窗', value: 'popup' },
  { label: '表格', value: 'silent' }
] as const

// 进度更新频率选项
export const PROGRESS_UPDATE_FREQUENCY_OPTIONS = [
  { label: '每小时', value: 'hourly' },
  { label: '每日', value: 'daily' },
  { label: '每周', value: 'weekly' }
] as const

// 验证标准选项
export const VALIDATION_STANDARD_OPTIONS = [
  { label: '业务表', value: 'business' },
  { label: '临时表', value: 'temporary' },
  { label: '综合', value: 'comprehensive' }
] as const

// 验证期间选项
export const VALIDATION_PERIOD_OPTIONS = [
  { label: '1小时', value: '1h' },
  { label: '1天', value: '1d' },
  { label: '1周', value: '1w' },
  { label: '1月', value: '1m' }
] as const

// 数据一致性、完整性、兼容性检查相关选项
export const DATA_SOURCE_OPTIONS = [
  { label: '临时表', value: 'temp_table' },
  { label: '业务表', value: 'business_table' }
]

export const CHECK_LEVEL_OPTIONS = [
  { label: '字段一', value: 'field_1' },
  { label: '字段二', value: 'field_2' },
  { label: '字段三', value: 'field_3' }
]

export const FIELD_TYPE_OPTIONS = [
  { label: '字符串', value: 'string' },
  { label: '浮点数', value: 'float' },
  { label: '整数', value: 'integer' }
]

export const OS_VERSION_OPTIONS = [
  { label: 'windows 8', value: 'windows_8' },
  { label: 'windows 10', value: 'windows_10' },
  { label: 'windows 11', value: 'windows_11' }
]

export const BROWSER_VERSION_OPTIONS = [
  { label: '2.3', value: '2.3' },
  { label: '2.4', value: '2.4' },
  { label: '2.5', value: '2.5' }
]

export const DATA_FORMAT_OPTIONS = [
  { label: 'json', value: 'json' },
  { label: 'xml', value: 'xml' },
  { label: 'csv', value: 'csv' }
]

// 异常数据处理相关选项
export const EXCEPTION_DATA_SOURCE_OPTIONS = [
  { label: '临时表', value: 'temp_table' },
  { label: '业务表', value: 'business_table' }
]

export const DETECTION_FIELD_OPTIONS = [
  { label: '字段一', value: 'field_1' },
  { label: '字段二', value: 'field_2' },
  { label: '字段三', value: 'field_3' }
]

export const EXCEPTION_TYPE_OPTIONS = [
  { label: '数值异常', value: 'numeric_exception' },
  { label: '格式异常', value: 'format_exception' }
]

export const AUTO_CORRECTION_OPTIONS = [
  { label: '启用', value: 'enabled' },
  { label: '禁用', value: 'disabled' }
]

export const TRIGGER_CONDITION_OPTIONS = [
  { label: '立即触发', value: 'immediate' },
  { label: '批量触发', value: 'batch' },
  { label: '定时触发', value: 'scheduled' }
]

export const EXECUTION_ACTION_OPTIONS = [
  { label: '删除记录', value: 'delete_record' },
  { label: '标记异常', value: 'mark_exception' },
  { label: '自动修复', value: 'auto_repair' },
  { label: '发送通知', value: 'send_notification' }
]

// 数据一致性检查配置项
export interface DataConsistencyCheckRule {
  dataSource: string // 选择数据来源
  checkLevel: string // 选择检查字段
  fieldFormat: string // 定义字段格式标准
  toleranceRange: number // 可接受的误差范围
}

// 数据完整性检查配置项
export interface DataIntegrityCheckRule {
  dataSource: string // 选择数据来源
  checkLevel: string // 选择必填字段
  fieldType: string // 字段类型限制
  dataRange: {
    min: number // 数据界限范围最小值
    max: number // 数据界限范围最大值
  }
}

// 数据兼容性检查配置项
export interface DataCompatibilityCheckRule {
  dataSource: string // 选择数据来源
  osVersion: string // 操作系统版本
  browserVersion: string // 浏览器版本
  apiPort: string // API接口
  dataFormat: string // 数据文件格式
}

// 数据一致性、完整性、兼容性检查规则配置
export interface DataConsistencyCheckConfig {
  id: string
  createTime: string

  // 数据一致性检查配置列表
  consistencyRules: DataConsistencyCheckRule[]

  // 数据完整性检查配置列表
  integrityRules: DataIntegrityCheckRule[]

  // 数据兼容性检查配置列表
  compatibilityRules: DataCompatibilityCheckRule[]
}

// 数据一致性、完整性、兼容性检查表单
export interface DataConsistencyCheckForm {
  consistencyRules: DataConsistencyCheckRule[]
  integrityRules: DataIntegrityCheckRule[]
  compatibilityRules: DataCompatibilityCheckRule[]
}

// 异常数据处理相关接口
export interface ExceptionDefinitionRule {
  dataSource: string
  detectionFields: string[]
  exceptionType: string
  exceptionDescription: string
}

export interface ProcessingLogicRule {
  autoCorrection: string
  triggerCondition: string
  triggerDescription: string
  executionAction: string
  actionDescription: string
}

export interface NotificationReceiver {
  id: string
  name: string
  department: string
  role: string
}

export interface ExceptionDataProcessingConfig {
  id: string
  createTime: string
  exceptionDefinitions: ExceptionDefinitionRule[]
  processingLogics: ProcessingLogicRule[]
  availableReceivers: NotificationReceiver[]
  selectedReceivers: NotificationReceiver[]
}

export interface ExceptionDataProcessingForm {
  exceptionDefinitions: ExceptionDefinitionRule[]
  processingLogics: ProcessingLogicRule[]
  availableReceivers: NotificationReceiver[]
  selectedReceivers: NotificationReceiver[]
}
