import type { DuplicateRule, NavigationItem, CompareConfigRule, PreciseCalcConfigRule, DataValidationSettingRule, BusinessTableIdentificationRule, TempTableToBusinessRule, FilterCondition, DataVisualizationSettingRule, DuplicateDataSettingRule, DataConsistencyCheckConfig, ExceptionDataProcessingConfig, NotificationReceiver } from './types'

// 生成模拟重复判断规则数据
export const generateMockDuplicateRuleData = (): DuplicateRule[] => {
  const mockData: DuplicateRule[] = []
  const reportTypes: ('business' | 'temporary')[] = ['business', 'temporary']
  const matchTypes: ('exact' | 'fuzzy' | 'regex')[] = ['exact', 'fuzzy', 'regex']
  const fields = ['业务表', '临时表', '电话', '手机', '身份证', '姓名', '地址', '邮箱']

  for (let i = 1; i <= 25; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

    mockData.push({
      id: `duplicate_rule_${i.toString().padStart(3, '0')}`,
      createTime: createTime.toISOString().replace('T', ' ').split('.')[0],
      reportAType: reportTypes[Math.floor(Math.random() * reportTypes.length)],
      reportAField: fields[Math.floor(Math.random() * fields.length)],
      reportBType: reportTypes[Math.floor(Math.random() * reportTypes.length)],
      reportBField: fields[Math.floor(Math.random() * fields.length)],
      matchType: matchTypes[Math.floor(Math.random() * matchTypes.length)]
    })
  }

  return mockData
}

// 左侧导航菜单数据
export const navigationMenuData: NavigationItem[] = [
  {
    id: 'duplicate_judgment',
    label: '重复判断',
    icon: 'Document',
    path: '/duplicate-judgment'
  },
  {
    id: 'duplicate_statistics',
    label: '重复数据统计',
    icon: 'DataAnalysis',
    path: '/duplicate-statistics'
  },
  {
    id: 'exclude_rules',
    label: '排除规则',
    icon: 'Filter',
    children: [
      {
        id: 'business_table_identification',
        label: '标识业务表',
        path: '/business-table-identification'
      },
      {
        id: 'temp_table_to_business',
        label: '临时表转业务表',
        path: '/temp-table-to-business'
      }
    ]
  },
  {
    id: 'compare_config',
    label: '比对配置',
    icon: 'Connection',
    path: '/compare-config'
  },
  {
    id: 'precise_calc_config',
    label: '精准计算配置',
    icon: 'Operation',
    path: '/precise-calc-config'
  },
  {
    id: 'data_consistency_check',
    label: '数据一致性、完整性、兼容性检查',
    icon: 'Check',
    path: '/data-consistency-check'
  },
  {
    id: 'exception_data_handle',
    label: '异常数据处理',
    icon: 'Warning',
    path: '/exception-data-handle'
  },
  {
    id: 'data_validation_setting',
    label: '数据校验设置',
    icon: 'Setting',
    path: '/data-validation-setting'
  },
  {
    id: 'duplicate_data_setting',
    label: '重复数据设置',
    icon: 'CopyDocument',
    path: '/duplicate-data-setting'
  },
  {
    id: 'data_visualization_setting',
    label: '数据可视化设置',
    icon: 'TrendCharts',
    path: '/data-visualization-setting'
  }
]

// 生成模拟对比配置规则数据
export const generateMockCompareConfigData = (): CompareConfigRule[] => {
  const mockData: CompareConfigRule[] = []
  const reportTypes: ('business' | 'temporary')[] = ['business', 'temporary']
  const matchTypes: ('exact' | 'fuzzy' | 'regex')[] = ['exact', 'fuzzy', 'regex']
  const thresholdOperators: ('gt' | 'lt' | 'gte' | 'lte' | 'eq')[] = ['gt', 'lt', 'gte', 'lte', 'eq']
  const fields = ['年龄', '性别', '户籍', '地址', '电话', '民族']

  // 生成随机字段组合的辅助函数，确保至少有一个字段
  const getRandomFields = (min: number = 1, max: number = 3): string[] => {
    const count = Math.floor(Math.random() * (max - min + 1)) + min
    const shuffled = [...fields].sort(() => 0.5 - Math.random())
    const selectedFields = shuffled.slice(0, count)
    // 确保至少有一个字段
    return selectedFields.length > 0 ? selectedFields : [fields[0]]
  }

  for (let i = 1; i <= 25; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

    // 确保业务表和临时表的合理分布（大约60%业务表，40%临时表）
    const reportType = i <= 15 ? 'business' : (i <= 20 ? 'temporary' : reportTypes[Math.floor(Math.random() * reportTypes.length)])

    mockData.push({
      id: `compare_config_${i.toString().padStart(3, '0')}`,
      createTime: createTime.toISOString().replace('T', ' ').split('.')[0],
      reportType: reportType,
      matchType: matchTypes[Math.floor(Math.random() * matchTypes.length)],
      thresholdOperator: thresholdOperators[Math.floor(Math.random() * thresholdOperators.length)],
      thresholdValue: Math.floor(Math.random() * 100) + 1,
      relatedFields: getRandomFields(1, 3)
    })
  }

  return mockData
}

// 生成模拟精准计算配置规则数据
export const generateMockPreciseCalcConfigData = (): PreciseCalcConfigRule[] => {
  const mockData: PreciseCalcConfigRule[] = []
  const reportTypes: ('business' | 'temporary')[] = ['business', 'temporary']
  const calcRuleTypes: ('direct_assign' | 'formula_calc' | 'condition_calc' | 'aggregate_calc')[] = ['direct_assign', 'formula_calc', 'condition_calc', 'aggregate_calc']
  const thresholdOperators: ('gt' | 'lt' | 'gte' | 'lte' | 'eq')[] = ['gt', 'lt', 'gte', 'lte', 'eq']
  const fields = ['年龄', '性别', '户籍', '地址', '电话', '民族']

  // 计算规则示例
  const calcRuleExamples = [
    'SUM(字段A) + SUM(字段B)',
    'AVG(年龄) * 1.2',
    'IF(性别="男", 1, 0)',
    'COUNT(DISTINCT 户籍)',
    'MAX(年龄) - MIN(年龄)',
    '字段A / 字段B * 100',
    'CASE WHEN 年龄 > 60 THEN "老年" ELSE "非老年" END',
    'ROUND(AVG(电话长度), 2)'
  ]

  for (let i = 1; i <= 28; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

    // 确保业务表和临时表的合理分布
    const reportAType = i <= 18 ? 'business' : (i <= 24 ? 'temporary' : reportTypes[Math.floor(Math.random() * reportTypes.length)])
    const reportBType = i <= 14 ? 'business' : (i <= 22 ? 'temporary' : reportTypes[Math.floor(Math.random() * reportTypes.length)])

    mockData.push({
      id: `precise_calc_config_${i.toString().padStart(3, '0')}`,
      createTime: createTime.toISOString().replace('T', ' ').split('.')[0],
      calcRuleType: calcRuleTypes[Math.floor(Math.random() * calcRuleTypes.length)],
      calcRule: calcRuleExamples[Math.floor(Math.random() * calcRuleExamples.length)],
      thresholdOperator: thresholdOperators[Math.floor(Math.random() * thresholdOperators.length)],
      thresholdValue: Math.floor(Math.random() * 100) + 1,
      reportAType: reportAType,
      reportBType: reportBType,
      reportAField: fields[Math.floor(Math.random() * fields.length)],
      reportBField: fields[Math.floor(Math.random() * fields.length)]
    })
  }

  return mockData
}

// 生成模拟数据校验设置规则数据
export const generateMockDataValidationSettingData = (): DataValidationSettingRule[] => {
  const mockData: DataValidationSettingRule[] = []
  const validationFields = ['字段A', '字段B', '字段C', '字段D', '字段E', '字段F']
  const validationTypes = ['非空检查', '格式校验', '长度限制', '数值范围']
  const errorReminders: ('bold' | 'highlight')[] = ['bold', 'highlight']
  const validationResults: ('xls' | 'pdf')[] = ['xls', 'pdf']
  const validationFrequencies: ('daily' | 'weekly' | 'monthly')[] = ['daily', 'weekly', 'monthly']
  const validationTemplates: ('finance_tax' | 'medical_insurance' | 'agriculture')[] = ['finance_tax', 'medical_insurance', 'agriculture']

  // 规则详情示例
  const ruleDetailsExamples = [
    '字段不能为空，必须填写有效值',
    '必须符合邮箱格式：<EMAIL>',
    '长度必须在6-20个字符之间',
    '数值必须在0-100之间',
    '必须为有效的身份证号码格式',
    '日期格式必须为YYYY-MM-DD',
    '手机号码必须为11位数字',
    '金额必须为正数且保留两位小数'
  ]

  for (let i = 1; i <= 26; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

    mockData.push({
      id: `data_validation_setting_${i.toString().padStart(3, '0')}`,
      createTime: createTime.toISOString().replace('T', ' ').split('.')[0],
      validationField: validationFields[Math.floor(Math.random() * validationFields.length)],
      validationType: validationTypes[Math.floor(Math.random() * validationTypes.length)],
      errorReminder: errorReminders[Math.floor(Math.random() * errorReminders.length)],
      validationResult: validationResults[Math.floor(Math.random() * validationResults.length)],
      validationFrequency: validationFrequencies[Math.floor(Math.random() * validationFrequencies.length)],
      validationTemplate: validationTemplates[Math.floor(Math.random() * validationTemplates.length)],
      ruleDetails: ruleDetailsExamples[Math.floor(Math.random() * ruleDetailsExamples.length)]
    })
  }

  return mockData
}

// 生成模拟标识业务表规则数据
export const generateMockBusinessTableIdentificationData = (): BusinessTableIdentificationRule[] => {
  const mockData: BusinessTableIdentificationRule[] = []
  const fields = ['年龄', '性别', '户籍', '地址', '电话', '民族', '姓名', '身份证', '邮箱', '职业', '收入', '学历']
  const logicOperators: ('and' | 'or')[] = ['and', 'or']

  // 生成随机字段的辅助函数
  const getRandomField = (): string => {
    return fields[Math.floor(Math.random() * fields.length)]
  }

  // 生成筛选条件
  const generateFilterConditions = (): FilterCondition[] => {
    const conditionCount = Math.floor(Math.random() * 3) + 1 // 1-3个条件
    const conditions: FilterCondition[] = []

    const filterRangeTemplates = [
      '大于 120',
      '小于 0',
      '等于 "男"',
      '包含 "北京"',
      '不为空',
      '长度大于 10',
      '在范围 [18, 65]',
      '匹配正则 /^1[3-9]\\d{9}$/'
    ]

    for (let i = 0; i < conditionCount; i++) {
      conditions.push({
        id: `condition_${Date.now()}_${i}`,
        logicOperator: logicOperators[Math.floor(Math.random() * logicOperators.length)],
        filterRange: filterRangeTemplates[Math.floor(Math.random() * filterRangeTemplates.length)]
      })
    }

    return conditions
  }

  // 规则描述模板
  const generateRuleDescription = (participatingField: string, filterConditions: FilterCondition[]): string => {
    const conditionStr = filterConditions.map(condition =>
      `字段${condition.filterRange}`
    ).join(` ${filterConditions[0]?.logicOperator === 'and' ? '且' : '或'} `)

    const timeRanges = [
      '时间范围在2024年1月1日至2024年12月31日',
      '时间范围在2023年6月1日至2024年5月31日',
      '时间范围在最近30天内',
      '时间范围在当前季度'
    ]

    const timeRange = timeRanges[Math.floor(Math.random() * timeRanges.length)]

    return `${conditionStr}，且${timeRange}，涉及字段：${participatingField}`
  }

  for (let i = 1; i <= 25; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    const participatingField = getRandomField()
    const filterConditions = generateFilterConditions()

    const newRule = {
      id: `business_table_identification_${i.toString().padStart(3, '0')}`,
      createTime: createTime.toISOString().replace('T', ' ').split('.')[0],
      participatingField: participatingField,
      ruleDescription: generateRuleDescription(participatingField, filterConditions),
      filterConditions: filterConditions
    }

    console.log(`生成数据 ${i}:`, newRule.participatingField) // 调试信息
    mockData.push(newRule)
  }

  return mockData
}

// 生成模拟临时表转业务表规则数据
export const generateMockTempTableToBusinessData = (): TempTableToBusinessRule[] => {
  const mockData: TempTableToBusinessRule[] = []
  const fields = ['年龄', '性别', '户籍', '地址', '电话', '民族', '姓名', '身份证', '邮箱', '职业', '收入', '学历']
  const logicOperators: ('and' | 'or')[] = ['and', 'or']

  // 生成随机字段的辅助函数
  const getRandomField = (): string => {
    return fields[Math.floor(Math.random() * fields.length)]
  }

  // 生成筛选条件
  const generateFilterConditions = (): FilterCondition[] => {
    const conditionCount = Math.floor(Math.random() * 3) + 1 // 1-3个条件
    const conditions: FilterCondition[] = []

    const filterRangeTemplates = [
      '大于 120',
      '小于 0',
      '等于 "男"',
      '包含 "北京"',
      '不为空',
      '长度大于 10',
      '在范围 [18, 65]',
      '匹配正则 /^1[3-9]\\d{9}$/'
    ]

    for (let i = 0; i < conditionCount; i++) {
      conditions.push({
        id: `condition_${Date.now()}_${i}`,
        logicOperator: logicOperators[Math.floor(Math.random() * logicOperators.length)],
        filterRange: filterRangeTemplates[Math.floor(Math.random() * filterRangeTemplates.length)]
      })
    }

    return conditions
  }

  // 规则描述模板
  const generateRuleDescription = (participatingField: string, filterConditions: FilterCondition[]): string => {
    const conditionStr = filterConditions.map(condition =>
      `字段${condition.filterRange}`
    ).join(` ${filterConditions[0]?.logicOperator === 'and' ? '且' : '或'} `)

    const timeRanges = [
      '时间范围在2024年1月1日至2024年12月31日',
      '时间范围在2023年6月1日至2024年5月31日',
      '时间范围在最近30天内',
      '时间范围在当前季度'
    ]

    const timeRange = timeRanges[Math.floor(Math.random() * timeRanges.length)]

    return `${conditionStr}，且${timeRange}，涉及字段：${participatingField}`
  }

  for (let i = 1; i <= 25; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    const participatingField = getRandomField()
    const filterConditions = generateFilterConditions()

    const newRule = {
      id: `temp_table_to_business_${i.toString().padStart(3, '0')}`,
      createTime: createTime.toISOString().replace('T', ' ').split('.')[0],
      participatingField: participatingField,
      ruleDescription: generateRuleDescription(participatingField, filterConditions),
      filterConditions: filterConditions
    }

    console.log(`生成临时表转业务表数据 ${i}:`, newRule.participatingField) // 调试信息
    mockData.push(newRule)
  }

  return mockData
}

// 生成模拟数据可视化设置规则数据
export const generateMockDataVisualizationSettingData = (): DataVisualizationSettingRule[] => {
  const mockData: DataVisualizationSettingRule[] = []
  const chartTypes: ('pie' | 'line' | 'bar')[] = ['pie', 'line', 'bar']
  const colorSchemes = ['blue', 'green', 'orange', 'red']
  const fontFamilies = ['Microsoft YaHei', 'SimSun', 'SimHei', 'Arial']
  const sharePermissions = ['public', 'view_only', 'edit']
  const exportFormats: ('xls' | 'pdf')[] = ['xls', 'pdf']
  const connectionStyles = ['solid', 'dashed', 'dotted']
  const axisFields = ['time', 'quantity', 'amount', 'percentage']

  // 图表标题模板
  const chartTitleTemplates = [
    '业务数据统计一览',
    '月度数据分析图',
    '年度业绩对比',
    '部门数据汇总',
    '用户行为分析',
    '销售趋势图表',
    '财务数据展示',
    '项目进度统计'
  ]

  for (let i = 1; i <= 25; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    const chartType = chartTypes[Math.floor(Math.random() * chartTypes.length)]

    const baseRule = {
      id: `data_visualization_setting_${i.toString().padStart(3, '0')}`,
      createTime: createTime.toISOString().replace('T', ' ').split('.')[0],
      chartType: chartType,
      chartTitle: chartTitleTemplates[Math.floor(Math.random() * chartTitleTemplates.length)],
      colorScheme: colorSchemes[Math.floor(Math.random() * colorSchemes.length)],
      fontSize: Math.floor(Math.random() * 6) + 12, // 12-18
      fontFamily: fontFamilies[Math.floor(Math.random() * fontFamilies.length)],
      tableSettings: {
        clickDisplay: Math.random() > 0.5,
        enabled: Math.random() > 0.3
      },
      shareSettings: {
        exportEnabled: Math.random() > 0.4,
        linkEnabled: Math.random() > 0.5
      },
      sharePermissions: sharePermissions[Math.floor(Math.random() * sharePermissions.length)],
      validityPeriod: Math.floor(Math.random() * 30) + 7, // 7-37天
      exportFormat: exportFormats[Math.floor(Math.random() * exportFormats.length)]
    }

    // 为折线图和柱状图添加轴设置
    if (chartType === 'line' || chartType === 'bar') {
      const axisSettings = {
        xAxisField: axisFields[Math.floor(Math.random() * axisFields.length)],
        yAxisField: axisFields[Math.floor(Math.random() * axisFields.length)],
        showDataLabels: Math.random() > 0.5,
        showGridLines: Math.random() > 0.4
      }

      // 为折线图添加连接样式
      if (chartType === 'line') {
        axisSettings.connectionStyle = connectionStyles[Math.floor(Math.random() * connectionStyles.length)]
      }

      mockData.push({
        ...baseRule,
        axisSettings
      })
    } else {
      mockData.push(baseRule)
    }
  }

  return mockData
}

// 生成模拟重复数据设置规则数据
export const generateMockDuplicateDataSettingData = (): DuplicateDataSettingRule[] => {
  const mockData: DuplicateDataSettingRule[] = []
  const ruleConfigTypes: ('complete_match' | 'custom')[] = ['complete_match', 'custom']
  const classificationMethods: ('date' | 'custom')[] = ['date', 'custom']
  const sortOrders: ('asc' | 'desc')[] = ['asc', 'desc']
  const viewTypes: ('pie' | 'bar')[] = ['pie', 'bar']
  const exportFormats: ('xls' | 'json' | 'pdf')[] = ['xls', 'json', 'pdf']
  const paperOrientations: ('portrait' | 'landscape')[] = ['portrait', 'landscape']
  const progressNotificationMethods: ('popup' | 'silent')[] = ['popup', 'silent']

  const fields = ['姓名', '身份证', '电话', '地址', '年龄', '性别']
  const filterConditions = ['条件A', '条件B', '条件C', '条件D']
  const processingConditions = ['执行条件1', '执行条件2', '执行条件3']
  const progressFrequencies = ['hourly', 'daily', 'weekly']
  const validationStandards = ['business', 'temporary', 'comprehensive']
  const validationPeriods = ['1h', '1d', '1w', '1m']
  const pageSizes = ['A4', 'A3', 'Letter']

  for (let i = 1; i <= 25; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

    // 随机选择字段
    const getRandomFields = (min: number = 1, max: number = 3): string[] => {
      const count = Math.floor(Math.random() * (max - min + 1)) + min
      const shuffled = [...fields].sort(() => 0.5 - Math.random())
      return shuffled.slice(0, count)
    }

    const getRandomConditions = (arr: string[], min: number = 1, max: number = 2): string[] => {
      const count = Math.floor(Math.random() * (max - min + 1)) + min
      const shuffled = [...arr].sort(() => 0.5 - Math.random())
      return shuffled.slice(0, count)
    }

    mockData.push({
      id: `duplicate_data_setting_${i.toString().padStart(3, '0')}`,
      createTime: createTime.toISOString().replace('T', ' ').split('.')[0],

      // 报表类型
      reportType: Math.random() > 0.5 ? 'business' : 'temporary',

      // 1. 重复数据筛选设置
      filterFields: getRandomFields(1, 3),
      ruleConfigType: ruleConfigTypes[Math.floor(Math.random() * ruleConfigTypes.length)],
      weightThreshold: Math.floor(Math.random() * 100),

      // 2. 重复数据分类设置
      classificationMethod: classificationMethods[Math.floor(Math.random() * classificationMethods.length)],
      customClassificationRule: `自定义分类规则${i}`,

      // 3. 重复数据排序设置
      sortField: fields[Math.floor(Math.random() * fields.length)],
      sortOrder: sortOrders[Math.floor(Math.random() * sortOrders.length)],

      // 4. 重复数据展示规则设置
      displayFields: getRandomFields(2, 4),
      filterConditions: getRandomConditions(filterConditions, 1, 2),
      viewType: viewTypes[Math.floor(Math.random() * viewTypes.length)],

      // 5. 重复审核规则设置
      colorSelection: {
        red: Math.random() > 0.5,
        blue: Math.random() > 0.5
      },
      regionPercentage: Math.floor(Math.random() * 100),

      // 6. 重复数据导出设置
      exportFormat: exportFormats[Math.floor(Math.random() * exportFormats.length)],
      exportFileSelection: `导出文件${i}`,

      // 7. 重复数据打印设置
      printMargin: Math.floor(Math.random() * 50) + 10,
      pageSize: pageSizes[Math.floor(Math.random() * pageSizes.length)],
      paperOrientation: paperOrientations[Math.floor(Math.random() * paperOrientations.length)],

      // 8. 重复数据分享设置
      shareChannels: {
        email: Math.random() > 0.5,
        link: Math.random() > 0.5,
        browser: Math.random() > 0.5,
        download: Math.random() > 0.5,
        edit: Math.random() > 0.5
      },
      sharePermissions: `权限设置${i}`,
      validPeriod: validationPeriods[Math.floor(Math.random() * validationPeriods.length)],

      // 9. 重复数据处理设置
      processingConditions: getRandomConditions(processingConditions, 1, 2),
      processingRule: `处理规则${i}`,
      processingTemplate: `处理模板${i}`,

      // 10. 重复数据修复进度跟踪设置
      progressUpdateFrequency: progressFrequencies[Math.floor(Math.random() * progressFrequencies.length)],
      progressNotificationMethod: progressNotificationMethods[Math.floor(Math.random() * progressNotificationMethods.length)],

      // 11. 重复数据修复结果跟踪设置
      validationSelection: validationStandards[Math.floor(Math.random() * validationStandards.length)],
      validationOptions: {
        linkValidation: Math.random() > 0.5,
        performanceAnalysis: Math.random() > 0.5,
        basicInfo: Math.random() > 0.5
      },
      validationPeriodSetting: validationPeriods[Math.floor(Math.random() * validationPeriods.length)]
    })
  }

  return mockData
}

// 生成数据一致性、完整性、兼容性检查模拟数据
export const generateMockDataConsistencyCheckData = (): DataConsistencyCheckConfig[] => {
  const mockData: DataConsistencyCheckConfig[] = []
  const dataSources = ['temp_table', 'business_table']
  const checkLevels = ['field_1', 'field_2', 'field_3']
  const fieldTypes = ['string', 'float', 'integer']
  const osVersions = ['windows_8', 'windows_10', 'windows_11']
  const browserVersions = ['2.3', '2.4', '2.5']
  const dataFormats = ['json', 'xml', 'csv']

  for (let i = 1; i <= 20; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

    // 生成1-3个一致性检查规则
    const consistencyRulesCount = Math.floor(Math.random() * 3) + 1
    const consistencyRules = []
    for (let j = 0; j < consistencyRulesCount; j++) {
      consistencyRules.push({
        dataSource: dataSources[Math.floor(Math.random() * dataSources.length)],
        checkLevel: checkLevels[Math.floor(Math.random() * checkLevels.length)],
        fieldFormat: `格式标准${j + 1}`,
        toleranceRange: Math.floor(Math.random() * 50) + 10
      })
    }

    // 生成1-3个完整性检查规则
    const integrityRulesCount = Math.floor(Math.random() * 3) + 1
    const integrityRules = []
    for (let j = 0; j < integrityRulesCount; j++) {
      const minValue = Math.floor(Math.random() * 100)
      const maxValue = minValue + Math.floor(Math.random() * 100) + 50
      integrityRules.push({
        dataSource: dataSources[Math.floor(Math.random() * dataSources.length)],
        checkLevel: checkLevels[Math.floor(Math.random() * checkLevels.length)],
        fieldType: fieldTypes[Math.floor(Math.random() * fieldTypes.length)],
        dataRange: {
          min: minValue,
          max: maxValue
        }
      })
    }

    // 生成1-3个兼容性检查规则
    const compatibilityRulesCount = Math.floor(Math.random() * 3) + 1
    const compatibilityRules = []
    for (let j = 0; j < compatibilityRulesCount; j++) {
      compatibilityRules.push({
        dataSource: dataSources[Math.floor(Math.random() * dataSources.length)],
        osVersion: osVersions[Math.floor(Math.random() * osVersions.length)],
        browserVersion: browserVersions[Math.floor(Math.random() * browserVersions.length)],
        apiPort: (3000 + Math.floor(Math.random() * 2000)).toString(),
        dataFormat: dataFormats[Math.floor(Math.random() * dataFormats.length)]
      })
    }

    mockData.push({
      id: `data_consistency_check_${i.toString().padStart(3, '0')}`,
      createTime: createTime.toISOString().replace('T', ' ').split('.')[0],
      consistencyRules,
      integrityRules,
      compatibilityRules
    })
  }

  return mockData
}

// 生成通知接收者模拟数据
export const generateMockNotificationReceivers = (): NotificationReceiver[] => {
  const departments = ['技术部', '运营部', '财务部', '人事部', '市场部']
  const roles = ['经理', '主管', '专员', '助理']
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十', '郑十一', '王十二']

  const receivers: NotificationReceiver[] = []

  for (let i = 0; i < 10; i++) {
    receivers.push({
      id: `receiver_${i + 1}`,
      name: names[i],
      department: departments[Math.floor(Math.random() * departments.length)],
      role: roles[Math.floor(Math.random() * roles.length)]
    })
  }

  return receivers
}

// 生成异常数据处理模拟数据
export const generateMockExceptionDataProcessingData = (): ExceptionDataProcessingConfig[] => {
  const mockData: ExceptionDataProcessingConfig[] = []
  const dataSources = ['temp_table', 'business_table']
  const detectionFields = ['field_1', 'field_2', 'field_3']
  const exceptionTypes = ['numeric_exception', 'format_exception']
  const autoCorrections = ['enabled', 'disabled']
  const triggerConditions = ['immediate', 'batch', 'scheduled']
  const executionActions = ['delete_record', 'mark_exception', 'auto_repair', 'send_notification']

  const allReceivers = generateMockNotificationReceivers()

  for (let i = 1; i <= 15; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

    // 生成1-3个异常定义规则
    const exceptionDefinitionsCount = Math.floor(Math.random() * 3) + 1
    const exceptionDefinitions = []
    for (let j = 0; j < exceptionDefinitionsCount; j++) {
      const fieldCount = Math.floor(Math.random() * 3) + 1
      const selectedFields = []
      for (let k = 0; k < fieldCount; k++) {
        const field = detectionFields[Math.floor(Math.random() * detectionFields.length)]
        if (!selectedFields.includes(field)) {
          selectedFields.push(field)
        }
      }

      exceptionDefinitions.push({
        dataSource: dataSources[Math.floor(Math.random() * dataSources.length)],
        detectionFields: selectedFields,
        exceptionType: exceptionTypes[Math.floor(Math.random() * exceptionTypes.length)],
        exceptionDescription: `异常描述${j + 1}：数据超出正常范围或格式不符合要求`
      })
    }

    // 生成1-3个处理逻辑规则
    const processingLogicsCount = Math.floor(Math.random() * 3) + 1
    const processingLogics = []
    for (let j = 0; j < processingLogicsCount; j++) {
      processingLogics.push({
        autoCorrection: autoCorrections[Math.floor(Math.random() * autoCorrections.length)],
        triggerCondition: triggerConditions[Math.floor(Math.random() * triggerConditions.length)],
        triggerDescription: `触发条件${j + 1}：当检测到异常数据时自动执行`,
        executionAction: executionActions[Math.floor(Math.random() * executionActions.length)],
        actionDescription: `执行操作${j + 1}：根据配置的规则进行相应处理`
      })
    }

    // 随机选择一些接收者作为已选择的
    const selectedCount = Math.floor(Math.random() * 4) + 1
    const selectedReceivers = allReceivers.slice(0, selectedCount)
    const availableReceivers = allReceivers.slice(selectedCount)

    mockData.push({
      id: `exception_data_processing_${i.toString().padStart(3, '0')}`,
      createTime: createTime.toISOString().replace('T', ' ').split('.')[0],
      exceptionDefinitions,
      processingLogics,
      availableReceivers,
      selectedReceivers
    })
  }

  return mockData
}

// 默认的模拟数据
export const defaultMockData = generateMockDuplicateRuleData()
