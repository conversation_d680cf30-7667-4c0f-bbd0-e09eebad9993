import type { DuplicateRule, PaginationConfig, DuplicateSearchForm, DuplicateStatisticsRule, CompareConfigRule, PreciseCalcConfigRule, DataValidationSettingRule, BusinessTableIdentificationRule, TempTableToBusinessRule, DataVisualizationSettingRule, DuplicateDataSettingRule, DataConsistencyCheckConfig, ExceptionDataProcessingConfig } from './types'
import { STORAGE_KEYS } from './types'
import { defaultMockData, generateMockCompareConfigData, generateMockPreciseCalcConfigData, generateMockDataValidationSettingData, generateMockBusinessTableIdentificationData, generateMockTempTableToBusinessData, generateMockDataVisualizationSettingData, generateMockDuplicateDataSettingData, generateMockDataConsistencyCheckData, generateMockExceptionDataProcessingData } from './mockData'

/**
 * 重复判断规则本地存储管理类
 */
export class DuplicateRuleStorage {

  /**
   * 获取重复判断规则列表
   */
  static getRuleList(): DuplicateRule[] {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.DUPLICATE_RULE_LIST)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，使用默认模拟数据并保存
      this.saveRuleList(defaultMockData)
      return defaultMockData
    } catch (error) {
      console.error('获取重复判断规则列表失败:', error)
      return defaultMockData
    }
  }

  /**
   * 保存重复判断规则列表
   */
  static saveRuleList(ruleList: DuplicateRule[]): void {
    try {
      localStorage.setItem(STORAGE_KEYS.DUPLICATE_RULE_LIST, JSON.stringify(ruleList))
    } catch (error) {
      console.error('保存重复判断规则列表失败:', error)
    }
  }

  /**
   * 添加重复判断规则
   */
  static addRule(rule: Omit<DuplicateRule, 'id' | 'createTime'>): DuplicateRule {
    const ruleList = this.getRuleList()
    const newRule: DuplicateRule = {
      ...rule,
      id: `duplicate_rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createTime: new Date().toISOString().replace('T', ' ').split('.')[0]
    }

    ruleList.unshift(newRule)
    this.saveRuleList(ruleList)
    return newRule
  }

  /**
   * 更新重复判断规则
   */
  static updateRule(id: string, updates: Partial<DuplicateRule>): boolean {
    try {
      const ruleList = this.getRuleList()
      const index = ruleList.findIndex(rule => rule.id === id)

      if (index !== -1) {
        ruleList[index] = {
          ...ruleList[index],
          ...updates
        }
        this.saveRuleList(ruleList)
        return true
      }
      return false
    } catch (error) {
      console.error('更新重复判断规则失败:', error)
      return false
    }
  }

  /**
   * 删除重复判断规则
   */
  static deleteRule(id: string): boolean {
    try {
      const ruleList = this.getRuleList()
      const filteredList = ruleList.filter(rule => rule.id !== id)

      if (filteredList.length !== ruleList.length) {
        this.saveRuleList(filteredList)
        return true
      }
      return false
    } catch (error) {
      console.error('删除重复判断规则失败:', error)
      return false
    }
  }

  /**
   * 根据ID获取重复判断规则
   */
  static getRuleById(id: string): DuplicateRule | null {
    try {
      const ruleList = this.getRuleList()
      return ruleList.find(rule => rule.id === id) || null
    } catch (error) {
      console.error('获取重复判断规则失败:', error)
      return null
    }
  }

  /**
   * 搜索重复判断规则
   */
  static searchRules(searchForm: DuplicateSearchForm): DuplicateRule[] {
    try {
      const ruleList = this.getRuleList()
      return ruleList.filter(rule => {
        const reportATypeMatch = !searchForm.reportAType || rule.reportAType === searchForm.reportAType
        const reportAFieldMatch = !searchForm.reportAField || rule.reportAField.includes(searchForm.reportAField)
        const reportBTypeMatch = !searchForm.reportBType || rule.reportBType === searchForm.reportBType
        const reportBFieldMatch = !searchForm.reportBField || rule.reportBField.includes(searchForm.reportBField)
        const matchTypeMatch = !searchForm.matchType || rule.matchType === searchForm.matchType

        return reportATypeMatch && reportAFieldMatch && reportBTypeMatch && reportBFieldMatch && matchTypeMatch
      })
    } catch (error) {
      console.error('搜索重复判断规则失败:', error)
      return []
    }
  }

  /**
   * 获取分页配置
   */
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.DUPLICATE_RULE_PAGINATION)
      if (data) {
        return JSON.parse(data)
      }
      return { page: 1, size: 10, total: 0 }
    } catch (error) {
      console.error('获取分页配置失败:', error)
      return { page: 1, size: 10, total: 0 }
    }
  }

  /**
   * 保存分页配置
   */
  static savePaginationConfig(pagination: PaginationConfig): void {
    try {
      localStorage.setItem(STORAGE_KEYS.DUPLICATE_RULE_PAGINATION, JSON.stringify(pagination))
    } catch (error) {
      console.error('保存分页配置失败:', error)
    }
  }

  /**
   * 获取搜索表单数据
   */
  static getSearchForm(): DuplicateSearchForm {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.DUPLICATE_RULE_SEARCH)
      if (data) {
        const parsed = JSON.parse(data)
        return { reportAType: '', reportAField: '', reportBType: '', reportBField: '', matchType: '', ...parsed }
      }
      return { reportAType: '', reportAField: '', reportBType: '', reportBField: '', matchType: '' }
    } catch (error) {
      console.error('获取搜索表单失败:', error)
      return { reportAType: '', reportAField: '', reportBType: '', reportBField: '', matchType: '' }
    }
  }

  /**
   * 保存搜索表单数据
   */
  static saveSearchForm(searchForm: DuplicateSearchForm): void {
    try {
      localStorage.setItem(STORAGE_KEYS.DUPLICATE_RULE_SEARCH, JSON.stringify(searchForm))
    } catch (error) {
      console.error('保存搜索表单失败:', error)
    }
  }

  /**
   * 清空所有数据
   */
  static clearAll(): void {
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key)
      })
    } catch (error) {
      console.error('清空数据失败:', error)
    }
  }
}

/**
 * 重复数据统计规则存储管理类
 */
export class DuplicateStatisticsStorage {
  private static readonly STORAGE_KEY = 'duplicate_statistics_rules'
  private static readonly PAGINATION_KEY = 'duplicate_statistics_pagination'

  // 获取重复数据统计规则列表
  static getStatisticsRuleList(): DuplicateStatisticsRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，返回默认模拟数据
      const defaultData = this.generateDefaultStatisticsData()
      this.saveStatisticsRuleList(defaultData)
      return defaultData
    } catch (error) {
      console.error('获取重复数据统计规则列表失败:', error)
      return []
    }
  }

  // 获取重复数据统计规则列表（不自动生成默认数据）
  static getStatisticsRuleListRaw(): DuplicateStatisticsRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return []
    } catch (error) {
      console.error('获取重复数据统计规则列表失败:', error)
      return []
    }
  }

  // 保存重复数据统计规则列表
  static saveStatisticsRuleList(rules: DuplicateStatisticsRule[]): boolean {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(rules))
      return true
    } catch (error) {
      console.error('保存重复数据统计规则列表失败:', error)
      return false
    }
  }

  // 添加重复数据统计规则
  static addStatisticsRule(rule: Omit<DuplicateStatisticsRule, 'id' | 'createTime'>): boolean {
    try {
      const rules = this.getStatisticsRuleList()
      const newRule: DuplicateStatisticsRule = {
        ...rule,
        id: `stats_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createTime: new Date().toLocaleString('zh-CN')
      }
      rules.unshift(newRule)
      return this.saveStatisticsRuleList(rules)
    } catch (error) {
      console.error('添加重复数据统计规则失败:', error)
      return false
    }
  }

  // 删除重复数据统计规则
  static deleteStatisticsRule(id: string): boolean {
    try {
      const rules = this.getStatisticsRuleList()
      const filteredRules = rules.filter(rule => rule.id !== id)
      return this.saveStatisticsRuleList(filteredRules)
    } catch (error) {
      console.error('删除重复数据统计规则失败:', error)
      return false
    }
  }

  // 根据ID获取重复数据统计规则
  static getStatisticsRuleById(id: string): DuplicateStatisticsRule | null {
    try {
      const rules = this.getStatisticsRuleList()
      return rules.find(rule => rule.id === id) || null
    } catch (error) {
      console.error('获取重复数据统计规则失败:', error)
      return null
    }
  }

  // 保存分页配置
  static savePaginationConfig(config: PaginationConfig): boolean {
    try {
      localStorage.setItem(this.PAGINATION_KEY, JSON.stringify(config))
      return true
    } catch (error) {
      console.error('保存分页配置失败:', error)
      return false
    }
  }

  // 获取分页配置
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(this.PAGINATION_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return { page: 1, pageSize: 10, total: 0 }
    } catch (error) {
      console.error('获取分页配置失败:', error)
      return { page: 1, pageSize: 10, total: 0 }
    }
  }

  // 生成默认的重复数据统计模拟数据
  private static generateDefaultStatisticsData(): DuplicateStatisticsRule[] {
    const reportTypes: Array<'business' | 'temporary'> = ['business', 'temporary']
    const fields = ['年龄', '电话', '民族', '姓名', '身份证', '地址', '时间']
    const matchTypes: Array<'exact' | 'fuzzy' | 'regex'> = ['exact', 'fuzzy', 'regex']
    const outputIndicators = ['total_duplicate_count', 'duplicate_ratio', 'max_duplicate_count']
    const outputFormats = ['.xls', '.pdf']

    const rules: DuplicateStatisticsRule[] = []

    for (let i = 1; i <= 25; i++) {
      const reportAType = reportTypes[Math.floor(Math.random() * reportTypes.length)]
      const reportBType = reportTypes[Math.floor(Math.random() * reportTypes.length)]
      const matchType = matchTypes[Math.floor(Math.random() * matchTypes.length)]

      // 随机选择2-4个字段
      const reportAFields = this.getRandomFields(fields, 2, 4)
      const reportBFields = this.getRandomFields(fields, 2, 4)

      // 随机选择1-3个输出指标
      const selectedIndicators = this.getRandomFields(outputIndicators, 1, 3)

      // 随机选择1-2个输出格式
      const selectedFormats = this.getRandomFields(outputFormats, 1, 2)

      const baseTime = new Date('2024-04-01')
      const randomTime = new Date(baseTime.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000)

      rules.push({
        id: `stats_${Date.now()}_${i}`,
        createTime: randomTime.toLocaleString('zh-CN'),
        reportAType,
        reportAFields,
        reportBType,
        reportBFields,
        outputIndicators: selectedIndicators,
        matchType,
        outputFormats: selectedFormats
      })
    }

    return rules.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
  }

  // 随机选择字段的辅助方法
  private static getRandomFields(fields: string[], min: number, max: number): string[] {
    const count = Math.floor(Math.random() * (max - min + 1)) + min
    const shuffled = [...fields].sort(() => 0.5 - Math.random())
    return shuffled.slice(0, count)
  }
}

/**
 * 对比配置规则存储管理类
 */
export class CompareConfigStorage {
  private static readonly STORAGE_KEY = 'compare_config_rules'
  private static readonly PAGINATION_KEY = 'compare_config_pagination'

  // 获取对比配置规则列表
  static getCompareConfigList(): CompareConfigRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，返回默认模拟数据
      const defaultData = this.generateDefaultCompareConfigData()
      this.saveCompareConfigList(defaultData)
      return defaultData
    } catch (error) {
      console.error('获取对比配置规则列表失败:', error)
      return []
    }
  }

  // 获取对比配置规则列表（不自动生成默认数据）
  static getCompareConfigListRaw(): CompareConfigRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return []
    } catch (error) {
      console.error('获取对比配置规则列表失败:', error)
      return []
    }
  }

  // 保存对比配置规则列表
  static saveCompareConfigList(rules: CompareConfigRule[]): boolean {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(rules))
      return true
    } catch (error) {
      console.error('保存对比配置规则列表失败:', error)
      return false
    }
  }

  // 添加对比配置规则
  static addCompareConfigRule(rule: Omit<CompareConfigRule, 'id' | 'createTime'>): CompareConfigRule {
    const ruleList = this.getCompareConfigList()
    const newRule: CompareConfigRule = {
      ...rule,
      id: `compare_config_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createTime: new Date().toISOString().replace('T', ' ').split('.')[0]
    }

    ruleList.unshift(newRule)
    this.saveCompareConfigList(ruleList)
    return newRule
  }

  // 更新对比配置规则
  static updateCompareConfigRule(id: string, updates: Partial<Omit<CompareConfigRule, 'id' | 'createTime'>>): boolean {
    try {
      const rules = this.getCompareConfigList()
      const index = rules.findIndex(rule => rule.id === id)
      if (index !== -1) {
        rules[index] = { ...rules[index], ...updates }
        return this.saveCompareConfigList(rules)
      }
      return false
    } catch (error) {
      console.error('更新对比配置规则失败:', error)
      return false
    }
  }

  // 删除对比配置规则
  static deleteCompareConfigRule(id: string): boolean {
    try {
      const rules = this.getCompareConfigList()
      const filteredRules = rules.filter(rule => rule.id !== id)
      return this.saveCompareConfigList(filteredRules)
    } catch (error) {
      console.error('删除对比配置规则失败:', error)
      return false
    }
  }

  // 批量删除对比配置规则
  static batchDeleteCompareConfigRules(ids: string[]): boolean {
    try {
      const rules = this.getCompareConfigList()
      const filteredRules = rules.filter(rule => !ids.includes(rule.id))
      return this.saveCompareConfigList(filteredRules)
    } catch (error) {
      console.error('批量删除对比配置规则失败:', error)
      return false
    }
  }

  // 获取分页配置
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(this.PAGINATION_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return { page: 1, size: 10, total: 0 }
    } catch (error) {
      console.error('获取对比配置分页配置失败:', error)
      return { page: 1, size: 10, total: 0 }
    }
  }

  // 保存分页配置
  static savePaginationConfig(config: PaginationConfig): boolean {
    try {
      localStorage.setItem(this.PAGINATION_KEY, JSON.stringify(config))
      return true
    } catch (error) {
      console.error('保存对比配置分页配置失败:', error)
      return false
    }
  }

  // 生成默认对比配置数据
  static generateDefaultCompareConfigData(): CompareConfigRule[] {
    return generateMockCompareConfigData()
  }

  // 清除所有对比配置数据（用于重新生成模拟数据）
  static clearAllData(): boolean {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      localStorage.removeItem(this.PAGINATION_KEY)
      return true
    } catch (error) {
      console.error('清除对比配置数据失败:', error)
      return false
    }
  }
}

/**
 * 精准计算配置规则存储管理类
 */
export class PreciseCalcConfigStorage {
  private static readonly STORAGE_KEY = 'precise_calc_config_rules'
  private static readonly PAGINATION_KEY = 'precise_calc_config_pagination'

  // 获取精准计算配置规则列表
  static getPreciseCalcConfigList(): PreciseCalcConfigRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，返回默认模拟数据
      const defaultData = this.generateDefaultPreciseCalcConfigData()
      this.savePreciseCalcConfigList(defaultData)
      return defaultData
    } catch (error) {
      console.error('获取精准计算配置规则列表失败:', error)
      return []
    }
  }

  // 获取精准计算配置规则列表（不自动生成默认数据）
  static getPreciseCalcConfigListRaw(): PreciseCalcConfigRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return []
    } catch (error) {
      console.error('获取精准计算配置规则列表失败:', error)
      return []
    }
  }

  // 保存精准计算配置规则列表
  static savePreciseCalcConfigList(rules: PreciseCalcConfigRule[]): boolean {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(rules))
      return true
    } catch (error) {
      console.error('保存精准计算配置规则列表失败:', error)
      return false
    }
  }

  // 添加精准计算配置规则
  static addPreciseCalcConfigRule(rule: Omit<PreciseCalcConfigRule, 'id' | 'createTime'>): PreciseCalcConfigRule {
    const ruleList = this.getPreciseCalcConfigList()
    const newRule: PreciseCalcConfigRule = {
      ...rule,
      id: `precise_calc_config_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createTime: new Date().toISOString().replace('T', ' ').split('.')[0]
    }

    ruleList.unshift(newRule)
    this.savePreciseCalcConfigList(ruleList)
    return newRule
  }

  // 更新精准计算配置规则
  static updatePreciseCalcConfigRule(id: string, updates: Partial<Omit<PreciseCalcConfigRule, 'id' | 'createTime'>>): boolean {
    try {
      const rules = this.getPreciseCalcConfigList()
      const index = rules.findIndex(rule => rule.id === id)
      if (index !== -1) {
        rules[index] = { ...rules[index], ...updates }
        return this.savePreciseCalcConfigList(rules)
      }
      return false
    } catch (error) {
      console.error('更新精准计算配置规则失败:', error)
      return false
    }
  }

  // 删除精准计算配置规则
  static deletePreciseCalcConfigRule(id: string): boolean {
    try {
      const rules = this.getPreciseCalcConfigList()
      const filteredRules = rules.filter(rule => rule.id !== id)
      return this.savePreciseCalcConfigList(filteredRules)
    } catch (error) {
      console.error('删除精准计算配置规则失败:', error)
      return false
    }
  }

  // 批量删除精准计算配置规则
  static batchDeletePreciseCalcConfigRules(ids: string[]): boolean {
    try {
      const rules = this.getPreciseCalcConfigList()
      const filteredRules = rules.filter(rule => !ids.includes(rule.id))
      return this.savePreciseCalcConfigList(filteredRules)
    } catch (error) {
      console.error('批量删除精准计算配置规则失败:', error)
      return false
    }
  }

  // 获取分页配置
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(this.PAGINATION_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return { page: 1, size: 10, total: 0 }
    } catch (error) {
      console.error('获取精准计算配置分页配置失败:', error)
      return { page: 1, size: 10, total: 0 }
    }
  }

  // 保存分页配置
  static savePaginationConfig(config: PaginationConfig): boolean {
    try {
      localStorage.setItem(this.PAGINATION_KEY, JSON.stringify(config))
      return true
    } catch (error) {
      console.error('保存精准计算配置分页配置失败:', error)
      return false
    }
  }

  // 生成默认精准计算配置数据
  static generateDefaultPreciseCalcConfigData(): PreciseCalcConfigRule[] {
    return generateMockPreciseCalcConfigData()
  }

  // 清除所有精准计算配置数据
  static clearAllData(): boolean {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      localStorage.removeItem(this.PAGINATION_KEY)
      return true
    } catch (error) {
      console.error('清除精准计算配置数据失败:', error)
      return false
    }
  }
}

/**
 * 数据校验设置规则存储管理类
 */
export class DataValidationSettingStorage {
  private static readonly STORAGE_KEY = 'data_validation_setting_rules'
  private static readonly PAGINATION_KEY = 'data_validation_setting_pagination'

  // 获取数据校验设置规则列表
  static getDataValidationSettingList(): DataValidationSettingRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，返回默认模拟数据
      const defaultData = this.generateDefaultDataValidationSettingData()
      this.saveDataValidationSettingList(defaultData)
      return defaultData
    } catch (error) {
      console.error('获取数据校验设置规则列表失败:', error)
      return []
    }
  }

  // 获取数据校验设置规则列表（不自动生成默认数据）
  static getDataValidationSettingListRaw(): DataValidationSettingRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return []
    } catch (error) {
      console.error('获取数据校验设置规则列表失败:', error)
      return []
    }
  }

  // 保存数据校验设置规则列表
  static saveDataValidationSettingList(rules: DataValidationSettingRule[]): boolean {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(rules))
      return true
    } catch (error) {
      console.error('保存数据校验设置规则列表失败:', error)
      return false
    }
  }

  // 添加数据校验设置规则
  static addDataValidationSettingRule(rule: Omit<DataValidationSettingRule, 'id' | 'createTime'>): DataValidationSettingRule {
    const ruleList = this.getDataValidationSettingList()
    const newRule: DataValidationSettingRule = {
      ...rule,
      id: `data_validation_setting_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createTime: new Date().toISOString().replace('T', ' ').split('.')[0]
    }

    ruleList.unshift(newRule)
    this.saveDataValidationSettingList(ruleList)
    return newRule
  }

  // 更新数据校验设置规则
  static updateDataValidationSettingRule(id: string, updates: Partial<Omit<DataValidationSettingRule, 'id' | 'createTime'>>): boolean {
    try {
      const rules = this.getDataValidationSettingList()
      const index = rules.findIndex(rule => rule.id === id)
      if (index !== -1) {
        rules[index] = { ...rules[index], ...updates }
        return this.saveDataValidationSettingList(rules)
      }
      return false
    } catch (error) {
      console.error('更新数据校验设置规则失败:', error)
      return false
    }
  }

  // 删除数据校验设置规则
  static deleteDataValidationSettingRule(id: string): boolean {
    try {
      const rules = this.getDataValidationSettingList()
      const filteredRules = rules.filter(rule => rule.id !== id)
      return this.saveDataValidationSettingList(filteredRules)
    } catch (error) {
      console.error('删除数据校验设置规则失败:', error)
      return false
    }
  }

  // 批量删除数据校验设置规则
  static batchDeleteDataValidationSettingRules(ids: string[]): boolean {
    try {
      const rules = this.getDataValidationSettingList()
      const filteredRules = rules.filter(rule => !ids.includes(rule.id))
      return this.saveDataValidationSettingList(filteredRules)
    } catch (error) {
      console.error('批量删除数据校验设置规则失败:', error)
      return false
    }
  }

  // 获取分页配置
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(this.PAGINATION_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return { page: 1, size: 10, total: 0 }
    } catch (error) {
      console.error('获取数据校验设置分页配置失败:', error)
      return { page: 1, size: 10, total: 0 }
    }
  }

  // 保存分页配置
  static savePaginationConfig(config: PaginationConfig): boolean {
    try {
      localStorage.setItem(this.PAGINATION_KEY, JSON.stringify(config))
      return true
    } catch (error) {
      console.error('保存数据校验设置分页配置失败:', error)
      return false
    }
  }

  // 生成默认数据校验设置数据
  static generateDefaultDataValidationSettingData(): DataValidationSettingRule[] {
    return generateMockDataValidationSettingData()
  }

  // 清除所有数据校验设置数据
  static clearAllData(): boolean {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      localStorage.removeItem(this.PAGINATION_KEY)
      return true
    } catch (error) {
      console.error('清除数据校验设置数据失败:', error)
      return false
    }
  }
}

/**
 * 标识业务表规则存储管理类
 */
export class BusinessTableIdentificationStorage {
  private static readonly STORAGE_KEY = 'business_table_identification_rules'
  private static readonly PAGINATION_KEY = 'business_table_identification_pagination'

  // 获取标识业务表规则列表
  static getBusinessTableIdentificationList(): BusinessTableIdentificationRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，返回默认模拟数据
      const defaultData = this.generateDefaultBusinessTableIdentificationData()
      this.saveBusinessTableIdentificationList(defaultData)
      return defaultData
    } catch (error) {
      console.error('获取标识业务表规则列表失败:', error)
      return []
    }
  }

  // 获取标识业务表规则列表（不自动生成默认数据）
  static getBusinessTableIdentificationListRaw(): BusinessTableIdentificationRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return []
    } catch (error) {
      console.error('获取标识业务表规则列表失败:', error)
      return []
    }
  }

  // 保存标识业务表规则列表
  static saveBusinessTableIdentificationList(rules: BusinessTableIdentificationRule[]): boolean {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(rules))
      return true
    } catch (error) {
      console.error('保存标识业务表规则列表失败:', error)
      return false
    }
  }

  // 添加标识业务表规则
  static addBusinessTableIdentificationRule(rule: Omit<BusinessTableIdentificationRule, 'id' | 'createTime'>): BusinessTableIdentificationRule {
    const ruleList = this.getBusinessTableIdentificationList()
    const newRule: BusinessTableIdentificationRule = {
      ...rule,
      id: `business_table_identification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createTime: new Date().toISOString().replace('T', ' ').split('.')[0]
    }

    ruleList.unshift(newRule)
    this.saveBusinessTableIdentificationList(ruleList)
    return newRule
  }

  // 更新标识业务表规则
  static updateBusinessTableIdentificationRule(id: string, updates: Partial<Omit<BusinessTableIdentificationRule, 'id' | 'createTime'>>): boolean {
    try {
      const rules = this.getBusinessTableIdentificationList()
      const index = rules.findIndex(rule => rule.id === id)
      if (index !== -1) {
        rules[index] = { ...rules[index], ...updates }
        return this.saveBusinessTableIdentificationList(rules)
      }
      return false
    } catch (error) {
      console.error('更新标识业务表规则失败:', error)
      return false
    }
  }

  // 删除标识业务表规则
  static deleteBusinessTableIdentificationRule(id: string): boolean {
    try {
      const rules = this.getBusinessTableIdentificationList()
      const filteredRules = rules.filter(rule => rule.id !== id)
      return this.saveBusinessTableIdentificationList(filteredRules)
    } catch (error) {
      console.error('删除标识业务表规则失败:', error)
      return false
    }
  }

  // 批量删除标识业务表规则
  static batchDeleteBusinessTableIdentificationRules(ids: string[]): boolean {
    try {
      const rules = this.getBusinessTableIdentificationList()
      const filteredRules = rules.filter(rule => !ids.includes(rule.id))
      return this.saveBusinessTableIdentificationList(filteredRules)
    } catch (error) {
      console.error('批量删除标识业务表规则失败:', error)
      return false
    }
  }

  // 获取分页配置
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(this.PAGINATION_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return { page: 1, size: 10, total: 0 }
    } catch (error) {
      console.error('获取标识业务表分页配置失败:', error)
      return { page: 1, size: 10, total: 0 }
    }
  }

  // 保存分页配置
  static savePaginationConfig(config: PaginationConfig): boolean {
    try {
      localStorage.setItem(this.PAGINATION_KEY, JSON.stringify(config))
      return true
    } catch (error) {
      console.error('保存标识业务表分页配置失败:', error)
      return false
    }
  }

  // 生成默认标识业务表数据
  static generateDefaultBusinessTableIdentificationData(): BusinessTableIdentificationRule[] {
    return generateMockBusinessTableIdentificationData()
  }

  // 清除所有标识业务表数据
  static clearAllData(): boolean {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      localStorage.removeItem(this.PAGINATION_KEY)
      return true
    } catch (error) {
      console.error('清除标识业务表数据失败:', error)
      return false
    }
  }
}

/**
 * 临时表转业务表规则存储管理类
 */
export class TempTableToBusinessStorage {
  private static readonly STORAGE_KEY = STORAGE_KEYS.TEMP_TABLE_TO_BUSINESS_LIST
  private static readonly PAGINATION_KEY = STORAGE_KEYS.TEMP_TABLE_TO_BUSINESS_PAGINATION

  // 获取临时表转业务表规则列表
  static getTempTableToBusinessList(): TempTableToBusinessRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，返回默认模拟数据
      const defaultData = this.generateDefaultTempTableToBusinessData()
      this.saveTempTableToBusinessList(defaultData)
      return defaultData
    } catch (error) {
      console.error('获取临时表转业务表规则列表失败:', error)
      return []
    }
  }

  // 获取临时表转业务表规则列表（不自动生成默认数据）
  static getTempTableToBusinessListRaw(): TempTableToBusinessRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return []
    } catch (error) {
      console.error('获取临时表转业务表规则列表失败:', error)
      return []
    }
  }

  // 保存临时表转业务表规则列表
  static saveTempTableToBusinessList(rules: TempTableToBusinessRule[]): boolean {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(rules))
      return true
    } catch (error) {
      console.error('保存临时表转业务表规则列表失败:', error)
      return false
    }
  }

  // 添加临时表转业务表规则
  static addTempTableToBusinessRule(rule: Omit<TempTableToBusinessRule, 'id' | 'createTime'>): TempTableToBusinessRule {
    const ruleList = this.getTempTableToBusinessList()
    const newRule: TempTableToBusinessRule = {
      ...rule,
      id: `temp_table_to_business_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      createTime: new Date().toISOString().replace('T', ' ').split('.')[0]
    }

    ruleList.unshift(newRule)
    this.saveTempTableToBusinessList(ruleList)
    return newRule
  }

  // 更新临时表转业务表规则
  static updateTempTableToBusinessRule(id: string, updates: Partial<Omit<TempTableToBusinessRule, 'id' | 'createTime'>>): boolean {
    try {
      const rules = this.getTempTableToBusinessList()
      const index = rules.findIndex(rule => rule.id === id)
      if (index !== -1) {
        rules[index] = { ...rules[index], ...updates }
        return this.saveTempTableToBusinessList(rules)
      }
      return false
    } catch (error) {
      console.error('更新临时表转业务表规则失败:', error)
      return false
    }
  }

  // 删除临时表转业务表规则
  static deleteTempTableToBusinessRule(id: string): boolean {
    try {
      const rules = this.getTempTableToBusinessList()
      const filteredRules = rules.filter(rule => rule.id !== id)
      return this.saveTempTableToBusinessList(filteredRules)
    } catch (error) {
      console.error('删除临时表转业务表规则失败:', error)
      return false
    }
  }

  // 批量删除临时表转业务表规则
  static batchDeleteTempTableToBusinessRules(ids: string[]): boolean {
    try {
      const rules = this.getTempTableToBusinessList()
      const filteredRules = rules.filter(rule => !ids.includes(rule.id))
      return this.saveTempTableToBusinessList(filteredRules)
    } catch (error) {
      console.error('批量删除临时表转业务表规则失败:', error)
      return false
    }
  }

  // 获取分页配置
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(this.PAGINATION_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return { page: 1, size: 10, total: 0 }
    } catch (error) {
      console.error('获取临时表转业务表分页配置失败:', error)
      return { page: 1, size: 10, total: 0 }
    }
  }

  // 保存分页配置
  static savePaginationConfig(config: PaginationConfig): boolean {
    try {
      localStorage.setItem(this.PAGINATION_KEY, JSON.stringify(config))
      return true
    } catch (error) {
      console.error('保存临时表转业务表分页配置失败:', error)
      return false
    }
  }

  // 生成默认临时表转业务表数据
  static generateDefaultTempTableToBusinessData(): TempTableToBusinessRule[] {
    return generateMockTempTableToBusinessData()
  }

  // 清除所有临时表转业务表数据
  static clearAllData(): boolean {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      localStorage.removeItem(this.PAGINATION_KEY)
      return true
    } catch (error) {
      console.error('清除临时表转业务表数据失败:', error)
      return false
    }
  }
}

/**
 * 数据可视化设置规则存储管理类
 */
export class DataVisualizationSettingStorage {
  private static readonly STORAGE_KEY = STORAGE_KEYS.DATA_VISUALIZATION_SETTING_LIST
  private static readonly PAGINATION_KEY = STORAGE_KEYS.DATA_VISUALIZATION_SETTING_PAGINATION

  // 获取数据可视化设置规则列表
  static getDataVisualizationSettingList(): DataVisualizationSettingRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，返回默认模拟数据
      const defaultData = this.generateDefaultDataVisualizationSettingData()
      this.saveDataVisualizationSettingList(defaultData)
      return defaultData
    } catch (error) {
      console.error('获取数据可视化设置规则列表失败:', error)
      return []
    }
  }

  // 获取数据可视化设置规则列表（不自动生成默认数据）
  static getDataVisualizationSettingListRaw(): DataVisualizationSettingRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return []
    } catch (error) {
      console.error('获取数据可视化设置规则列表失败:', error)
      return []
    }
  }

  // 保存数据可视化设置规则列表
  static saveDataVisualizationSettingList(rules: DataVisualizationSettingRule[]): boolean {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(rules))
      return true
    } catch (error) {
      console.error('保存数据可视化设置规则列表失败:', error)
      return false
    }
  }

  // 添加数据可视化设置规则
  static addDataVisualizationSettingRule(rule: Omit<DataVisualizationSettingRule, 'id' | 'createTime'>): DataVisualizationSettingRule {
    const ruleList = this.getDataVisualizationSettingList()
    const newRule: DataVisualizationSettingRule = {
      ...rule,
      id: `data_visualization_setting_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      createTime: new Date().toISOString().replace('T', ' ').split('.')[0]
    }

    ruleList.unshift(newRule)
    this.saveDataVisualizationSettingList(ruleList)
    return newRule
  }

  // 更新数据可视化设置规则
  static updateDataVisualizationSettingRule(id: string, updates: Partial<Omit<DataVisualizationSettingRule, 'id' | 'createTime'>>): boolean {
    try {
      const rules = this.getDataVisualizationSettingList()
      const index = rules.findIndex(rule => rule.id === id)
      if (index !== -1) {
        rules[index] = { ...rules[index], ...updates }
        return this.saveDataVisualizationSettingList(rules)
      }
      return false
    } catch (error) {
      console.error('更新数据可视化设置规则失败:', error)
      return false
    }
  }

  // 删除数据可视化设置规则
  static deleteDataVisualizationSettingRule(id: string): boolean {
    try {
      const rules = this.getDataVisualizationSettingList()
      const filteredRules = rules.filter(rule => rule.id !== id)
      return this.saveDataVisualizationSettingList(filteredRules)
    } catch (error) {
      console.error('删除数据可视化设置规则失败:', error)
      return false
    }
  }

  // 批量删除数据可视化设置规则
  static batchDeleteDataVisualizationSettingRules(ids: string[]): boolean {
    try {
      const rules = this.getDataVisualizationSettingList()
      const filteredRules = rules.filter(rule => !ids.includes(rule.id))
      return this.saveDataVisualizationSettingList(filteredRules)
    } catch (error) {
      console.error('批量删除数据可视化设置规则失败:', error)
      return false
    }
  }

  // 获取分页配置
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(this.PAGINATION_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return { page: 1, size: 10, total: 0 }
    } catch (error) {
      console.error('获取数据可视化设置分页配置失败:', error)
      return { page: 1, size: 10, total: 0 }
    }
  }

  // 保存分页配置
  static savePaginationConfig(config: PaginationConfig): boolean {
    try {
      localStorage.setItem(this.PAGINATION_KEY, JSON.stringify(config))
      return true
    } catch (error) {
      console.error('保存数据可视化设置分页配置失败:', error)
      return false
    }
  }

  // 生成默认数据可视化设置数据
  static generateDefaultDataVisualizationSettingData(): DataVisualizationSettingRule[] {
    return generateMockDataVisualizationSettingData()
  }

  // 清除所有数据可视化设置数据
  static clearAllData(): boolean {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      localStorage.removeItem(this.PAGINATION_KEY)
      return true
    } catch (error) {
      console.error('清除数据可视化设置数据失败:', error)
      return false
    }
  }
}

/**
 * 重复数据设置规则本地存储管理类
 */
export class DuplicateDataSettingStorage {
  private static readonly STORAGE_KEY = STORAGE_KEYS.DUPLICATE_DATA_SETTING_LIST
  private static readonly PAGINATION_KEY = STORAGE_KEYS.DUPLICATE_DATA_SETTING_PAGINATION

  /**
   * 获取重复数据设置规则列表
   */
  static getRuleList(): DuplicateDataSettingRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，使用默认模拟数据并保存
      const mockData = generateMockDuplicateDataSettingData()
      this.saveRuleList(mockData)
      return mockData
    } catch (error) {
      console.error('获取重复数据设置规则列表失败:', error)
      return generateMockDuplicateDataSettingData()
    }
  }

  /**
   * 保存重复数据设置规则列表
   */
  static saveRuleList(rules: DuplicateDataSettingRule[]): boolean {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(rules))
      return true
    } catch (error) {
      console.error('保存重复数据设置规则列表失败:', error)
      return false
    }
  }

  /**
   * 添加重复数据设置规则
   */
  static addRule(rule: DuplicateDataSettingRule): boolean {
    try {
      const rules = this.getRuleList()
      rules.unshift(rule) // 添加到列表开头
      return this.saveRuleList(rules)
    } catch (error) {
      console.error('添加重复数据设置规则失败:', error)
      return false
    }
  }

  /**
   * 更新重复数据设置规则
   */
  static updateRule(id: string, updatedRule: Partial<DuplicateDataSettingRule>): boolean {
    try {
      const rules = this.getRuleList()
      const index = rules.findIndex(rule => rule.id === id)
      if (index !== -1) {
        rules[index] = { ...rules[index], ...updatedRule }
        return this.saveRuleList(rules)
      }
      return false
    } catch (error) {
      console.error('更新重复数据设置规则失败:', error)
      return false
    }
  }

  /**
   * 删除重复数据设置规则
   */
  static deleteRule(id: string): boolean {
    try {
      const rules = this.getRuleList()
      const filteredRules = rules.filter(rule => rule.id !== id)
      return this.saveRuleList(filteredRules)
    } catch (error) {
      console.error('删除重复数据设置规则失败:', error)
      return false
    }
  }

  /**
   * 批量删除重复数据设置规则
   */
  static deleteRules(ids: string[]): boolean {
    try {
      const rules = this.getRuleList()
      const filteredRules = rules.filter(rule => !ids.includes(rule.id))
      return this.saveRuleList(filteredRules)
    } catch (error) {
      console.error('批量删除重复数据设置规则失败:', error)
      return false
    }
  }

  /**
   * 根据ID获取重复数据设置规则
   */
  static getRuleById(id: string): DuplicateDataSettingRule | null {
    try {
      const rules = this.getRuleList()
      return rules.find(rule => rule.id === id) || null
    } catch (error) {
      console.error('获取重复数据设置规则失败:', error)
      return null
    }
  }

  /**
   * 获取分页配置
   */
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(this.PAGINATION_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return { page: 1, size: 10, total: 0 }
    } catch (error) {
      console.error('获取重复数据设置分页配置失败:', error)
      return { page: 1, size: 10, total: 0 }
    }
  }

  /**
   * 保存分页配置
   */
  static savePaginationConfig(config: PaginationConfig): boolean {
    try {
      localStorage.setItem(this.PAGINATION_KEY, JSON.stringify(config))
      return true
    } catch (error) {
      console.error('保存重复数据设置分页配置失败:', error)
      return false
    }
  }

  /**
   * 清除所有重复数据设置数据
   */
  static clearAllData(): boolean {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      localStorage.removeItem(this.PAGINATION_KEY)
      return true
    } catch (error) {
      console.error('清除重复数据设置数据失败:', error)
      return false
    }
  }
}

/**
 * 数据一致性、完整性、兼容性检查本地存储管理类
 */
export class DataConsistencyCheckStorage {

  /**
   * 获取数据一致性检查配置列表
   */
  static getConfigList(): DataConsistencyCheckConfig[] {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.DATA_CONSISTENCY_CHECK_LIST)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，使用默认模拟数据并保存
      const mockData = generateMockDataConsistencyCheckData()
      this.saveConfigList(mockData)
      return mockData
    } catch (error) {
      console.error('获取数据一致性检查配置列表失败:', error)
      return generateMockDataConsistencyCheckData()
    }
  }

  /**
   * 保存数据一致性检查配置列表
   */
  static saveConfigList(configList: DataConsistencyCheckConfig[]): boolean {
    try {
      localStorage.setItem(STORAGE_KEYS.DATA_CONSISTENCY_CHECK_LIST, JSON.stringify(configList))
      return true
    } catch (error) {
      console.error('保存数据一致性检查配置列表失败:', error)
      return false
    }
  }

  /**
   * 添加数据一致性检查配置
   */
  static addConfig(config: Omit<DataConsistencyCheckConfig, 'id' | 'createTime'>): DataConsistencyCheckConfig {
    const configList = this.getConfigList()
    const newConfig: DataConsistencyCheckConfig = {
      ...config,
      id: `data_consistency_check_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      createTime: new Date().toISOString().replace('T', ' ').split('.')[0]
    }

    configList.unshift(newConfig)
    this.saveConfigList(configList)
    return newConfig
  }

  /**
   * 更新数据一致性检查配置
   */
  static updateConfig(id: string, updates: Partial<Omit<DataConsistencyCheckConfig, 'id' | 'createTime'>>): boolean {
    try {
      const configs = this.getConfigList()
      const index = configs.findIndex(config => config.id === id)
      if (index !== -1) {
        configs[index] = { ...configs[index], ...updates }
        return this.saveConfigList(configs)
      }
      return false
    } catch (error) {
      console.error('更新数据一致性检查配置失败:', error)
      return false
    }
  }

  /**
   * 删除数据一致性检查配置
   */
  static deleteConfig(id: string): boolean {
    try {
      const configs = this.getConfigList()
      const filteredConfigs = configs.filter(config => config.id !== id)
      return this.saveConfigList(filteredConfigs)
    } catch (error) {
      console.error('删除数据一致性检查配置失败:', error)
      return false
    }
  }

  /**
   * 批量删除数据一致性检查配置
   */
  static deleteConfigs(ids: string[]): boolean {
    try {
      const configs = this.getConfigList()
      const filteredConfigs = configs.filter(config => !ids.includes(config.id))
      return this.saveConfigList(filteredConfigs)
    } catch (error) {
      console.error('批量删除数据一致性检查配置失败:', error)
      return false
    }
  }

  /**
   * 根据ID获取数据一致性检查配置
   */
  static getConfigById(id: string): DataConsistencyCheckConfig | null {
    try {
      const configs = this.getConfigList()
      return configs.find(config => config.id === id) || null
    } catch (error) {
      console.error('获取数据一致性检查配置失败:', error)
      return null
    }
  }

  /**
   * 获取分页配置
   */
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.DATA_CONSISTENCY_CHECK_PAGINATION)
      if (data) {
        return JSON.parse(data)
      }
      return { currentPage: 1, pageSize: 10 }
    } catch (error) {
      console.error('获取数据一致性检查分页配置失败:', error)
      return { currentPage: 1, pageSize: 10 }
    }
  }

  /**
   * 保存分页配置
   */
  static savePaginationConfig(config: PaginationConfig): boolean {
    try {
      localStorage.setItem(STORAGE_KEYS.DATA_CONSISTENCY_CHECK_PAGINATION, JSON.stringify(config))
      return true
    } catch (error) {
      console.error('保存数据一致性检查分页配置失败:', error)
      return false
    }
  }

  /**
   * 清除所有数据一致性检查数据
   */
  static clearAllData(): boolean {
    try {
      localStorage.removeItem(STORAGE_KEYS.DATA_CONSISTENCY_CHECK_LIST)
      localStorage.removeItem(STORAGE_KEYS.DATA_CONSISTENCY_CHECK_PAGINATION)
      return true
    } catch (error) {
      console.error('清除数据一致性检查数据失败:', error)
      return false
    }
  }
}

/**
 * 异常数据处理本地存储管理类
 */
export class ExceptionDataProcessingStorage {

  /**
   * 获取异常数据处理配置列表
   */
  static getConfigList(): ExceptionDataProcessingConfig[] {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.EXCEPTION_DATA_PROCESSING_LIST)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，使用默认模拟数据并保存
      const mockData = generateMockExceptionDataProcessingData()
      this.saveConfigList(mockData)
      return mockData
    } catch (error) {
      console.error('获取异常数据处理配置列表失败:', error)
      return generateMockExceptionDataProcessingData()
    }
  }

  /**
   * 保存异常数据处理配置列表
   */
  static saveConfigList(configList: ExceptionDataProcessingConfig[]): boolean {
    try {
      localStorage.setItem(STORAGE_KEYS.EXCEPTION_DATA_PROCESSING_LIST, JSON.stringify(configList))
      return true
    } catch (error) {
      console.error('保存异常数据处理配置列表失败:', error)
      return false
    }
  }

  /**
   * 添加异常数据处理配置
   */
  static addConfig(config: Omit<ExceptionDataProcessingConfig, 'id' | 'createTime'>): ExceptionDataProcessingConfig {
    const configList = this.getConfigList()
    const newConfig: ExceptionDataProcessingConfig = {
      ...config,
      id: `exception_data_processing_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      createTime: new Date().toISOString().replace('T', ' ').split('.')[0]
    }

    configList.unshift(newConfig)
    this.saveConfigList(configList)
    return newConfig
  }

  /**
   * 更新异常数据处理配置
   */
  static updateConfig(id: string, updates: Partial<Omit<ExceptionDataProcessingConfig, 'id' | 'createTime'>>): boolean {
    try {
      const configs = this.getConfigList()
      const index = configs.findIndex(config => config.id === id)
      if (index !== -1) {
        configs[index] = { ...configs[index], ...updates }
        return this.saveConfigList(configs)
      }
      return false
    } catch (error) {
      console.error('更新异常数据处理配置失败:', error)
      return false
    }
  }

  /**
   * 删除异常数据处理配置
   */
  static deleteConfig(id: string): boolean {
    try {
      const configs = this.getConfigList()
      const filteredConfigs = configs.filter(config => config.id !== id)
      return this.saveConfigList(filteredConfigs)
    } catch (error) {
      console.error('删除异常数据处理配置失败:', error)
      return false
    }
  }

  /**
   * 批量删除异常数据处理配置
   */
  static deleteConfigs(ids: string[]): boolean {
    try {
      const configs = this.getConfigList()
      const filteredConfigs = configs.filter(config => !ids.includes(config.id))
      return this.saveConfigList(filteredConfigs)
    } catch (error) {
      console.error('批量删除异常数据处理配置失败:', error)
      return false
    }
  }

  /**
   * 根据ID获取异常数据处理配置
   */
  static getConfigById(id: string): ExceptionDataProcessingConfig | null {
    try {
      const configs = this.getConfigList()
      return configs.find(config => config.id === id) || null
    } catch (error) {
      console.error('获取异常数据处理配置失败:', error)
      return null
    }
  }

  /**
   * 获取分页配置
   */
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.EXCEPTION_DATA_PROCESSING_PAGINATION)
      if (data) {
        return JSON.parse(data)
      }
      return { currentPage: 1, pageSize: 10 }
    } catch (error) {
      console.error('获取异常数据处理分页配置失败:', error)
      return { currentPage: 1, pageSize: 10 }
    }
  }

  /**
   * 保存分页配置
   */
  static savePaginationConfig(config: PaginationConfig): boolean {
    try {
      localStorage.setItem(STORAGE_KEYS.EXCEPTION_DATA_PROCESSING_PAGINATION, JSON.stringify(config))
      return true
    } catch (error) {
      console.error('保存异常数据处理分页配置失败:', error)
      return false
    }
  }

  /**
   * 清除所有异常数据处理数据
   */
  static clearAllData(): boolean {
    try {
      localStorage.removeItem(STORAGE_KEYS.EXCEPTION_DATA_PROCESSING_LIST)
      localStorage.removeItem(STORAGE_KEYS.EXCEPTION_DATA_PROCESSING_PAGINATION)
      return true
    } catch (error) {
      console.error('清除异常数据处理数据失败:', error)
      return false
    }
  }
}
