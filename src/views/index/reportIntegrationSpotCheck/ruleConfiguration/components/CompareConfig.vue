<script setup lang="ts" name="CompareConfig">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { 
  CompareConfigRule, 
  CompareConfigRuleForm, 
  TableColumn, 
  ActionButton, 
  PaginationConfig 
} from '../types'
import { 
  REPORT_TYPE_OPTIONS, 
  MATCH_TYPE_OPTIONS, 
  THRESHOLD_OPERATOR_OPTIONS,
  PRESET_FIELD_OPTIONS 
} from '../types'
import { CompareConfigStorage } from '../storage'

// Props
interface Props {
  tableHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  tableHeight: 400
})

// 响应式数据
const loading = ref(false)
const currentRow = ref<CompareConfigRule | null>(null)

// 表格相关
const tableRef = ref()
const tableData = ref<CompareConfigRule[]>([])
const filteredData = ref<CompareConfigRule[]>([])

// 分页配置
const pagination = reactive<PaginationConfig>({
  page: 1,
  size: 10,
  total: 0
})

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const dialogForm = ref<CompareConfigRuleForm>({
  reportType: 'business',
  matchType: 'exact',
  thresholdOperator: 'gt',
  thresholdValue: 80,
  relatedFields: []
})

// 表格列配置
const columns: TableColumn[] = [
  { prop: 'createTime', label: '创建时间' },
  { prop: 'reportType', label: '报表类型' },
  { prop: 'relatedFields', label: '已关联的字段' },
  { prop: 'matchType', label: '匹配类型' }
]

// 操作按钮配置
const buttons: ActionButton[] = [
  { label: '编辑', type: 'primary', code: 'edit' },
  { label: '删除', type: 'danger', code: 'delete' }
]

// 表单验证规则
const formRules = {
  relatedFields: [
    { required: true, message: '请选择字段关联', trigger: 'change' }
  ],
  thresholdValue: [
    { required: true, message: '请输入阈值', trigger: 'blur' }
  ]
}

// 当前页数据
const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filteredData.value.slice(start, end)
})

// 初始化数据
const initData = () => {
  console.log('初始化对比配置数据...')
  loading.value = true

  try {
    // 清除旧数据并重新生成（临时用于测试新的模拟数据）
    CompareConfigStorage.clearAllData()

    // 生成新的默认数据
    const finalData = CompareConfigStorage.getCompareConfigList()
    console.log('生成新的默认数据:', finalData.length, '条')

    if (Array.isArray(finalData) && finalData.length > 0) {
      tableData.value = finalData
      filteredData.value = [...tableData.value]
      pagination.total = filteredData.value.length
      console.log(`对比配置数据加载完成: ${finalData.length} 条`)
    } else {
      console.error('数据加载失败')
      tableData.value = []
      filteredData.value = []
      pagination.total = 0
    }

    // 恢复分页状态
    const savedPagination = CompareConfigStorage.getPaginationConfig()
    const currentTotal = pagination.total
    Object.assign(pagination, savedPagination)
    pagination.total = currentTotal

    loading.value = false
  } catch (error) {
    console.error('初始化对比配置数据失败:', error)
    loading.value = false
  }
}

// 新增规则
const onClickAdd = () => {
  currentRow.value = null
  dialogForm.value = {
    reportType: 'business',
    matchType: 'exact',
    thresholdOperator: 'gt',
    thresholdValue: 80,
    relatedFields: []
  }
  showDialogForm.value = true
}

// 表格操作按钮点击
const onTableClickButton = ({ row, btn }: any) => {
  if (btn.code === 'edit') {
    currentRow.value = row
    Object.assign(dialogForm.value, {
      reportType: row.reportType,
      matchType: row.matchType,
      thresholdOperator: row.thresholdOperator,
      thresholdValue: row.thresholdValue,
      relatedFields: [...row.relatedFields]
    })
    showDialogForm.value = true
  } else if (btn.code === 'delete') {
    ElMessageBox.confirm('确定要删除这条对比配置规则吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      if (CompareConfigStorage.deleteCompareConfigRule(row.id)) {
        ElMessage.success('删除成功')
        initData()
      } else {
        ElMessage.error('删除失败')
      }
    }).catch(() => {
      // 用户取消删除
    })
  }
}

// 保存规则
const onSaveRule = async () => {
  if (!dialogFormRef.value) return

  try {
    await dialogFormRef.value.validate()
    
    if (currentRow.value) {
      // 编辑模式
      const success = CompareConfigStorage.updateCompareConfigRule(currentRow.value.id, dialogForm.value)
      if (success) {
        ElMessage.success('更新成功')
        showDialogForm.value = false
        initData()
      } else {
        ElMessage.error('更新失败')
      }
    } else {
      // 新增模式
      const newRule = CompareConfigStorage.addCompareConfigRule(dialogForm.value)
      if (newRule) {
        ElMessage.success('添加成功')
        showDialogForm.value = false
        initData()
      } else {
        ElMessage.error('添加失败')
      }
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
const onCancel = () => {
  showDialogForm.value = false
}

// 分页变化
const onPaginationChange = (val: number, type: 'page' | 'size') => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1 // 重置到第一页
  }
  
  // 保存分页状态
  CompareConfigStorage.savePaginationConfig(pagination)
}

// 暴露方法给父组件
defineExpose({
  initData,
  onClickAdd
})

// 组件挂载
onMounted(async () => {
  await nextTick()
  initData()
})
</script>

<template>
  <div class="compare-config">
    <!-- 对比配置表格 -->
    <TableV2
      ref="tableRef"
      :defaultTableData="paginatedData"
      :columns="columns"
      :enable-toolbar="false"
      :enable-own-button="false"
      :enable-selection="false"
      :enable-index="true"
      :height="tableHeight"
      :buttons="buttons"
      :loading="loading"
      @click-button="onTableClickButton"
    >
      <!-- 报表类型列自定义显示 -->
      <template #reportType="{ row }">
        <el-tag :type="row.reportType === 'business' ? 'primary' : 'success'">
          {{ row.reportType === 'business' ? '业务表' : '临时表' }}
        </el-tag>
      </template>

      <!-- 已关联的字段列自定义显示 -->
      <template #relatedFields="{ row }">
        <div class="related-fields">
          <el-tag
            v-for="field in row.relatedFields"
            :key="field"
            size="small"
            type="info"
            style="margin-right: 4px; margin-bottom: 2px;"
          >
            {{ field }}
          </el-tag>
        </div>
      </template>

      <!-- 匹配类型列自定义显示 -->
      <template #matchType="{ row }">
        <el-tag
          :type="row.matchType === 'exact' ? 'success' : row.matchType === 'fuzzy' ? 'warning' : 'info'"
        >
          {{ MATCH_TYPE_OPTIONS.find(opt => opt.value === row.matchType)?.label || row.matchType }}
        </el-tag>
      </template>
    </TableV2>

    <!-- 分页 -->
    <Pagination
      :total="pagination.total"
      :current-page="pagination.page"
      :page-size="pagination.size"
      @current-change="onPaginationChange($event, 'page')"
      @size-change="onPaginationChange($event, 'size')"
    />

    <!-- 新增/编辑对话框 -->
    <DialogComp
      :visible="showDialogForm"
      :title="currentRow ? '编辑对比配置' : '新增对比配置'"
      width="600px"
      @clickConfirm="onSaveRule"
      @clickCancel="onCancel"
      @closed="onCancel"
    >
      <template #body>
        <el-form
          ref="dialogFormRef"
          :model="dialogForm"
          :rules="formRules"
          label-width="120px"
          class="dialog-form"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="报表类型" prop="reportType">
                <el-select v-model="dialogForm.reportType" placeholder="请选择报表类型" style="width: 100%">
                  <el-option
                    v-for="option in REPORT_TYPE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="匹配方式" prop="matchType">
                <el-select v-model="dialogForm.matchType" placeholder="请选择匹配方式" style="width: 100%">
                  <el-option
                    v-for="option in MATCH_TYPE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="阈值操作符" prop="thresholdOperator">
                <el-select v-model="dialogForm.thresholdOperator" placeholder="请选择操作符" style="width: 100%">
                  <el-option
                    v-for="option in THRESHOLD_OPERATOR_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="阈值数值" prop="thresholdValue">
                <el-input-number
                  v-model="dialogForm.thresholdValue"
                  :min="0"
                  :max="100"
                  :precision="0"
                  style="width: 100%"
                  placeholder="请输入阈值"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="字段关联" prop="relatedFields">
                <el-select
                  v-model="dialogForm.relatedFields"
                  multiple
                  placeholder="请选择字段关联（可多选）"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in PRESET_FIELD_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.label"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </DialogComp>
  </div>
</template>

<style scoped lang="scss">
.compare-config {
  .dialog-form {
    padding: 20px 0;
  }

  .related-fields {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;

    .el-tag {
      margin: 0;
    }
  }
}
</style>
