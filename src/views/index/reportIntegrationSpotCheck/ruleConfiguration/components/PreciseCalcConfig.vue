<script setup lang="ts" name="PreciseCalcConfig">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { 
  PreciseCalcConfigRule, 
  PreciseCalcConfigRuleForm, 
  TableColumn, 
  ActionButton, 
  PaginationConfig 
} from '../types'
import { 
  REPORT_TYPE_OPTIONS, 
  THRESHOLD_OPERATOR_OPTIONS,
  PRESET_FIELD_OPTIONS,
  CALC_RULE_TYPE_OPTIONS
} from '../types'
import { PreciseCalcConfigStorage } from '../storage'

// Props
interface Props {
  tableHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  tableHeight: 400
})

// 响应式数据
const loading = ref(false)
const currentRow = ref<PreciseCalcConfigRule | null>(null)
const selectedRows = ref<PreciseCalcConfigRule[]>([])

// 表格相关
const tableRef = ref()
const tableData = ref<PreciseCalcConfigRule[]>([])
const filteredData = ref<PreciseCalcConfigRule[]>([])

// 分页配置
const pagination = reactive<PaginationConfig>({
  page: 1,
  size: 10,
  total: 0
})

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const dialogForm = ref<PreciseCalcConfigRuleForm>({
  calcRuleType: 'direct_assign',
  calcRule: '',
  thresholdOperator: 'gt',
  thresholdValue: 80,
  reportAType: 'business',
  reportBType: 'business',
  reportAField: '',
  reportBField: ''
})

// 表格列配置
const columns: TableColumn[] = [
  { prop: 'createTime', label: '创建时间', width: '160px' },
  { prop: 'calcRuleType', label: '计算规则类型', width: '120px' },
  { prop: 'calcRule', label: '计算规则', width: '200px' },
  { prop: 'thresholdSetting', label: '阈值设定', width: '150px' },
  { prop: 'reportAField', label: '报表A关联字段', width: '130px' },
  { prop: 'reportBField', label: '报表B关联字段', width: '130px' }
]

// 操作按钮配置
const buttons: ActionButton[] = [
  { label: '编辑', type: 'primary', code: 'edit' },
  { label: '删除', type: 'danger', code: 'delete' }
]

// 表单验证规则
const formRules = {
  calcRule: [
    { required: true, message: '请输入计算规则', trigger: 'blur' }
  ],
  thresholdValue: [
    { required: true, message: '请输入阈值', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '阈值范围为0-100', trigger: 'blur' }
  ],
  reportAField: [
    { required: true, message: '请选择报表A关联字段', trigger: 'change' }
  ],
  reportBField: [
    { required: true, message: '请选择报表B关联字段', trigger: 'change' }
  ]
}

// 当前页数据
const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filteredData.value.slice(start, end)
})

// 初始化数据
const initData = () => {
  console.log('初始化精准计算配置数据...')
  loading.value = true

  try {
    // 先检查是否有真实数据
    const existingRules = PreciseCalcConfigStorage.getPreciseCalcConfigListRaw()
    let finalData: PreciseCalcConfigRule[]

    if (existingRules.length > 0) {
      // 有真实数据，使用现有数据
      finalData = existingRules
      console.log('使用现有数据:', existingRules.length, '条')
    } else {
      // 没有数据，生成默认数据
      finalData = PreciseCalcConfigStorage.getPreciseCalcConfigList()
      console.log('生成默认数据:', finalData.length, '条')
    }

    if (Array.isArray(finalData) && finalData.length > 0) {
      tableData.value = finalData
      filteredData.value = [...tableData.value]
      pagination.total = filteredData.value.length
      console.log(`精准计算配置数据加载完成: ${finalData.length} 条`)
    } else {
      console.error('数据加载失败')
      tableData.value = []
      filteredData.value = []
      pagination.total = 0
    }

    // 恢复分页状态
    const savedPagination = PreciseCalcConfigStorage.getPaginationConfig()
    const currentTotal = pagination.total
    Object.assign(pagination, savedPagination)
    pagination.total = currentTotal

    loading.value = false
  } catch (error) {
    console.error('初始化精准计算配置数据失败:', error)
    loading.value = false
  }
}

// 新增规则
const onClickAdd = () => {
  currentRow.value = null
  dialogForm.value = {
    calcRuleType: 'direct_assign',
    calcRule: '',
    thresholdOperator: 'gt',
    thresholdValue: 80,
    reportAType: 'business',
    reportBType: 'business',
    reportAField: '',
    reportBField: ''
  }
  showDialogForm.value = true
}

// 表格操作按钮点击
const onTableClickButton = ({ row, btn }: any) => {
  if (btn.code === 'edit') {
    currentRow.value = row
    Object.assign(dialogForm.value, {
      calcRuleType: row.calcRuleType,
      calcRule: row.calcRule,
      thresholdOperator: row.thresholdOperator,
      thresholdValue: row.thresholdValue,
      reportAType: row.reportAType,
      reportBType: row.reportBType,
      reportAField: row.reportAField,
      reportBField: row.reportBField
    })
    showDialogForm.value = true
  } else if (btn.code === 'delete') {
    ElMessageBox.confirm('确定要删除这条精准计算配置规则吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      if (PreciseCalcConfigStorage.deletePreciseCalcConfigRule(row.id)) {
        ElMessage.success('删除成功')
        initData()
      } else {
        ElMessage.error('删除失败')
      }
    }).catch(() => {
      // 用户取消删除
    })
  }
}

// 选择变化
const onSelectionChange = (selection: PreciseCalcConfigRule[]) => {
  selectedRows.value = selection
}

// 批量删除
const onBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的数据')
    return
  }

  ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 条精准计算配置规则吗？`, '批量删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const ids = selectedRows.value.map(row => row.id)
    if (PreciseCalcConfigStorage.batchDeletePreciseCalcConfigRules(ids)) {
      ElMessage.success(`成功删除 ${selectedRows.value.length} 条数据`)
      selectedRows.value = []
      initData()
    } else {
      ElMessage.error('批量删除失败')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 保存规则
const onSaveRule = async () => {
  if (!dialogFormRef.value) return

  try {
    await dialogFormRef.value.validate()
    
    if (currentRow.value) {
      // 编辑模式
      const success = PreciseCalcConfigStorage.updatePreciseCalcConfigRule(currentRow.value.id, dialogForm.value)
      if (success) {
        ElMessage.success('更新成功')
        showDialogForm.value = false
        initData()
      } else {
        ElMessage.error('更新失败')
      }
    } else {
      // 新增模式
      const newRule = PreciseCalcConfigStorage.addPreciseCalcConfigRule(dialogForm.value)
      if (newRule) {
        ElMessage.success('添加成功')
        showDialogForm.value = false
        initData()
      } else {
        ElMessage.error('添加失败')
      }
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
const onCancel = () => {
  showDialogForm.value = false
}

// 分页变化
const onPaginationChange = (val: number, type: 'page' | 'size') => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1 // 重置到第一页
  }
  
  // 保存分页状态
  PreciseCalcConfigStorage.savePaginationConfig(pagination)
}

// 暴露方法给父组件
defineExpose({
  initData,
  onClickAdd,
  onBatchDelete
})

// 组件挂载
onMounted(async () => {
  await nextTick()
  initData()
})
</script>

<template>
  <div class="precise-calc-config">
    <!-- 精准计算配置表格 -->
    <TableV2
      ref="tableRef"
      :defaultTableData="paginatedData"
      :columns="columns"
      :enable-toolbar="false"
      :enable-own-button="false"
      :enable-selection="true"
      :enable-index="true"
      :height="tableHeight"
      :buttons="buttons"
      :loading="loading"
      @click-button="onTableClickButton"
      @selection-change="onSelectionChange"
    >
      <!-- 计算规则类型列自定义显示 -->
      <template #calcRuleType="{ row }">
        <el-tag
          :type="row.calcRuleType === 'direct_assign' ? 'success' :
                 row.calcRuleType === 'formula_calc' ? 'primary' :
                 row.calcRuleType === 'condition_calc' ? 'warning' : 'info'"
        >
          {{ CALC_RULE_TYPE_OPTIONS.find(opt => opt.value === row.calcRuleType)?.label || row.calcRuleType }}
        </el-tag>
      </template>

      <!-- 计算规则列自定义显示 -->
      <template #calcRule="{ row }">
        <el-tooltip :content="row.calcRule" placement="top" :show-after="500">
          <span class="calc-rule-text">{{ row.calcRule }}</span>
        </el-tooltip>
      </template>

      <!-- 阈值设定列自定义显示 -->
      <template #thresholdSetting="{ row }">
        <div class="threshold-setting">
          <el-tag size="small" type="info">
            {{ THRESHOLD_OPERATOR_OPTIONS.find(opt => opt.value === row.thresholdOperator)?.label || row.thresholdOperator }}
          </el-tag>
          <span class="threshold-value">{{ row.thresholdValue }}</span>
        </div>
      </template>

      <!-- 报表A关联字段列自定义显示 -->
      <template #reportAField="{ row }">
        <div class="field-info">
          <el-tag size="small" :type="row.reportAType === 'business' ? 'primary' : 'success'">
            {{ row.reportAType === 'business' ? '业务表' : '临时表' }}
          </el-tag>
          <span class="field-name">{{ row.reportAField }}</span>
        </div>
      </template>

      <!-- 报表B关联字段列自定义显示 -->
      <template #reportBField="{ row }">
        <div class="field-info">
          <el-tag size="small" :type="row.reportBType === 'business' ? 'primary' : 'success'">
            {{ row.reportBType === 'business' ? '业务表' : '临时表' }}
          </el-tag>
          <span class="field-name">{{ row.reportBField }}</span>
        </div>
      </template>
    </TableV2>

    <!-- 分页 -->
    <Pagination
      :total="pagination.total"
      :current-page="pagination.page"
      :page-size="pagination.size"
      @current-change="onPaginationChange($event, 'page')"
      @size-change="onPaginationChange($event, 'size')"
    />

    <!-- 新增/编辑对话框 -->
    <DialogComp
      :visible="showDialogForm"
      :title="currentRow ? '编辑精准计算配置' : '新增精准计算配置'"
      width="700px"
      @clickConfirm="onSaveRule"
      @clickCancel="onCancel"
      @closed="onCancel"
    >
      <template #body>
        <el-form
          ref="dialogFormRef"
          :model="dialogForm"
          :rules="formRules"
          label-width="120px"
          class="dialog-form"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="计算规则类型" prop="calcRuleType">
                <el-select v-model="dialogForm.calcRuleType" placeholder="请选择计算规则类型" style="width: 100%">
                  <el-option
                    v-for="option in CALC_RULE_TYPE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="阈值操作符" prop="thresholdOperator">
                <el-select v-model="dialogForm.thresholdOperator" placeholder="请选择操作符" style="width: 100%">
                  <el-option
                    v-for="option in THRESHOLD_OPERATOR_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="计算规则" prop="calcRule">
                <el-input
                  v-model="dialogForm.calcRule"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入计算规则，如：SUM(字段A) + AVG(字段B)"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="阈值" prop="thresholdValue">
                <el-input-number
                  v-model="dialogForm.thresholdValue"
                  :min="0"
                  :max="100"
                  :precision="0"
                  style="width: 100%"
                  placeholder="请输入阈值"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="报表A类型" prop="reportAType">
                <el-select v-model="dialogForm.reportAType" placeholder="请选择报表A类型" style="width: 100%">
                  <el-option
                    v-for="option in REPORT_TYPE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报表B类型" prop="reportBType">
                <el-select v-model="dialogForm.reportBType" placeholder="请选择报表B类型" style="width: 100%">
                  <el-option
                    v-for="option in REPORT_TYPE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="报表A字段" prop="reportAField">
                <el-select v-model="dialogForm.reportAField" placeholder="请选择报表A字段" style="width: 100%">
                  <el-option
                    v-for="option in PRESET_FIELD_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.label"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报表B字段" prop="reportBField">
                <el-select v-model="dialogForm.reportBField" placeholder="请选择报表B字段" style="width: 100%">
                  <el-option
                    v-for="option in PRESET_FIELD_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.label"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </DialogComp>
  </div>
</template>

<style scoped lang="scss">
.precise-calc-config {
  .dialog-form {
    padding: 20px 0;
  }

  .calc-rule-text {
    display: inline-block;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }

  .threshold-setting {
    display: flex;
    align-items: center;
    gap: 8px;

    .threshold-value {
      font-weight: 500;
      color: #409eff;
    }
  }

  .field-info {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .field-name {
      font-size: 12px;
      color: #606266;
    }
  }
}
</style>
