<script setup lang="ts" name="DataVisualizationSetting">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import html2canvas from 'html2canvas'

// Props
interface Props {
  tableHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  tableHeight: 400
})

// 响应式数据
const loading = ref(false)
const currentChartType = ref<'pie' | 'line' | 'bar'>('bar') // 默认为柱状图

// 图表引用
const pieChartRef = ref<HTMLElement>()
const lineChartRef = ref<HTMLElement>()
const barChartRef = ref<HTMLElement>()

// 图表实例
let pieChart: echarts.ECharts | null = null
let lineChart: echarts.ECharts | null = null
let barChart: echarts.ECharts | null = null

// 配置选项
const chartConfig = reactive({
  // 颜色组合选项
  colorSchemes: [
    { name: '蓝色', colors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'] },
    { name: '绿色', colors: ['#91cc75', '#5470c6', '#fac858', '#ee6666', '#73c0de'] },
    { name: '橙色', colors: ['#fac858', '#5470c6', '#91cc75', '#ee6666', '#73c0de'] },
    { name: '红色', colors: ['#ee6666', '#5470c6', '#91cc75', '#fac858', '#73c0de'] }
  ],
  selectedColorScheme: 0,

  // 字体设置
  fontFamily: '微软雅黑',
  fontSize: 14,

  // 其他设置
  showDataLabels: true,
  showGridLines: true,
  exportFormat: 'xls',

  // 轴设置
  xAxisField: 'time',
  yAxisField: 'quantity',

  // 分享设置
  shareSettings: {
    clickDisplay: true,
    enabled: true,
    exportEnabled: true,
    linkEnabled: true
  },

  // 分享权限和其他设置
  sharePermissions: 'public',
  validityPeriod: 7,
  chartTitle: '业务数据统计一览'
})

// 模拟数据
const mockData = {
  // 饼图数据
  pieData: [
    { name: '类别A', value: 61 },
    { name: '类别B', value: 18 },
    { name: '类别C', value: 21 }
  ],

  // 折线图数据 - 根据字段类型提供不同数据
  lineData: {
    time: {
      categories: ['时间1', '时间2', '时间3', '时间4', '时间5', '时间6'],
      series: [81, 65, 70, 85, 75, 80]
    },
    category: {
      categories: ['类别A', '类别B', '类别C', '类别D', '类别E', '类别F'],
      series: [95, 78, 82, 88, 72, 85]
    }
  },

  // 柱状图数据 - 根据字段类型提供不同数据
  barData: {
    time: {
      categories: ['时间1', '时间2', '时间3', '时间4', '时间5'],
      series: [120, 200, 150, 80, 70]
    },
    category: {
      categories: ['类别A', '类别B', '类别C', '类别D', '类别E'],
      series: [120, 200, 150, 80, 70]
    }
  }
}

// 计算属性
const currentColors = computed(() => {
  return chartConfig.colorSchemes[chartConfig.selectedColorScheme].colors
})

// 字段名映射
const fieldNameMap = {
  time: '时间',
  category: '类别',
  quantity: '数量',
  amount: '金额'
}

// 获取当前X轴字段名
const currentXAxisName = computed(() => {
  return fieldNameMap[chartConfig.xAxisField as keyof typeof fieldNameMap] || 'X轴字段名'
})

// 获取当前Y轴字段名
const currentYAxisName = computed(() => {
  return fieldNameMap[chartConfig.yAxisField as keyof typeof fieldNameMap] || 'Y轴字段名'
})

// 获取当前折线图数据
const currentLineData = computed(() => {
  return mockData.lineData[chartConfig.xAxisField as keyof typeof mockData.lineData] || mockData.lineData.time
})

// 获取当前柱状图数据
const currentBarData = computed(() => {
  return mockData.barData[chartConfig.xAxisField as keyof typeof mockData.barData] || mockData.barData.time
})

// 计算是否显示工具栏
const showToolbar = computed(() => {
  return chartConfig.shareSettings.enabled &&
         (chartConfig.shareSettings.exportEnabled || chartConfig.shareSettings.linkEnabled)
})

// 计算是否显示任何功能按钮
const showAnyButton = computed(() => {
  return chartConfig.shareSettings.exportEnabled || chartConfig.shareSettings.linkEnabled
})

// 图表选项配置
const pieOption = computed((): EChartsOption => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'horizontal',
    left: 'center',
    bottom: '8%',
    data: mockData.pieData.map(item => item.name),
    textStyle: {
      fontSize: chartConfig.fontSize,
      fontFamily: chartConfig.fontFamily
    }
  },
  series: [
    {
      name: '业务数据统计一览',
      type: 'pie',
      radius: ['30%', '65%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: chartConfig.showDataLabels,
        position: 'outside',
        fontSize: chartConfig.fontSize,
        fontFamily: chartConfig.fontFamily,
        formatter: '{b}: {c} ({d}%)'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: chartConfig.fontSize + 2,
          fontWeight: 'bold'
        }
      },
      data: mockData.pieData.map((item, index) => ({
        name: item.name,
        value: item.value,
        itemStyle: {
          color: currentColors.value[index % currentColors.value.length]
        }
      }))
    }
  ]
}))

const lineOption = computed((): EChartsOption => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  grid: {
    left: '15%',
    right: '15%',
    bottom: '20%',
    top: '20%',
    containLabel: true,
    show: chartConfig.showGridLines
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: currentLineData.value.categories,
    name: currentXAxisName.value,
    nameLocation: 'middle',
    nameGap: 30,
    nameTextStyle: {
      fontSize: chartConfig.fontSize + 2,
      fontFamily: chartConfig.fontFamily
    },
    axisLabel: {
      fontSize: chartConfig.fontSize,
      fontFamily: chartConfig.fontFamily
    }
  },
  yAxis: {
    type: 'value',
    name: currentYAxisName.value,
    nameLocation: 'middle',
    nameGap: 50,
    nameTextStyle: {
      fontSize: chartConfig.fontSize + 2,
      fontFamily: chartConfig.fontFamily
    },
    axisLabel: {
      fontSize: chartConfig.fontSize,
      fontFamily: chartConfig.fontFamily
    }
  },
  series: [
    {
      name: '类别A',
      type: 'line',
      data: currentLineData.value.series,
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: {
        color: currentColors.value[0]
      },
      lineStyle: {
        color: currentColors.value[0],
        width: 4
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: currentColors.value[0] + '40'
          }, {
            offset: 1, color: currentColors.value[0] + '10'
          }]
        }
      },
      label: {
        show: chartConfig.showDataLabels,
        fontSize: chartConfig.fontSize + 1,
        fontFamily: chartConfig.fontFamily,
        position: 'top'
      }
    }
  ]
}))

const barOption = computed((): EChartsOption => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
    show: chartConfig.showGridLines
  },
  xAxis: {
    type: 'category',
    data: currentBarData.value.categories,
    name: currentXAxisName.value,
    nameTextStyle: {
      fontSize: chartConfig.fontSize,
      fontFamily: chartConfig.fontFamily
    }
  },
  yAxis: {
    type: 'value',
    name: currentYAxisName.value,
    nameTextStyle: {
      fontSize: chartConfig.fontSize,
      fontFamily: chartConfig.fontFamily
    }
  },
  series: [
    {
      name: '数量',
      type: 'bar',
      data: currentBarData.value.series.map((value, index) => ({
        value,
        itemStyle: {
          color: currentColors.value[index % currentColors.value.length]
        }
      })),
      label: {
        show: chartConfig.showDataLabels,
        position: 'top',
        fontSize: chartConfig.fontSize,
        fontFamily: chartConfig.fontFamily
      }
    }
  ]
}))

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    setTimeout(() => {
      // 只初始化当前选中的图表类型
      if (currentChartType.value === 'bar') {
        initBarChart()
      } else if (currentChartType.value === 'pie') {
        initPieChart()
      } else if (currentChartType.value === 'line') {
        initLineChart()
      }
    }, 200)
  })
}

// 初始化饼图
const initPieChart = () => {
  if (!pieChartRef.value) return

  // 检查容器是否可见
  const containerStyle = window.getComputedStyle(pieChartRef.value)
  if (containerStyle.display === 'none') {
    return
  }

  if (pieChart) {
    pieChart.dispose()
  }

  pieChart = echarts.init(pieChartRef.value)
  pieChart.setOption(pieOption.value)

  // 确保图表正确适应容器
  setTimeout(() => {
    if (pieChart) {
      pieChart.resize()
    }
  }, 100)
}

// 初始化折线图
const initLineChart = () => {
  if (!lineChartRef.value) return

  // 检查容器是否可见
  const containerStyle = window.getComputedStyle(lineChartRef.value)
  if (containerStyle.display === 'none') {
    return
  }

  if (lineChart) {
    lineChart.dispose()
  }

  lineChart = echarts.init(lineChartRef.value)
  lineChart.setOption(lineOption.value)

  // 确保图表正确适应容器
  setTimeout(() => {
    if (lineChart) {
      lineChart.resize()
    }
  }, 100)
}

// 初始化柱状图
const initBarChart = () => {
  console.log('开始初始化柱状图')
  if (!barChartRef.value) {
    console.log('柱状图容器不存在')
    return
  }

  // 检查容器是否可见
  const containerStyle = window.getComputedStyle(barChartRef.value)
  if (containerStyle.display === 'none') {
    console.log('柱状图容器不可见')
    return
  }

  if (barChart) {
    barChart.dispose()
  }

  console.log('正在创建柱状图实例')
  barChart = echarts.init(barChartRef.value)
  console.log('柱状图配置:', barOption.value)
  barChart.setOption(barOption.value)
  console.log('柱状图初始化完成')

  // 确保图表正确适应容器
  setTimeout(() => {
    if (barChart) {
      barChart.resize()
    }
  }, 100)
}



// 切换图表类型
const switchChartType = (type: 'pie' | 'line' | 'bar') => {
  currentChartType.value = type

  // 延迟初始化图表，确保DOM元素可见后再初始化
  nextTick(() => {
    setTimeout(() => {
      if (type === 'pie') {
        initPieChart()
      } else if (type === 'line') {
        initLineChart()
      } else if (type === 'bar') {
        initBarChart()
      }
    }, 100)
  })
}

// 导出和分享功能
const exportLoading = ref(false)
const shareLoading = ref(false)
const isFullscreen = ref(false)

// 获取当前图表实例
const getCurrentChart = () => {
  switch (currentChartType.value) {
    case 'pie':
      return pieChart
    case 'line':
      return lineChart
    case 'bar':
      return barChart
    default:
      return null
  }
}

// 获取当前图表标题
const getCurrentChartTitle = () => {
  if (chartConfig.chartTitle && chartConfig.chartTitle.trim()) {
    return chartConfig.chartTitle
  }
  const typeMap = {
    pie: '饼图',
    line: '折线图',
    bar: '柱状图'
  }
  return `数据可视化-${typeMap[currentChartType.value]}`
}

// 导出图表为图片
const exportChartAsImage = async (format: 'png' | 'svg' = 'png') => {
  try {
    exportLoading.value = true
    const chart = getCurrentChart()

    if (!chart) {
      ElMessage.error('图表未初始化，无法导出')
      return
    }

    if (format === 'png') {
      // 导出为PNG
      const dataURL = chart.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })

      // 下载图片
      const link = document.createElement('a')
      link.download = `${getCurrentChartTitle()}_${new Date().toISOString().slice(0, 10)}.png`
      link.href = dataURL
      link.click()

      ElMessage.success('图片导出成功')
    } else if (format === 'svg') {
      // 导出为SVG
      const svgStr = chart.renderToSVGString()
      const blob = new Blob([svgStr], { type: 'image/svg+xml' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.download = `${getCurrentChartTitle()}_${new Date().toISOString().slice(0, 10)}.svg`
      link.href = url
      link.click()

      URL.revokeObjectURL(url)
      ElMessage.success('SVG导出成功')
    }
  } catch (error) {
    console.error('导出图片失败:', error)
    ElMessage.error('导出图片失败，请重试')
  } finally {
    exportLoading.value = false
  }
}

// 导出图表数据为Excel
const exportChartDataAsExcel = async () => {
  try {
    exportLoading.value = true

    // 动态导入ExcelJS和file-saver
    const [ExcelJS, FileSaver] = await Promise.all([
      import('exceljs'),
      import('file-saver')
    ])

    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('图表数据')

    // 根据图表类型准备数据
    let data: any[] = []
    let headers: string[] = []

    switch (currentChartType.value) {
      case 'pie':
        headers = ['类别', '数值', '占比']
        data = mockData.pieData.map(item => [
          item.name,
          item.value,
          `${((item.value / mockData.pieData.reduce((sum, d) => sum + d.value, 0)) * 100).toFixed(2)}%`
        ])
        break
      case 'line':
        headers = [currentXAxisName.value, currentYAxisName.value]
        data = currentLineData.value.categories.map((cat, index) => [
          cat,
          currentLineData.value.series[index]
        ])
        break
      case 'bar':
        headers = [currentXAxisName.value, currentYAxisName.value]
        data = currentBarData.value.categories.map((cat, index) => [
          cat,
          currentBarData.value.series[index]
        ])
        break
    }

    // 添加表头
    worksheet.addRow(headers)

    // 添加数据
    data.forEach(row => {
      worksheet.addRow(row)
    })

    // 设置列宽
    worksheet.columns.forEach(column => {
      column.width = 15
    })

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 下载文件
    const fileName = `${getCurrentChartTitle()}_数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    FileSaver.saveAs(blob, fileName)

    ElMessage.success('Excel导出成功')
  } catch (error) {
    console.error('导出Excel失败:', error)
    ElMessage.error('导出Excel失败，请重试')
  } finally {
    exportLoading.value = false
  }
}

// 导出为PDF
const exportChartAsPDF = async () => {
  try {
    exportLoading.value = true

    ElMessageBox.alert(
      '当前系统使用浏览器打印功能来生成PDF。如需完整的PDF导出功能，请联系系统管理员安装相关组件。',
      'PDF导出说明',
      {
        confirmButtonText: '继续打印',
        type: 'info'
      }
    ).then(async () => {
      await printChart()
    }).catch(() => {
      // 用户取消
    })
  } catch (error) {
    console.error('导出PDF失败:', error)
    ElMessage.error('导出PDF失败，请重试')
  } finally {
    exportLoading.value = false
  }
}

// 打印图表
const printChart = async () => {
  const chart = getCurrentChart()
  if (!chart) {
    ElMessage.error('图表未初始化，无法打印')
    return
  }

  // 获取图表图片
  const dataURL = chart.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff'
  })

  // 创建打印窗口
  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(`
      <html>
        <head>
          <title>${getCurrentChartTitle()}</title>
          <style>
            body { margin: 0; padding: 20px; text-align: center; }
            img { max-width: 100%; height: auto; }
            h1 { font-family: Arial, sans-serif; color: #333; }
          </style>
        </head>
        <body>
          <h1>${getCurrentChartTitle()}</h1>
          <img src="${dataURL}" alt="图表" />
          <p>导出时间：${new Date().toLocaleString()}</p>
        </body>
      </html>
    `)
    printWindow.document.close()
    printWindow.print()
  }
}

// 分享图表链接
const shareChartLink = async () => {
  try {
    shareLoading.value = true
    ElMessage.info('正在生成分享链接...')

    // 模拟生成分享链接的过程
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 构建分享参数
    const shareParams = new URLSearchParams()
    shareParams.set('chartType', currentChartType.value)
    shareParams.set('chartTitle', getCurrentChartTitle())
    shareParams.set('colorScheme', chartConfig.selectedColorScheme.toString())
    shareParams.set('share', Date.now().toString())
    shareParams.set('type', 'chart')

    // 生成分享链接
    const shareUrl = `${window.location.origin}/reportIntegrationSpotCheck/ruleConfiguration?${shareParams.toString()}`

    // 准备分享内容
    const shareTitle = getCurrentChartTitle()
    const shareText = `查看数据可视化图表：${shareTitle}`

    // 尝试使用Web Share API（如果支持）
    if (navigator.share) {
      await navigator.share({
        title: shareTitle,
        text: shareText,
        url: shareUrl
      })
      ElMessage.success('分享成功')
    } else {
      // 备选方案：复制到剪贴板
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(shareUrl)
        ElMessage.success('分享链接已复制到剪贴板')
      } else {
        // 最后的备选方案：显示链接
        ElMessageBox.alert(shareUrl, '分享链接', {
          confirmButtonText: '确定',
          type: 'info'
        })
      }
    }
  } catch (error) {
    console.error('分享失败:', error)
    ElMessage.error('分享失败，请重试')
  } finally {
    shareLoading.value = false
  }
}

// 处理导出命令
const handleExportCommand = (command: string) => {
  switch (command) {
    case 'png':
      exportChartAsImage('png')
      break
    case 'svg':
      exportChartAsImage('svg')
      break
    case 'excel':
      exportChartDataAsExcel()
      break
    case 'pdf':
      exportChartAsPDF()
      break
    default:
      ElMessage.info(`${command} 功能开发中...`)
  }
}



// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value

  // 延迟调整图表大小
  nextTick(() => {
    setTimeout(() => {
      const chart = getCurrentChart()
      if (chart) {
        chart.resize()
      }
    }, 300)
  })
}

// 暴露方法给父组件
const onClickAdd = () => {
  // 这里可以添加新增图表的逻辑
  console.log('添加新图表')
}

const onBatchDelete = () => {
  // 这里可以添加批量删除的逻辑
  console.log('批量删除图表')
}

// 监听配置变化，更新图表
const updateChartsOnConfigChange = () => {
  updateCharts()
}

// 更新图表配置
const updateCharts = () => {
  if (currentChartType.value === 'pie' && pieChart) {
    pieChart.setOption(pieOption.value)
  } else if (currentChartType.value === 'line' && lineChart) {
    lineChart.setOption(lineOption.value)
  } else if (currentChartType.value === 'bar' && barChart) {
    barChart.setOption(barOption.value)
  }
}

// 暴露方法给父组件
defineExpose({
  initData: initCharts,
  onClickAdd,
  onBatchDelete
})

// 组件挂载
onMounted(async () => {
  await nextTick()
  initCharts()
})
</script>

<template>
  <div class="data-visualization-setting">
    <!-- 图表类型切换 -->
    <div class="chart-type-tabs">
      <el-radio-group v-model="currentChartType" @change="(val) => switchChartType(val as 'pie' | 'line' | 'bar')">
        <el-radio-button label="bar">柱状图</el-radio-button>
        <el-radio-button label="pie">饼图</el-radio-button>
        <el-radio-button label="line">折线图</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <div class="config-section">
          <h4>数据可视化设置</h4>

          <!-- 颜色组合选择 -->
          <div class="config-item">
            <label>颜色组合选择</label>
            <div class="color-scheme-selector">
              <div
                v-for="(scheme, index) in chartConfig.colorSchemes"
                :key="index"
                class="color-scheme-item"
                :class="{ active: chartConfig.selectedColorScheme === index }"
                @click="chartConfig.selectedColorScheme = index; updateChartsOnConfigChange()"
              >
                <div class="color-preview">
                  <span
                    v-for="color in scheme.colors.slice(0, 3)"
                    :key="color"
                    class="color-dot"
                    :style="{ backgroundColor: color }"
                  ></span>
                </div>
                <span class="scheme-name">{{ scheme.name }}</span>
              </div>
            </div>
          </div>

          <!-- 字体及大小 -->
          <div class="config-item">
            <label>字体及大小</label>
            <div class="font-config">
              <el-select
                v-model="chartConfig.fontFamily"
                placeholder="字体"
                @change="updateChartsOnConfigChange"
              >
                <el-option label="微软雅黑" value="微软雅黑" />
                <el-option label="宋体" value="宋体" />
                <el-option label="黑体" value="黑体" />
              </el-select>
              <el-slider
                v-model="chartConfig.fontSize"
                :min="10"
                :max="24"
                :step="1"
                show-input
                @change="updateChartsOnConfigChange"
              />
            </div>
          </div>

          <!-- 轴设置（仅折线图和柱状图显示） -->
          <template v-if="currentChartType === 'line' || currentChartType === 'bar'">
            <div class="config-item">
              <label>X轴字段名</label>
              <el-select v-model="chartConfig.xAxisField" placeholder="请选择X轴字段" @change="updateChartsOnConfigChange">
                <el-option label="时间" value="time" />
                <el-option label="类别" value="category" />
              </el-select>
            </div>

            <div class="config-item">
              <label>Y轴字段名</label>
              <el-select v-model="chartConfig.yAxisField" placeholder="请选择Y轴字段" @change="updateChartsOnConfigChange">
                <el-option label="数量" value="quantity" />
                <el-option label="金额" value="amount" />
              </el-select>
            </div>

            <div class="config-item">
              <label>显示数据标签</label>
              <el-switch
                v-model="chartConfig.showDataLabels"
                @change="updateChartsOnConfigChange"
              />
            </div>

            <div class="config-item">
              <label>显示网格线</label>
              <el-switch
                v-model="chartConfig.showGridLines"
                @change="updateChartsOnConfigChange"
              />
            </div>

            <div class="config-item" v-if="currentChartType === 'line'">
              <label>连接样式</label>
              <el-select placeholder="连接样式">
                <el-option label="实线" value="solid" />
                <el-option label="虚线" value="dashed" />
              </el-select>
            </div>
          </template>
        </div>

        <!-- 其他设置区域 -->
        <div class="config-section">
          <h4>其他设置</h4>

          <!-- 表设置 -->
          <div class="config-item">
            <label>点击显示功能</label>
            <el-switch v-model="chartConfig.shareSettings.clickDisplay" />
          </div>

          <div class="config-item">
            <label>开启/关闭</label>
            <el-switch v-model="chartConfig.shareSettings.enabled" />
          </div>

          <!-- 分享设置 -->
          <div class="config-item">
            <label>导出</label>
            <el-switch v-model="chartConfig.shareSettings.exportEnabled" />
          </div>

          <div class="config-item">
            <label>链接</label>
            <el-switch v-model="chartConfig.shareSettings.linkEnabled" />
          </div>

          <div class="config-item">
            <label>分享权限</label>
            <el-select v-model="chartConfig.sharePermissions" placeholder="请选择分享权限">
              <el-option label="公开" value="public" />
              <el-option label="仅查看" value="view_only" />
              <el-option label="私有" value="private" />
            </el-select>
          </div>

          <div class="config-item">
            <label>有效期限</label>
            <el-input-number
              v-model="chartConfig.validityPeriod"
              :min="1"
              :max="365"
              placeholder="天数"
              controls-position="right"
            />
          </div>

          <div class="config-item">
            <label>导出格式</label>
            <el-radio-group v-model="chartConfig.exportFormat">
              <el-radio label="xls">xls</el-radio>
              <el-radio label="pdf">pdf</el-radio>
            </el-radio-group>
          </div>

          <div class="config-item">
            <label>图表标题</label>
            <el-input
              v-model="chartConfig.chartTitle"
              placeholder="业务数据统计一览"
              @input="updateChartsOnConfigChange"
            />
          </div>
        </div>
      </div>

      <!-- 右侧图表展示区域 -->
      <div class="chart-display">
        <!-- 图表工具栏 -->
        <div class="chart-toolbar">
          <div class="toolbar-left">
            <span class="chart-title">{{ getCurrentChartTitle() }}</span>
          </div>
          <div class="toolbar-right">
            <!-- 导出下拉菜单 -->
            <el-dropdown
              v-if="chartConfig.shareSettings.enabled && chartConfig.shareSettings.exportEnabled"
              @command="handleExportCommand"
              :disabled="exportLoading"
            >
              <el-button type="primary" size="small" :loading="exportLoading">
                <el-icon><Download /></el-icon>
                导出
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="png">导出为PNG图片</el-dropdown-item>
                  <el-dropdown-item command="svg">导出为SVG图片</el-dropdown-item>
                  <el-dropdown-item command="excel">导出数据为Excel</el-dropdown-item>
                  <el-dropdown-item command="pdf">导出为PDF</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 分享按钮 -->
            <el-button
              v-if="chartConfig.shareSettings.enabled && chartConfig.shareSettings.linkEnabled"
              type="success"
              size="small"
              :loading="shareLoading"
              @click="shareChartLink"
            >
              <el-icon><Share /></el-icon>
              分享
            </el-button>

            <!-- 打印按钮 - 固定显示 -->
            <el-button
              type="warning"
              size="small"
              @click="printChart"
            >
              <el-icon><Printer /></el-icon>
              打印
            </el-button>

            <!-- 全屏按钮 - 固定显示 -->
            <el-button
              type="info"
              size="small"
              @click="toggleFullscreen"
            >
              <el-icon><FullScreen /></el-icon>
              全屏
            </el-button>
          </div>
        </div>

        <!-- 图表容器 -->
        <div class="chart-wrapper" :class="{ 'fullscreen': isFullscreen }">
          <!-- 柱状图 -->
          <div
            v-if="currentChartType === 'bar'"
            ref="barChartRef"
            class="chart-container"
          ></div>

          <!-- 饼图 -->
          <div
            v-if="currentChartType === 'pie'"
            ref="pieChartRef"
            class="chart-container"
          ></div>

          <!-- 折线图 -->
          <div
            v-if="currentChartType === 'line'"
            ref="lineChartRef"
            class="chart-container"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.data-visualization-setting {
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-type-tabs {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 20px;
    min-height: 0;
  }

  .config-panel {
    width: 300px;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 20px;
    overflow-y: auto;

    .config-section {
      margin-bottom: 30px;

      h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 8px;
      }

      .config-item {
        margin-bottom: 16px;

        label {
          display: block;
          margin-bottom: 8px;
          font-size: 14px;
          color: #606266;
          font-weight: 500;
        }

        .el-select,
        .el-input-number {
          width: 100%;
        }

        .font-config {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }
      }
    }

    .color-scheme-selector {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .color-scheme-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          background-color: #f0f9ff;
        }

        &.active {
          border-color: #409eff;
          background-color: #e6f7ff;
        }

        .color-preview {
          display: flex;
          gap: 2px;

          .color-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid #e4e7ed;
          }
        }

        .scheme-name {
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }

  .chart-display {
    flex: 1;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 20px;
    height: 600px;
    display: flex;
    flex-direction: column;

    .chart-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e4e7ed;

      .toolbar-left {
        .chart-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }

      .toolbar-right {
        display: flex;
        gap: 8px;

        .el-button {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }

    .chart-wrapper {
      flex: 1;
      min-height: 0;
      position: relative;
      transition: all 0.3s ease;

      // 当工具栏不显示时，调整上边距
      &:first-child {
        margin-top: 0;
      }

      &.fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
        background: white;
        padding: 20px;

        .chart-container {
          height: calc(100vh - 40px);
        }
      }

      .chart-container {
        width: 100%;
        height: 100%;
        transition: height 0.3s ease;
      }
    }
  }
}
</style>
