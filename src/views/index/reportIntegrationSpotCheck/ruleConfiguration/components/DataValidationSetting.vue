<script setup lang="ts" name="DataValidationSetting">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type {
  DataValidationSettingRule,
  DataValidationSettingRuleForm,
  TableColumn,
  PaginationConfig
} from '../types'
import { 
  VALIDATION_FIELD_OPTIONS,
  ERROR_REMINDER_OPTIONS,
  VALIDATION_RESULT_OPTIONS,
  VALIDATION_FREQUENCY_OPTIONS,
  VALIDATION_TEMPLATE_OPTIONS
} from '../types'
import { DataValidationSettingStorage } from '../storage'

// Props
interface Props {
  tableHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  tableHeight: 400
})

// 响应式数据
const loading = ref(false)
const currentRow = ref<DataValidationSettingRule | null>(null)
const selectedRows = ref<DataValidationSettingRule[]>([])

// 表格相关
const tableRef = ref()
const tableData = ref<DataValidationSettingRule[]>([])
const filteredData = ref<DataValidationSettingRule[]>([])

// 分页配置
const pagination = reactive<PaginationConfig>({
  page: 1,
  size: 10,
  total: 0
})

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const dialogForm = ref<DataValidationSettingRuleForm>({
  validationField: '',
  validationType: '',
  errorReminder: 'bold',
  validationResult: 'xls',
  validationFrequency: 'daily',
  validationTemplate: 'finance_tax',
  ruleDetails: ''
})

// 表格列配置
const columns: TableColumn[] = [
  { prop: 'createTime', label: '创建时间' },
  { prop: 'validationField', label: '字段' },
  { prop: 'validationType', label: '校验类型' },
  { prop: 'validationFrequency', label: '校验频率' },
  { prop: 'ruleDetails', label: '规则详情' },
  { prop: 'actions', label: '操作' }
]

// 当前使用的模板ID
const currentTemplateId = ref<string | null>(null)



// 表单验证规则
const formRules = {
  validationField: [
    { required: true, message: '请选择进行校验字段', trigger: 'change' }
  ],
  validationType: [
    { required: true, message: '请输入校验类型', trigger: 'blur' }
  ],
  ruleDetails: [
    { required: true, message: '请输入规则详情', trigger: 'blur' }
  ]
}

// 当前页数据
const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filteredData.value.slice(start, end)
})

// 初始化数据
const initData = () => {
  console.log('初始化数据校验设置数据...')
  loading.value = true

  try {
    // 先检查是否有真实数据
    const existingRules = DataValidationSettingStorage.getDataValidationSettingListRaw()
    let finalData: DataValidationSettingRule[]

    if (existingRules.length > 0) {
      // 有真实数据，使用现有数据
      finalData = existingRules
      console.log('使用现有数据:', existingRules.length, '条')
    } else {
      // 没有数据，生成默认数据
      finalData = DataValidationSettingStorage.getDataValidationSettingList()
      console.log('生成默认数据:', finalData.length, '条')
    }

    if (Array.isArray(finalData) && finalData.length > 0) {
      tableData.value = finalData
      filteredData.value = [...tableData.value]
      pagination.total = filteredData.value.length
      console.log(`数据校验设置数据加载完成: ${finalData.length} 条`)
    } else {
      console.error('数据加载失败')
      tableData.value = []
      filteredData.value = []
      pagination.total = 0
    }

    // 恢复分页状态
    const savedPagination = DataValidationSettingStorage.getPaginationConfig()
    const currentTotal = pagination.total
    Object.assign(pagination, savedPagination)
    pagination.total = currentTotal

    loading.value = false
  } catch (error) {
    console.error('初始化数据校验设置数据失败:', error)
    loading.value = false
  }
}

// 新增规则
const onClickAdd = () => {
  currentRow.value = null
  dialogForm.value = {
    validationField: '',
    validationType: '',
    errorReminder: 'bold',
    validationResult: 'xls',
    validationFrequency: 'daily',
    validationTemplate: 'finance_tax',
    ruleDetails: ''
  }
  showDialogForm.value = true
}

// 使用模板
const onUseTemplate = (row: DataValidationSettingRule) => {
  currentTemplateId.value = row.id
  ElMessage.success(`已应用模板：${getTemplateLabel(row.validationTemplate)}`)
}



// 删除规则
const onDeleteRule = (row: DataValidationSettingRule) => {
  ElMessageBox.confirm('确定要删除这条数据校验设置规则吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    if (DataValidationSettingStorage.deleteDataValidationSettingRule(row.id)) {
      ElMessage.success('删除成功')
      // 如果删除的是当前模板，清除当前模板ID
      if (currentTemplateId.value === row.id) {
        currentTemplateId.value = null
      }
      initData()
    } else {
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 获取模板标签
const getTemplateLabel = (value: string) => {
  const option = VALIDATION_TEMPLATE_OPTIONS.find(opt => opt.value === value)
  return option ? option.label : value
}

// 选择变化
const onSelectionChange = (selection: DataValidationSettingRule[]) => {
  selectedRows.value = selection
}

// 批量删除
const onBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的数据')
    return
  }

  ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 条数据校验设置规则吗？`, '批量删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const ids = selectedRows.value.map(row => row.id)
    if (DataValidationSettingStorage.batchDeleteDataValidationSettingRules(ids)) {
      ElMessage.success(`成功删除 ${selectedRows.value.length} 条数据`)
      selectedRows.value = []
      initData()
    } else {
      ElMessage.error('批量删除失败')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 保存规则
const onSaveRule = async () => {
  if (!dialogFormRef.value) return

  try {
    await dialogFormRef.value.validate()
    
    if (currentRow.value) {
      // 编辑模式
      const success = DataValidationSettingStorage.updateDataValidationSettingRule(currentRow.value.id, dialogForm.value)
      if (success) {
        ElMessage.success('更新成功')
        showDialogForm.value = false
        initData()
      } else {
        ElMessage.error('更新失败')
      }
    } else {
      // 新增模式
      const newRule = DataValidationSettingStorage.addDataValidationSettingRule(dialogForm.value)
      if (newRule) {
        ElMessage.success('添加成功')
        showDialogForm.value = false
        initData()
      } else {
        ElMessage.error('添加失败')
      }
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
const onCancel = () => {
  showDialogForm.value = false
}

// 分页变化
const onPaginationChange = (val: number, type: 'page' | 'size') => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1 // 重置到第一页
  }
  
  // 保存分页状态
  DataValidationSettingStorage.savePaginationConfig(pagination)
}

// 暴露方法给父组件
defineExpose({
  initData,
  onClickAdd,
  onBatchDelete
})

// 组件挂载
onMounted(async () => {
  await nextTick()
  initData()
})
</script>

<template>
  <div class="data-validation-setting">
    <!-- 数据校验设置表格 -->
    <TableV2
      ref="tableRef"
      :defaultTableData="paginatedData"
      :columns="columns"
      :enable-toolbar="false"
      :enable-own-button="false"
      :enable-selection="true"
      :enable-index="true"
      :height="tableHeight"
      :loading="loading"
      @selection-change="onSelectionChange"
    >
      <!-- 校验类型列自定义显示 -->
      <template #validationType="{ row }">
        <el-tag
          :type="row.validationType === '非空检查' ? 'danger' :
                 row.validationType === '格式校验' ? 'warning' :
                 row.validationType === '长度限制' ? 'info' : 'success'"
        >
          {{ row.validationType }}
        </el-tag>
      </template>

      <!-- 校验频率列自定义显示 -->
      <template #validationFrequency="{ row }">
        <el-tag
          size="small"
          :type="row.validationFrequency === 'daily' ? 'success' :
                 row.validationFrequency === 'weekly' ? 'warning' : 'info'"
        >
          {{ VALIDATION_FREQUENCY_OPTIONS.find(opt => opt.value === row.validationFrequency)?.label || row.validationFrequency }}
        </el-tag>
      </template>

      <!-- 规则详情列自定义显示 -->
      <template #ruleDetails="{ row }">
        <el-tooltip :content="row.ruleDetails" placement="top" :show-after="500">
          <span class="rule-details-text">{{ row.ruleDetails }}</span>
        </el-tooltip>
      </template>

      <!-- 操作列自定义显示 -->
      <template #actions="{ row }">
        <div class="action-buttons">
          <el-button
            v-if="currentTemplateId === row.id"
            size="small"
            type="info"
            disabled
          >
            当前模板
          </el-button>
          <el-button
            v-else
            size="small"
            type="primary"
            @click="onUseTemplate(row)"
          >
            使用模板
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="onDeleteRule(row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </TableV2>

    <!-- 分页 -->
    <Pagination
      :total="pagination.total"
      :current-page="pagination.page"
      :page-size="pagination.size"
      @current-change="onPaginationChange($event, 'page')"
      @size-change="onPaginationChange($event, 'size')"
    />

    <!-- 新增/编辑对话框 -->
    <DialogComp
      :visible="showDialogForm"
      :title="currentRow ? '编辑数据校验设置' : '新增数据校验设置'"
      width="600px"
      @clickConfirm="onSaveRule"
      @clickCancel="onCancel"
      @closed="onCancel"
    >
      <template #body>
        <el-form
          ref="dialogFormRef"
          :model="dialogForm"
          :rules="formRules"
          label-width="120px"
          class="dialog-form"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="进行校验字段" prop="validationField">
                <el-select v-model="dialogForm.validationField" placeholder="请选择字段" style="width: 100%">
                  <el-option
                    v-for="option in VALIDATION_FIELD_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.label"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="校验频率" prop="validationFrequency">
                <el-select v-model="dialogForm.validationFrequency" placeholder="请选择校验频率" style="width: 100%">
                  <el-option
                    v-for="option in VALIDATION_FREQUENCY_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="校验模板" prop="validationTemplate">
                <el-select v-model="dialogForm.validationTemplate" placeholder="请选择校验模板" style="width: 100%">
                  <el-option
                    v-for="option in VALIDATION_TEMPLATE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="错误提醒" prop="errorReminder">
                <el-radio-group v-model="dialogForm.errorReminder">
                  <el-radio
                    v-for="option in ERROR_REMINDER_OPTIONS"
                    :key="option.value"
                    :label="option.value"
                  >
                    {{ option.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="校验结果" prop="validationResult">
                <el-radio-group v-model="dialogForm.validationResult">
                  <el-radio
                    v-for="option in VALIDATION_RESULT_OPTIONS"
                    :key="option.value"
                    :label="option.value"
                  >
                    {{ option.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="校验类型" prop="validationType">
                <el-input
                  v-model="dialogForm.validationType"
                  placeholder="请输入校验类型，如：非空检查、格式校验、长度限制、数值范围"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="规则详情" prop="ruleDetails">
                <el-input
                  v-model="dialogForm.ruleDetails"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入详细的校验规则说明"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </DialogComp>
  </div>
</template>

<style scoped lang="scss">
.data-validation-setting {
  .dialog-form {
    padding: 20px 0;
  }

  .rule-details-text {
    display: inline-block;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;

    .el-button {
      margin: 0;
    }
  }
}
</style>
