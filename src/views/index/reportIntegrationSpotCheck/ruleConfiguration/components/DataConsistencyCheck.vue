<script setup lang="ts" name="DataConsistencyCheck">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { 
  DataConsistencyCheckForm, 
  DataConsistencyCheckRule, 
  DataIntegrityCheckRule, 
  DataCompatibilityCheckRule 
} from '../types'
import { 
  DATA_SOURCE_OPTIONS,
  CHECK_LEVEL_OPTIONS,
  FIELD_TYPE_OPTIONS,
  OS_VERSION_OPTIONS,
  BROWSER_VERSION_OPTIONS,
  DATA_FORMAT_OPTIONS
} from '../types'
import { DataConsistencyCheckStorage } from '../storage'

// 响应式数据
const loading = ref(false)

// 表单数据
const formData = reactive<DataConsistencyCheckForm>({
  // 数据一致性检查规则列表
  consistencyRules: [
    {
      dataSource: 'temp_table',
      checkLevel: 'field_1',
      fieldFormat: '',
      toleranceRange: 20
    }
  ],
  
  // 数据完整性检查规则列表
  integrityRules: [
    {
      dataSource: 'business_table',
      checkLevel: 'field_1',
      fieldType: 'string',
      dataRange: {
        min: 0,
        max: 100
      }
    }
  ],
  
  // 数据兼容性检查规则列表
  compatibilityRules: [
    {
      dataSource: 'business_table',
      osVersion: 'windows_8',
      browserVersion: '2.3',
      apiPort: '3465',
      dataFormat: 'json'
    }
  ]
})

// 保存配置
const saveConfiguration = async () => {
  loading.value = true
  try {
    const success = DataConsistencyCheckStorage.addConfig({
      consistencyRules: formData.consistencyRules,
      integrityRules: formData.integrityRules,
      compatibilityRules: formData.compatibilityRules
    })
    
    if (success) {
      ElMessage.success('数据一致性检查配置保存成功')
      // 保存成功后保留表单数据，不重置
    } else {
      ElMessage.error('保存失败，请重试')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    loading.value = false
  }
}

// 添加一致性检查规则
const addConsistencyRule = () => {
  formData.consistencyRules.push({
    dataSource: 'temp_table',
    checkLevel: 'field_1',
    fieldFormat: '',
    toleranceRange: 20
  })
}

// 删除一致性检查规则
const removeConsistencyRule = (index: number) => {
  if (formData.consistencyRules.length > 1) {
    formData.consistencyRules.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一个一致性检查规则')
  }
}

// 添加完整性检查规则
const addIntegrityRule = () => {
  formData.integrityRules.push({
    dataSource: 'business_table',
    checkLevel: 'field_1',
    fieldType: 'string',
    dataRange: {
      min: 0,
      max: 100
    }
  })
}

// 删除完整性检查规则
const removeIntegrityRule = (index: number) => {
  if (formData.integrityRules.length > 1) {
    formData.integrityRules.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一个完整性检查规则')
  }
}

// 添加兼容性检查规则
const addCompatibilityRule = () => {
  formData.compatibilityRules.push({
    dataSource: 'business_table',
    osVersion: 'windows_8',
    browserVersion: '2.3',
    apiPort: '3465',
    dataFormat: 'json'
  })
}

// 删除兼容性检查规则
const removeCompatibilityRule = (index: number) => {
  if (formData.compatibilityRules.length > 1) {
    formData.compatibilityRules.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一个兼容性检查规则')
  }
}

// 加载保存的配置
const loadSavedConfiguration = () => {
  try {
    const savedConfigs = DataConsistencyCheckStorage.getConfigList()
    if (savedConfigs && savedConfigs.length > 0) {
      // 加载最新的配置（第一条记录）
      const latestConfig = savedConfigs[0]
      formData.consistencyRules = latestConfig.consistencyRules || [
        {
          dataSource: 'temp_table',
          checkLevel: 'field_1',
          fieldFormat: '',
          toleranceRange: 20
        }
      ]
      formData.integrityRules = latestConfig.integrityRules || [
        {
          dataSource: 'business_table',
          checkLevel: 'field_1',
          fieldType: 'string',
          dataRange: {
            min: 0,
            max: 100
          }
        }
      ]
      formData.compatibilityRules = latestConfig.compatibilityRules || [
        {
          dataSource: 'business_table',
          osVersion: 'windows_8',
          browserVersion: '2.3',
          apiPort: '3465',
          dataFormat: 'json'
        }
      ]
    }
  } catch (error) {
    console.error('加载保存的配置失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  formData.consistencyRules = [
    {
      dataSource: 'temp_table',
      checkLevel: 'field_1',
      fieldFormat: '',
      toleranceRange: 20
    }
  ]
  formData.integrityRules = [
    {
      dataSource: 'business_table',
      checkLevel: 'field_1',
      fieldType: 'string',
      dataRange: {
        min: 0,
        max: 100
      }
    }
  ]
  formData.compatibilityRules = [
    {
      dataSource: 'business_table',
      osVersion: 'windows_8',
      browserVersion: '2.3',
      apiPort: '3465',
      dataFormat: 'json'
    }
  ]
}

// 组件挂载时的初始化
onMounted(() => {
  // 加载保存的配置
  loadSavedConfiguration()
})

// 暴露给父组件的方法
defineExpose({
  saveConfiguration,
  resetForm,
  loadSavedConfiguration
})
</script>

<template>
  <div class="data-consistency-check">
    <!-- 顶部标题 -->
    <div class="check-header">
      <h3>数据一致性、完整性、兼容性检查</h3>
    </div>

    <!-- 配置表单 -->
    <div class="check-form">
      <el-form :model="formData" label-width="150px" label-position="left">
        <!-- 第一行：数据一致性检查配置 -->
        <div class="form-row">
          <div class="form-section">
            <h4>数据一致性检查配置</h4>
            <div 
              v-for="(rule, index) in formData.consistencyRules" 
              :key="`consistency-${index}`"
              class="rule-item"
            >
              <div class="rule-header">
                <span class="rule-title">规则 {{ index + 1 }}</span>
                <el-button 
                  v-if="formData.consistencyRules.length > 1"
                  type="danger" 
                  size="small" 
                  text
                  @click="removeConsistencyRule(index)"
                >
                  删除
                </el-button>
              </div>
              
              <el-form-item label="选择数据来源">
                <el-select v-model="rule.dataSource" style="width: 200px">
                  <el-option
                    v-for="option in DATA_SOURCE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item label="选择检查字段">
                <el-select v-model="rule.checkLevel" style="width: 200px">
                  <el-option
                    v-for="option in CHECK_LEVEL_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item label="定义字段格式标准">
                <el-input 
                  v-model="rule.fieldFormat" 
                  placeholder="如：年-月-日期"
                  style="width: 200px"
                />
              </el-form-item>
              
              <el-form-item label="可接受的误差范围">
                <el-input-number 
                  v-model="rule.toleranceRange" 
                  :min="0" 
                  :max="100"
                  style="width: 200px"
                />
                <span style="margin-left: 8px;">%</span>
              </el-form-item>
            </div>
            
            <div class="add-rule-btn">
              <el-button type="primary" text @click="addConsistencyRule">
                添加规则
              </el-button>
            </div>
          </div>
        </div>

        <!-- 第二行：数据完整性检查配置 -->
        <div class="form-row">
          <div class="form-section">
            <h4>数据完整性检查配置</h4>
            <div
              v-for="(rule, index) in formData.integrityRules"
              :key="`integrity-${index}`"
              class="rule-item"
            >
              <div class="rule-header">
                <span class="rule-title">规则 {{ index + 1 }}</span>
                <el-button
                  v-if="formData.integrityRules.length > 1"
                  type="danger"
                  size="small"
                  text
                  @click="removeIntegrityRule(index)"
                >
                  删除
                </el-button>
              </div>

              <el-form-item label="选择数据来源">
                <el-select v-model="rule.dataSource" style="width: 200px">
                  <el-option
                    v-for="option in DATA_SOURCE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="选择必填字段">
                <el-select v-model="rule.checkLevel" style="width: 200px">
                  <el-option
                    v-for="option in CHECK_LEVEL_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="字段类型限制">
                <el-select v-model="rule.fieldType" style="width: 200px">
                  <el-option
                    v-for="option in FIELD_TYPE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="数据界限范围">
                <div style="display: flex; align-items: center; gap: 8px;">
                  <el-input-number
                    v-model="rule.dataRange.min"
                    :max="rule.dataRange.max - 1"
                    :step="1"
                    :precision="0"
                    controls-position="right"
                    style="width: 120px"
                  />
                  <span>-</span>
                  <el-input-number
                    v-model="rule.dataRange.max"
                    :min="rule.dataRange.min + 1"
                    :step="1"
                    :precision="0"
                    controls-position="right"
                    style="width: 120px"
                  />
                </div>
              </el-form-item>
            </div>

            <div class="add-rule-btn">
              <el-button type="primary" text @click="addIntegrityRule">
                添加规则
              </el-button>
            </div>
          </div>
        </div>

        <!-- 第三行：数据兼容性检查配置 -->
        <div class="form-row">
          <div class="form-section">
            <h4>数据兼容性检查配置</h4>
            <div
              v-for="(rule, index) in formData.compatibilityRules"
              :key="`compatibility-${index}`"
              class="rule-item"
            >
              <div class="rule-header">
                <span class="rule-title">规则 {{ index + 1 }}</span>
                <el-button
                  v-if="formData.compatibilityRules.length > 1"
                  type="danger"
                  size="small"
                  text
                  @click="removeCompatibilityRule(index)"
                >
                  删除
                </el-button>
              </div>

              <el-form-item label="选择数据来源">
                <el-select v-model="rule.dataSource" style="width: 200px">
                  <el-option
                    v-for="option in DATA_SOURCE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="操作系统版本">
                <el-select v-model="rule.osVersion" style="width: 200px">
                  <el-option
                    v-for="option in OS_VERSION_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="浏览器版本">
                <el-select v-model="rule.browserVersion" style="width: 200px">
                  <el-option
                    v-for="option in BROWSER_VERSION_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="API接口">
                <el-input
                  v-model="rule.apiPort"
                  placeholder="3465"
                  style="width: 200px"
                />
              </el-form-item>

              <el-form-item label="数据文件格式">
                <el-select v-model="rule.dataFormat" style="width: 200px">
                  <el-option
                    v-for="option in DATA_FORMAT_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </div>

            <div class="add-rule-btn">
              <el-button type="primary" text @click="addCompatibilityRule">
                添加规则
              </el-button>
            </div>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<style scoped>
.data-consistency-check {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.check-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

.check-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.check-form {
  flex: 1;
  overflow-y: auto;
}

.form-row {
  display: flex;
  gap: 30px;
  margin-bottom: 40px;
}

.form-section {
  flex: 1;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.form-section h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 10px;
}

.rule-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: #ffffff;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.rule-title {
  font-weight: 600;
  color: #606266;
}

.add-rule-btn {
  text-align: center;
  padding: 10px 0;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}
</style>
