<script setup lang="ts" name="BusinessTableIdentification">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type {
  BusinessTableIdentificationRule,
  BusinessTableIdentificationRuleForm,
  FilterCondition,
  TableColumn,
  PaginationConfig
} from '../types'
import { PRESET_FIELD_OPTIONS, LOGIC_OPERATOR_OPTIONS } from '../types'
import { BusinessTableIdentificationStorage } from '../storage'

// Props
interface Props {
  tableHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  tableHeight: 400
})

// 响应式数据
const loading = ref(false)
const currentRow = ref<BusinessTableIdentificationRule | null>(null)
const selectedRows = ref<BusinessTableIdentificationRule[]>([])

// 表格相关
const tableRef = ref()
const tableData = ref<BusinessTableIdentificationRule[]>([])
const filteredData = ref<BusinessTableIdentificationRule[]>([])

// 分页配置
const pagination = reactive<PaginationConfig>({
  page: 1,
  size: 10,
  total: 0
})

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const dialogForm = ref<BusinessTableIdentificationRuleForm>({
  participatingField: '',
  ruleDescription: '',
  filterConditions: []
})

// 表格列配置
const columns: TableColumn[] = [
  { prop: 'createTime', label: '创建时间' },
  { prop: 'participatingField', label: '参与的字段' },
  { prop: 'ruleDescription', label: '规则描述' },
  { prop: 'actions', label: '操作' }
]

// 添加筛选条件
const addFilterCondition = () => {
  dialogForm.value.filterConditions.push(createDefaultFilterCondition())
}

// 删除筛选条件
const removeFilterCondition = (index: number) => {
  if (dialogForm.value.filterConditions.length > 1) {
    dialogForm.value.filterConditions.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一个筛选条件')
  }
}

// 表单验证规则
const formRules = {
  participatingField: [
    { required: true, message: '请选择参与的字段', trigger: 'change' }
  ],
  ruleDescription: [
    { required: true, message: '请输入规则描述', trigger: 'blur' }
  ]
}

// 当前页数据
const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filteredData.value.slice(start, end)
})

// 初始化数据
const initData = () => {
  console.log('初始化标识业务表数据...')
  loading.value = true

  try {
    // 清除旧数据并重新生成（确保数据结构正确）
    BusinessTableIdentificationStorage.clearAllData()

    // 生成新的默认数据
    const finalData = BusinessTableIdentificationStorage.getBusinessTableIdentificationList()
    console.log('生成新的默认数据:', finalData.length, '条')

    if (Array.isArray(finalData) && finalData.length > 0) {
      tableData.value = finalData
      filteredData.value = [...tableData.value]
      pagination.total = filteredData.value.length
      console.log(`标识业务表数据加载完成: ${finalData.length} 条`)
      console.log('第一条数据示例:', finalData[0]) // 调试信息
    } else {
      console.error('数据加载失败')
      tableData.value = []
      filteredData.value = []
      pagination.total = 0
    }

    // 恢复分页状态
    const savedPagination = BusinessTableIdentificationStorage.getPaginationConfig()
    const currentTotal = pagination.total
    Object.assign(pagination, savedPagination)
    pagination.total = currentTotal

    loading.value = false
  } catch (error) {
    console.error('初始化标识业务表数据失败:', error)
    loading.value = false
  }
}

// 创建默认筛选条件
const createDefaultFilterCondition = (): FilterCondition => ({
  id: `condition_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
  logicOperator: 'and',
  filterRange: ''
})

// 新增规则
const onClickAdd = () => {
  currentRow.value = null
  dialogForm.value = {
    participatingField: '',
    ruleDescription: '',
    filterConditions: [createDefaultFilterCondition()]
  }
  showDialogForm.value = true
}



// 删除规则
const onDeleteRule = (row: BusinessTableIdentificationRule) => {
  ElMessageBox.confirm('确定要删除这条标识业务表规则吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    if (BusinessTableIdentificationStorage.deleteBusinessTableIdentificationRule(row.id)) {
      ElMessage.success('删除成功')
      initData()
    } else {
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 选择变化
const onSelectionChange = (selection: BusinessTableIdentificationRule[]) => {
  selectedRows.value = selection
}

// 批量删除
const onBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的数据')
    return
  }

  ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 条标识业务表规则吗？`, '批量删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const ids = selectedRows.value.map(row => row.id)
    if (BusinessTableIdentificationStorage.batchDeleteBusinessTableIdentificationRules(ids)) {
      ElMessage.success(`成功删除 ${selectedRows.value.length} 条数据`)
      selectedRows.value = []
      initData()
    } else {
      ElMessage.error('批量删除失败')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 保存规则
const onSaveRule = async () => {
  if (!dialogFormRef.value) return

  try {
    await dialogFormRef.value.validate()
    
    if (currentRow.value) {
      // 编辑模式
      const success = BusinessTableIdentificationStorage.updateBusinessTableIdentificationRule(currentRow.value.id, dialogForm.value)
      if (success) {
        ElMessage.success('更新成功')
        showDialogForm.value = false
        initData()
      } else {
        ElMessage.error('更新失败')
      }
    } else {
      // 新增模式
      const newRule = BusinessTableIdentificationStorage.addBusinessTableIdentificationRule(dialogForm.value)
      if (newRule) {
        ElMessage.success('添加成功')
        showDialogForm.value = false
        initData()
      } else {
        ElMessage.error('添加失败')
      }
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
const onCancel = () => {
  showDialogForm.value = false
}

// 分页变化
const onPaginationChange = (val: number, type: 'page' | 'size') => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1 // 重置到第一页
  }
  
  // 保存分页状态
  BusinessTableIdentificationStorage.savePaginationConfig(pagination)
}

// 暴露方法给父组件
defineExpose({
  initData,
  onClickAdd,
  onBatchDelete
})

// 组件挂载
onMounted(async () => {
  await nextTick()
  initData()
})
</script>

<template>
  <div class="business-table-identification">
    <!-- 标识业务表表格 -->
    <TableV2
      ref="tableRef"
      :defaultTableData="paginatedData"
      :columns="columns"
      :enable-toolbar="false"
      :enable-own-button="false"
      :enable-selection="true"
      :enable-index="true"
      :height="tableHeight"
      :loading="loading"
      @selection-change="onSelectionChange"
    >
      <!-- 参与的字段列自定义显示 -->
      <template #participatingField="{ row }">
        <el-tag
          size="small"
          type="primary"
        >
          {{ row.participatingField }}
        </el-tag>
      </template>

      <!-- 规则描述列自定义显示 -->
      <template #ruleDescription="{ row }">
        <el-tooltip :content="row.ruleDescription" placement="top" :show-after="500">
          <span class="rule-description-text">{{ row.ruleDescription }}</span>
        </el-tooltip>
      </template>

      <!-- 操作列自定义显示 -->
      <template #actions="{ row }">
        <div class="action-buttons">
          <el-button
            size="small"
            type="danger"
            @click="onDeleteRule(row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </TableV2>

    <!-- 分页 -->
    <Pagination
      :total="pagination.total"
      :current-page="pagination.page"
      :page-size="pagination.size"
      @current-change="onPaginationChange($event, 'page')"
      @size-change="onPaginationChange($event, 'size')"
    />

    <!-- 新增/编辑对话框 -->
    <DialogComp
      :visible="showDialogForm"
      :title="currentRow ? '编辑标识业务表规则' : '新增标识业务表规则'"
      width="600px"
      @clickConfirm="onSaveRule"
      @clickCancel="onCancel"
      @closed="onCancel"
    >
      <template #body>
        <el-form
          ref="dialogFormRef"
          :model="dialogForm"
          :rules="formRules"
          label-width="120px"
          class="dialog-form"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="参与的字段" prop="participatingField">
                <el-select
                  v-model="dialogForm.participatingField"
                  placeholder="请选择参与的字段"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in PRESET_FIELD_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.label"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 筛选条件区域 -->
          <el-row>
            <el-col :span="24">
              <el-form-item label="筛选条件">
                <div class="filter-conditions-container">
                  <div
                    v-for="(condition, index) in dialogForm.filterConditions"
                    :key="condition.id"
                    class="filter-condition-item"
                  >
                    <div class="condition-header">
                      <span class="condition-label">筛选条件{{ index + 1 }}：</span>
                      <el-button
                        v-if="dialogForm.filterConditions.length > 1"
                        size="small"
                        type="danger"
                        text
                        @click="removeFilterCondition(index)"
                      >
                        删除
                      </el-button>
                    </div>

                    <div class="condition-content">
                      <el-row :gutter="12">
                        <el-col :span="8">
                          <el-form-item label="逻辑条件">
                            <el-radio-group v-model="condition.logicOperator" size="small">
                              <el-radio
                                v-for="option in LOGIC_OPERATOR_OPTIONS"
                                :key="option.value"
                                :label="option.value"
                              >
                                {{ option.label }}
                              </el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </el-col>
                        <el-col :span="16">
                          <el-form-item label="筛选范围">
                            <el-input
                              v-model="condition.filterRange"
                              placeholder="如：大于 120、等于 '男'、包含 '北京'"
                              size="small"
                              style="width: 100%"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                  </div>

                  <el-button
                    type="primary"
                    size="small"
                    @click="addFilterCondition"
                    style="margin-top: 10px;"
                  >
                    添加逻辑
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="规则描述" prop="ruleDescription">
                <el-input
                  v-model="dialogForm.ruleDescription"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入规则描述，如：年龄字段数值大于120或小于0，且时间范围在2024年..."
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </DialogComp>
  </div>
</template>

<style scoped lang="scss">
.business-table-identification {
  .dialog-form {
    padding: 20px 0;
  }

  .participating-fields {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;

    .el-tag {
      margin: 0;
    }
  }

  .rule-description-text {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;

    .el-button {
      margin: 0;
    }
  }

  .filter-conditions-container {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 16px;
    background-color: #fafafa;

    .filter-condition-item {
      margin-bottom: 16px;
      padding: 12px;
      background-color: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .condition-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .condition-label {
          font-weight: 500;
          color: #303133;
        }
      }

      .condition-content {
        .el-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
