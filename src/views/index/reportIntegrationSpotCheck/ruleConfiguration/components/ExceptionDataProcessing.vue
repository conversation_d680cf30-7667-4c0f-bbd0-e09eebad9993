<script setup lang="ts" name="ExceptionDataProcessing">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { 
  ExceptionDataProcessingForm, 
  ExceptionDefinitionRule, 
  ProcessingLogicRule,
  NotificationReceiver
} from '../types'
import { 
  EXCEPTION_DATA_SOURCE_OPTIONS,
  DETECTION_FIELD_OPTIONS,
  EXCEPTION_TYPE_OPTIONS,
  AUTO_CORRECTION_OPTIONS,
  TRIGGER_CONDITION_OPTIONS,
  EXECUTION_ACTION_OPTIONS
} from '../types'
import { ExceptionDataProcessingStorage } from '../storage'
import { generateMockNotificationReceivers } from '../mockData'

// 响应式数据
const loading = ref(false)

// 表单数据
const formData = reactive<ExceptionDataProcessingForm>({
  // 异常定义规则列表
  exceptionDefinitions: [
    {
      dataSource: 'temp_table',
      detectionFields: ['field_1'],
      exceptionType: 'numeric_exception',
      exceptionDescription: ''
    }
  ],
  
  // 处理逻辑规则列表
  processingLogics: [
    {
      autoCorrection: 'enabled',
      triggerCondition: 'immediate',
      triggerDescription: '',
      executionAction: 'mark_exception',
      actionDescription: ''
    }
  ],
  
  // 可选通知接收者
  availableReceivers: generateMockNotificationReceivers(),
  
  // 已选通知接收者
  selectedReceivers: []
})

// 保存配置
const saveConfiguration = async () => {
  loading.value = true
  try {
    const success = ExceptionDataProcessingStorage.addConfig({
      exceptionDefinitions: formData.exceptionDefinitions,
      processingLogics: formData.processingLogics,
      availableReceivers: formData.availableReceivers,
      selectedReceivers: formData.selectedReceivers
    })
    
    if (success) {
      ElMessage.success('异常数据处理配置保存成功')
      // 保存成功后保留表单数据，不重置
    } else {
      ElMessage.error('保存失败，请重试')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    loading.value = false
  }
}

// 添加异常定义规则
const addExceptionDefinition = () => {
  formData.exceptionDefinitions.push({
    dataSource: 'temp_table',
    detectionFields: ['field_1'],
    exceptionType: 'numeric_exception',
    exceptionDescription: ''
  })
}

// 删除异常定义规则
const removeExceptionDefinition = (index: number) => {
  if (formData.exceptionDefinitions.length > 1) {
    formData.exceptionDefinitions.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一个异常定义规则')
  }
}

// 添加处理逻辑规则
const addProcessingLogic = () => {
  formData.processingLogics.push({
    autoCorrection: 'enabled',
    triggerCondition: 'immediate',
    triggerDescription: '',
    executionAction: 'mark_exception',
    actionDescription: ''
  })
}

// 删除处理逻辑规则
const removeProcessingLogic = (index: number) => {
  if (formData.processingLogics.length > 1) {
    formData.processingLogics.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一个处理逻辑规则')
  }
}

// 选择通知接收者（从可选移动到已选）
const selectReceiver = (receiver: NotificationReceiver) => {
  const index = formData.availableReceivers.findIndex(r => r.id === receiver.id)
  if (index !== -1) {
    formData.availableReceivers.splice(index, 1)
    formData.selectedReceivers.push(receiver)
  }
}

// 移除通知接收者（从已选移动到可选）
const removeReceiver = (receiver: NotificationReceiver) => {
  const index = formData.selectedReceivers.findIndex(r => r.id === receiver.id)
  if (index !== -1) {
    formData.selectedReceivers.splice(index, 1)
    formData.availableReceivers.push(receiver)
  }
}

// 加载保存的配置
const loadSavedConfiguration = () => {
  try {
    const savedConfigs = ExceptionDataProcessingStorage.getConfigList()
    if (savedConfigs && savedConfigs.length > 0) {
      // 加载最新的配置（第一条记录）
      const latestConfig = savedConfigs[0]
      formData.exceptionDefinitions = latestConfig.exceptionDefinitions || [
        {
          dataSource: 'temp_table',
          detectionFields: ['field_1'],
          exceptionType: 'numeric_exception',
          exceptionDescription: ''
        }
      ]
      formData.processingLogics = latestConfig.processingLogics || [
        {
          autoCorrection: 'enabled',
          triggerCondition: 'immediate',
          triggerDescription: '',
          executionAction: 'mark_exception',
          actionDescription: ''
        }
      ]
      formData.availableReceivers = latestConfig.availableReceivers || generateMockNotificationReceivers()
      formData.selectedReceivers = latestConfig.selectedReceivers || []
    }
  } catch (error) {
    console.error('加载保存的配置失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  formData.exceptionDefinitions = [
    {
      dataSource: 'temp_table',
      detectionFields: ['field_1'],
      exceptionType: 'numeric_exception',
      exceptionDescription: ''
    }
  ]
  formData.processingLogics = [
    {
      autoCorrection: 'enabled',
      triggerCondition: 'immediate',
      triggerDescription: '',
      executionAction: 'mark_exception',
      actionDescription: ''
    }
  ]
  formData.availableReceivers = generateMockNotificationReceivers()
  formData.selectedReceivers = []
}

// 组件挂载时的初始化
onMounted(() => {
  // 加载保存的配置
  loadSavedConfiguration()
})

// 暴露给父组件的方法
defineExpose({
  saveConfiguration,
  resetForm,
  loadSavedConfiguration
})
</script>

<template>
  <div class="exception-data-processing">
    <!-- 顶部标题 -->
    <div class="processing-header">
      <h3>异常数据处理</h3>
    </div>

    <!-- 配置表单 -->
    <div class="processing-form">
      <el-form :model="formData" label-width="120px" label-position="left">
        <!-- 第一行：定义数据异常 -->
        <div class="form-row">
          <div class="form-section">
            <h4>定义数据异常</h4>
            <div 
              v-for="(rule, index) in formData.exceptionDefinitions" 
              :key="`exception-${index}`"
              class="rule-item"
            >
              <div class="rule-header">
                <span class="rule-title">规则 {{ index + 1 }}</span>
                <el-button 
                  v-if="formData.exceptionDefinitions.length > 1"
                  type="danger" 
                  size="small" 
                  text
                  @click="removeExceptionDefinition(index)"
                >
                  删除
                </el-button>
              </div>
              
              <el-form-item label="选择数据来源">
                <el-select v-model="rule.dataSource" style="width: 200px">
                  <el-option
                    v-for="option in EXCEPTION_DATA_SOURCE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item label="选择检测字段">
                <el-select 
                  v-model="rule.detectionFields" 
                  multiple 
                  style="width: 200px"
                  placeholder="请选择检测字段"
                >
                  <el-option
                    v-for="option in DETECTION_FIELD_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item label="定义数据异常">
                <el-select v-model="rule.exceptionType" style="width: 200px">
                  <el-option
                    v-for="option in EXCEPTION_TYPE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item label="">
                <el-input 
                  v-model="rule.exceptionDescription" 
                  type="textarea"
                  :rows="2"
                  placeholder="请输入定义详情"
                  style="width: 200px"
                />
              </el-form-item>
            </div>
            
            <div class="add-rule-btn">
              <el-button type="primary" text @click="addExceptionDefinition">
                添加定义
              </el-button>
            </div>
          </div>
        </div>

        <!-- 第二行：处理数据异常 -->
        <div class="form-row">
          <div class="form-section">
            <h4>处理数据异常</h4>
            <div
              v-for="(rule, index) in formData.processingLogics"
              :key="`processing-${index}`"
              class="rule-item"
            >
              <div class="rule-header">
                <span class="rule-title">规则 {{ index + 1 }}</span>
                <el-button
                  v-if="formData.processingLogics.length > 1"
                  type="danger"
                  size="small"
                  text
                  @click="removeProcessingLogic(index)"
                >
                  删除
                </el-button>
              </div>

              <el-form-item label="自动修正">
                <el-select v-model="rule.autoCorrection" style="width: 200px">
                  <el-option
                    v-for="option in AUTO_CORRECTION_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="触发条件">
                <el-input
                  v-model="rule.triggerDescription"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入触发条件"
                  style="width: 200px"
                />
              </el-form-item>

              <el-form-item label="执行操作">
                <el-select v-model="rule.executionAction" style="width: 200px">
                  <el-option
                    v-for="option in EXECUTION_ACTION_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="">
                <el-input
                  v-model="rule.actionDescription"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入执行操作详情"
                  style="width: 200px"
                />
              </el-form-item>
            </div>

            <div class="add-rule-btn">
              <el-button type="primary" text @click="addProcessingLogic">
                添加处理逻辑
              </el-button>
            </div>
          </div>
        </div>

        <!-- 第三行：通知数据异常 -->
        <div class="form-row">
          <div class="form-section notification-section">
            <h4>通知数据异常</h4>
            <div class="notification-content">
              <div class="receiver-selection">
                <div class="receiver-box">
                  <div class="box-header">选择通知接收者</div>
                  <div class="receiver-list available-list">
                    <div
                      v-for="receiver in formData.availableReceivers"
                      :key="receiver.id"
                      class="receiver-item"
                      @click="selectReceiver(receiver)"
                    >
                      <div class="receiver-info">
                        <div class="receiver-name">{{ receiver.name }}</div>
                        <div class="receiver-dept">{{ receiver.department }}</div>
                        <div class="receiver-role">{{ receiver.role }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="receiver-box">
                  <div class="box-header">已选</div>
                  <div class="receiver-list selected-list">
                    <div
                      v-for="receiver in formData.selectedReceivers"
                      :key="receiver.id"
                      class="receiver-item selected"
                      @click="removeReceiver(receiver)"
                    >
                      <div class="receiver-info">
                        <div class="receiver-name">{{ receiver.name }}</div>
                        <div class="receiver-dept">{{ receiver.department }}</div>
                        <div class="receiver-role">{{ receiver.role }}</div>
                      </div>
                      <div class="remove-btn">删除</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<style scoped>
.exception-data-processing {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.processing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

.processing-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.processing-form {
  flex: 1;
  overflow-y: auto;
}

.form-row {
  display: flex;
  gap: 30px;
  margin-bottom: 40px;
}

.form-section {
  flex: 1;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.form-section h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 10px;
}

.rule-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: #ffffff;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.rule-title {
  font-weight: 600;
  color: #606266;
}

.add-rule-btn {
  text-align: center;
  padding: 10px 0;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.notification-section {
  min-height: 400px;
}

.notification-content {
  width: 100%;
}

.receiver-selection {
  display: flex;
  gap: 20px;
  height: 300px;
}

.receiver-box {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
}

.box-header {
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  font-weight: 600;
  color: #606266;
  text-align: center;
}

.receiver-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.receiver-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.receiver-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
}

.receiver-item.selected {
  background: #f0f9ff;
  border-color: #409eff;
}

.receiver-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.receiver-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.receiver-dept {
  color: #606266;
  font-size: 12px;
}

.receiver-role {
  color: #909399;
  font-size: 12px;
}

.remove-btn {
  position: absolute;
  top: 5px;
  right: 8px;
  color: #f56c6c;
  font-size: 12px;
}
</style>
