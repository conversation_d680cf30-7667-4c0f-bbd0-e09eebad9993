<script setup lang="ts" name="DuplicateDataSetting">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { DuplicateDataSettingRule, DuplicateDataSettingRuleForm } from '../types'
import { 
  RULE_CONFIG_TYPE_OPTIONS,
  CLASSIFICATION_METHOD_OPTIONS,
  SORT_ORDER_OPTIONS,
  VIEW_TYPE_OPTIONS,
  DUPLICATE_EXPORT_FORMAT_OPTIONS,
  PAGE_SIZE_OPTIONS,
  PAPER_ORIENTATION_OPTIONS,
  PROGRESS_NOTIFICATION_OPTIONS,
  PROGRESS_UPDATE_FREQUENCY_OPTIONS,
  VALIDATION_STANDARD_OPTIONS,
  VALIDATION_PERIOD_OPTIONS,
  PRESET_FIELD_OPTIONS
} from '../types'
import { DuplicateDataSettingStorage } from '../storage'

// 响应式数据
const loading = ref(false)

// 表单数据
const formData = reactive<DuplicateDataSettingRuleForm>({
  // 报表类型
  reportType: 'business',

  // 1. 重复数据筛选设置
  filterFields: [],
  ruleConfigType: 'complete_match',
  weightThreshold: 30,
  
  // 2. 重复数据分类设置
  classificationMethod: 'date',
  customClassificationRule: '',
  
  // 3. 重复数据排序设置
  sortField: '',
  sortOrder: 'asc',
  
  // 4. 重复数据展示规则设置
  displayFields: [],
  filterConditions: [],
  viewType: 'pie',
  
  // 5. 重复审核规则设置
  colorSelection: {
    red: false,
    blue: false
  },
  regionPercentage: 30,
  
  // 6. 重复数据导出设置
  exportFormat: 'xls',
  exportFileSelection: '',
  
  // 7. 重复数据打印设置
  printMargin: 15,
  pageSize: 'A4',
  paperOrientation: 'portrait',
  
  // 8. 重复数据分享设置
  shareChannels: {
    email: false,
    link: false,
    browser: false,
    download: false,
    edit: false
  },
  sharePermissions: '',
  validPeriod: '1d',
  
  // 9. 重复数据处理设置
  processingConditions: [],
  processingRule: '',
  processingTemplate: '',
  
  // 10. 重复数据修复进度跟踪设置
  progressUpdateFrequency: 'daily',
  progressNotificationMethod: 'popup',
  
  // 11. 重复数据修复结果跟踪设置
  validationSelection: 'business',
  validationOptions: {
    linkValidation: false,
    performanceAnalysis: false,
    basicInfo: false
  },
  validationPeriodSetting: '1d'
})

// 字段选项
const fieldOptions = computed(() => PRESET_FIELD_OPTIONS)

// 报表类型选项
const reportTypeOptions = [
  { label: '业务表', value: 'business' },
  { label: '临时表', value: 'temporary' }
]

// 过滤条件选项
const filterConditionOptions = [
  { label: '条件A', value: 'condition_a' },
  { label: '条件B', value: 'condition_b' },
  { label: '条件C', value: 'condition_c' },
  { label: '条件D', value: 'condition_d' }
]

// 处理条件选项
const processingConditionOptions = [
  { label: '执行条件1', value: 'exec_condition_1' },
  { label: '执行条件2', value: 'exec_condition_2' },
  { label: '执行条件3', value: 'exec_condition_3' }
]

// 保存配置
const saveConfiguration = async () => {
  try {
    loading.value = true
    
    // 验证必填字段
    if (formData.filterFields.length === 0) {
      ElMessage.warning('请选择筛选字段')
      return
    }
    
    if (formData.displayFields.length === 0) {
      ElMessage.warning('请选择展示字段')
      return
    }

    // 创建新规则
    const newRule: DuplicateDataSettingRule = {
      id: `duplicate_data_setting_${Date.now()}`,
      createTime: new Date().toISOString().replace('T', ' ').split('.')[0],
      ...formData
    }

    // 保存到本地存储
    const success = DuplicateDataSettingStorage.addRule(newRule)
    
    if (success) {
      ElMessage.success('重复数据设置保存成功')
      // 不重置表单，保留用户输入的参数
    } else {
      ElMessage.error('保存失败，请重试')
    }
  } catch (error) {
    console.error('保存重复数据设置失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    reportType: 'business',
    filterFields: [],
    ruleConfigType: 'complete_match',
    weightThreshold: 30,
    classificationMethod: 'date',
    customClassificationRule: '',
    sortField: '',
    sortOrder: 'asc',
    displayFields: [],
    filterConditions: [],
    viewType: 'pie',
    colorSelection: {
      red: false,
      blue: false
    },
    regionPercentage: 30,
    exportFormat: 'xls',
    exportFileSelection: '',
    printMargin: 15,
    pageSize: 'A4',
    paperOrientation: 'portrait',
    shareChannels: {
      email: false,
      link: false,
      browser: false,
      download: false,
      edit: false
    },
    sharePermissions: '',
    validPeriod: '1d',
    processingConditions: [],
    processingRule: '',
    processingTemplate: '',
    progressUpdateFrequency: 'daily',
    progressNotificationMethod: 'popup',
    validationSelection: 'business',
    validationOptions: {
      linkValidation: false,
      performanceAnalysis: false,
      basicInfo: false
    },
    validationPeriodSetting: '1d'
  })
}

// 暴露方法给父组件
const onClickAdd = () => {
  saveConfiguration()
}

// 加载保存的配置
const loadSavedConfiguration = () => {
  try {
    const savedRules = DuplicateDataSettingStorage.getRuleList()
    if (savedRules && savedRules.length > 0) {
      // 加载最新的配置（第一条记录）
      const latestRule = savedRules[0]
      Object.assign(formData, {
        reportType: latestRule.reportType || 'business',
        filterFields: latestRule.filterFields || [],
        ruleConfigType: latestRule.ruleConfigType || 'complete_match',
        weightThreshold: latestRule.weightThreshold || 30,
        classificationMethod: latestRule.classificationMethod || 'date',
        customClassificationRule: latestRule.customClassificationRule || '',
        sortField: latestRule.sortField || '',
        sortOrder: latestRule.sortOrder || 'asc',
        displayFields: latestRule.displayFields || [],
        filterConditions: latestRule.filterConditions || [],
        viewType: latestRule.viewType || 'pie',
        colorSelection: latestRule.colorSelection || { red: false, blue: false },
        regionPercentage: latestRule.regionPercentage || 30,
        exportFormat: latestRule.exportFormat || 'xls',
        exportFileSelection: latestRule.exportFileSelection || '',
        printMargin: latestRule.printMargin || 15,
        pageSize: latestRule.pageSize || 'A4',
        paperOrientation: latestRule.paperOrientation || 'portrait',
        shareChannels: latestRule.shareChannels || {
          email: false,
          link: false,
          browser: false,
          download: false,
          edit: false
        },
        sharePermissions: latestRule.sharePermissions || '',
        validPeriod: latestRule.validPeriod || '1d',
        processingConditions: latestRule.processingConditions || [],
        processingRule: latestRule.processingRule || '',
        processingTemplate: latestRule.processingTemplate || '',
        progressUpdateFrequency: latestRule.progressUpdateFrequency || 'daily',
        progressNotificationMethod: latestRule.progressNotificationMethod || 'popup',
        validationSelection: latestRule.validationSelection || 'business',
        validationOptions: latestRule.validationOptions || {
          linkValidation: false,
          performanceAnalysis: false,
          basicInfo: false
        },
        validationPeriodSetting: latestRule.validationPeriodSetting || '1d'
      })
    }
  } catch (error) {
    console.error('加载保存的配置失败:', error)
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 加载保存的配置
  loadSavedConfiguration()
})

// 暴露给父组件的方法
defineExpose({
  onClickAdd,
  resetForm,
  loadSavedConfiguration
})
</script>

<template>
  <div class="duplicate-data-setting">
    <!-- 顶部标题 -->
    <div class="setting-header">
      <h3>重复数据设置</h3>
    </div>

    <!-- 配置表单 -->
    <div class="setting-form">
      <el-form :model="formData" label-width="180px" label-position="left">
        <!-- 报表类型选择 -->
        <div class="report-type-section">
          <el-form-item label="报表类型">
            <el-select v-model="formData.reportType" style="width: 200px">
              <el-option
                v-for="option in reportTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </div>

        <!-- 第一行：重复数据筛选设置、重复审核规则设置、重复数据处理设置 -->
        <div class="form-row">
          <!-- 1. 重复数据筛选设置 -->
          <div class="form-section">
            <h4>1. 重复数据筛选设置</h4>
            
            <el-form-item label="筛选字段选择">
              <el-select
                v-model="formData.filterFields"
                multiple
                placeholder="请选择筛选字段"
                style="width: 200px"
              >
                <el-option
                  v-for="option in fieldOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="规则配置类型">
              <el-select v-model="formData.ruleConfigType" style="width: 200px">
                <el-option
                  v-for="option in RULE_CONFIG_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="重复权重值">
              <div class="weight-slider">
                <el-slider
                  v-model="formData.weightThreshold"
                  :min="0"
                  :max="100"
                  show-input
                  style="width: 200px"
                />
              </div>
            </el-form-item>
          </div>

          <!-- 5. 重复审核规则设置 -->
          <div class="form-section">
            <h4>5. 重复审核规则设置</h4>
            
            <el-form-item label="颜色选择">
              <div class="color-selection">
                <el-checkbox v-model="formData.colorSelection.red">
                  <span class="color-option red">红色</span>
                </el-checkbox>
                <el-checkbox v-model="formData.colorSelection.blue">
                  <span class="color-option blue">蓝色</span>
                </el-checkbox>
              </div>
            </el-form-item>

            <el-form-item label="重复审核区间分分">
              <div class="percentage-input">
                <el-input-number
                  v-model="formData.regionPercentage"
                  :min="0"
                  :max="100"
                  style="width: 120px"
                />
                <span>%</span>
                <el-button type="text" style="margin-left: 10px">添加</el-button>
              </div>
            </el-form-item>
          </div>

          <!-- 9. 重复数据处理设置 -->
          <div class="form-section">
            <h4>9. 重复数据处理设置</h4>
            
            <el-form-item label="执行条件">
              <el-select
                v-model="formData.processingConditions"
                multiple
                placeholder="请选择执行条件"
                style="width: 200px"
              >
                <el-option
                  v-for="option in processingConditionOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="则生成">
              <el-input
                v-model="formData.processingRule"
                placeholder="请输入处理规则"
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item label="处理设置模板">
              <el-input
                v-model="formData.processingTemplate"
                placeholder="请输入处理模板"
                style="width: 200px"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 第二行：重复数据分类设置、重复数据导出设置、重复数据修复进度跟踪设置 -->
        <div class="form-row">
          <!-- 2. 重复数据分类设置 -->
          <div class="form-section">
            <h4>2. 重复数据分类设置</h4>

            <el-form-item label="分类依据选择">
              <el-select v-model="formData.classificationMethod" style="width: 200px">
                <el-option
                  v-for="option in CLASSIFICATION_METHOD_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="设置条件式分类">
              <el-input
                v-model="formData.customClassificationRule"
                placeholder="请输入分类规则"
                style="width: 200px"
              />
            </el-form-item>
          </div>

          <!-- 6. 重复数据导出设置 -->
          <div class="form-section">
            <h4>6. 重复数据导出设置</h4>

            <el-form-item label="导出格式选择">
              <el-radio-group v-model="formData.exportFormat">
                <el-radio
                  v-for="option in DUPLICATE_EXPORT_FORMAT_OPTIONS"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="导出文件名选择">
              <el-input
                v-model="formData.exportFileSelection"
                placeholder="请输入文件名"
                style="width: 200px"
              />
            </el-form-item>
          </div>

          <!-- 10. 重复数据修复进度跟踪设置 -->
          <div class="form-section">
            <h4>10. 重复数据修复进度跟踪设置</h4>

            <el-form-item label="进度更新新频率">
              <el-select v-model="formData.progressUpdateFrequency" style="width: 200px">
                <el-option
                  v-for="option in PROGRESS_UPDATE_FREQUENCY_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="进度通知方式">
              <el-radio-group v-model="formData.progressNotificationMethod">
                <el-radio
                  v-for="option in PROGRESS_NOTIFICATION_OPTIONS"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>

        <!-- 第三行：重复数据排序设置、重复数据打印设置、重复数据修复结果跟踪设置 -->
        <div class="form-row">
          <!-- 3. 重复数据排序设置 -->
          <div class="form-section">
            <h4>3. 重复数据排序设置</h4>

            <el-form-item label="选择排序字段">
              <el-select v-model="formData.sortField" placeholder="请选择排序字段" style="width: 200px">
                <el-option
                  v-for="option in fieldOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="排序顺序">
              <el-radio-group v-model="formData.sortOrder">
                <el-radio
                  v-for="option in SORT_ORDER_OPTIONS"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- 7. 重复数据打印设置 -->
          <div class="form-section">
            <h4>7. 重复数据打印设置</h4>

            <el-form-item label="边距">
              <div class="margin-input">
                <el-input-number
                  v-model="formData.printMargin"
                  :min="0"
                  :max="100"
                  style="width: 120px"
                />
                <span>mm</span>
              </div>
            </el-form-item>

            <el-form-item label="页面/页面大小">
              <el-select v-model="formData.pageSize" style="width: 120px">
                <el-option
                  v-for="option in PAGE_SIZE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="纸张方向">
              <el-radio-group v-model="formData.paperOrientation">
                <el-radio
                  v-for="option in PAPER_ORIENTATION_OPTIONS"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- 11. 重复数据修复结果跟踪设置 -->
          <div class="form-section">
            <h4>11. 重复数据修复结果跟踪设置</h4>

            <el-form-item label="验证标准选择">
              <el-select v-model="formData.validationSelection" style="width: 200px">
                <el-option
                  v-for="option in VALIDATION_STANDARD_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="验证选项">
              <div class="validation-options">
                <el-checkbox v-model="formData.validationOptions.linkValidation">
                  链接日志
                </el-checkbox>
                <el-checkbox v-model="formData.validationOptions.performanceAnalysis">
                  性能分析
                </el-checkbox>
                <el-checkbox v-model="formData.validationOptions.basicInfo">
                  基本信息
                </el-checkbox>
              </div>
            </el-form-item>

            <el-form-item label="验证期间设置">
              <el-select v-model="formData.validationPeriodSetting" style="width: 200px">
                <el-option
                  v-for="option in VALIDATION_PERIOD_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 第四行：重复数据展示规则设置、重复数据分享设置 -->
        <div class="form-row">
          <!-- 4. 重复数据展示规则设置 -->
          <div class="form-section">
            <h4>4. 重复数据展示规则设置</h4>

            <el-form-item label="展示字段选择">
              <el-select
                v-model="formData.displayFields"
                multiple
                placeholder="请选择展示字段"
                style="width: 200px"
              >
                <el-option
                  v-for="option in fieldOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="过滤条件选择">
              <el-select
                v-model="formData.filterConditions"
                multiple
                placeholder="请选择过滤条件"
                style="width: 200px"
              >
                <el-option
                  v-for="option in filterConditionOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="视图选择">
              <el-radio-group v-model="formData.viewType">
                <el-radio
                  v-for="option in VIEW_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- 8. 重复数据分享设置 -->
          <div class="form-section">
            <h4>8. 重复数据分享设置</h4>

            <el-form-item label="分享渠道">
              <div class="share-channels">
                <el-checkbox v-model="formData.shareChannels.email">邮件</el-checkbox>
                <el-checkbox v-model="formData.shareChannels.link">链接</el-checkbox>
                <el-checkbox v-model="formData.shareChannels.browser">浏览器</el-checkbox>
                <el-checkbox v-model="formData.shareChannels.download">下载</el-checkbox>
                <el-checkbox v-model="formData.shareChannels.edit">编辑</el-checkbox>
              </div>
            </el-form-item>

            <el-form-item label="分享权限">
              <el-input
                v-model="formData.sharePermissions"
                placeholder="请输入分享权限"
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item label="有效期">
              <el-select v-model="formData.validPeriod" style="width: 200px">
                <el-option
                  v-for="option in VALIDATION_PERIOD_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<style scoped>
.duplicate-data-setting {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.setting-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.setting-form {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.report-type-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.form-row {
  display: flex;
  gap: 40px;
  margin-bottom: 30px;
}

.form-section {
  flex: 1;
  min-width: 300px;
}

.form-section h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.weight-slider {
  width: 200px;
}

.color-selection {
  display: flex;
  gap: 15px;
}

.color-option {
  padding: 2px 8px;
  border-radius: 4px;
  color: white;
  font-size: 12px;
}

.color-option.red {
  background-color: #f56c6c;
}

.color-option.blue {
  background-color: #409eff;
}

.percentage-input {
  display: flex;
  align-items: center;
  gap: 5px;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

:deep(.el-form-item__label) {
  font-size: 13px;
  color: #606266;
}

.margin-input {
  display: flex;
  align-items: center;
  gap: 5px;
}

.validation-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.share-channels {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.share-channels .el-checkbox {
  margin-right: 0;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .form-row {
    flex-direction: column;
    gap: 20px;
  }

  .form-section {
    min-width: auto;
  }
}
</style>
