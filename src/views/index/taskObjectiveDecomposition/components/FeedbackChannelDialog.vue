<!-- 报表任务问题反馈渠道设置弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="报表任务问题反馈渠道设置"
    width="600px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="feedback-channel-content">
      <!-- 反馈渠道选项 -->
      <div class="feedback-channels">
        <!-- 表单反馈 -->
        <div class="channel-card">
          <div class="channel-header">
            <div class="channel-icon form-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="channel-info">
              <h3 class="channel-title">表单反馈</h3>
              <p class="channel-desc">用户通过系统内表单提交问题反馈</p>
            </div>
            <div class="channel-toggle">
              <el-switch
                v-model="formData.formFeedbackEnabled"
                size="large"
                active-color="#409eff"
                inactive-color="#dcdfe6"
              />
            </div>
          </div>

          <!-- 表单反馈配置 -->
          <div class="channel-config" v-if="formData.formFeedbackEnabled">
            <el-form :model="formData" label-width="120px" label-position="right">
              <el-form-item label="反馈分类">
                <el-checkbox-group v-model="formData.feedbackCategories">
                  <el-checkbox value="bug">功能异常</el-checkbox>
                  <el-checkbox value="feature">功能建议</el-checkbox>
                  <el-checkbox value="performance">性能问题</el-checkbox>
                  <el-checkbox value="ui">界面问题</el-checkbox>
                  <el-checkbox value="other">其他问题</el-checkbox>
                </el-checkbox-group>
              </el-form-item>

              <el-form-item label="必填字段">
                <el-checkbox-group v-model="formData.requiredFields">
                  <el-checkbox value="contact">联系方式</el-checkbox>
                  <el-checkbox value="department">所属部门</el-checkbox>
                  <el-checkbox value="priority">问题优先级</el-checkbox>
                  <el-checkbox value="attachment">附件上传</el-checkbox>
                </el-checkbox-group>
              </el-form-item>

              <el-form-item label="自动分配">
                <el-switch
                  v-model="formData.autoAssign"
                  active-text="启用"
                  inactive-text="禁用"
                  active-color="#409eff"
                  inactive-color="#dcdfe6"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 邮箱反馈 -->
        <div class="channel-card">
          <div class="channel-header">
            <div class="channel-icon email-icon">
              <el-icon><Message /></el-icon>
            </div>
            <div class="channel-info">
              <h3 class="channel-title">邮箱反馈</h3>
              <p class="channel-desc">用户通过邮件发送问题反馈至指定邮箱</p>
            </div>
            <div class="channel-toggle">
              <el-switch
                v-model="formData.emailFeedbackEnabled"
                size="large"
                active-color="#409eff"
                inactive-color="#dcdfe6"
              />
            </div>
          </div>

          <!-- 邮箱反馈配置 -->
          <div class="channel-config" v-if="formData.emailFeedbackEnabled">
            <el-form :model="formData" label-width="120px" label-position="right">
              <el-form-item label="反馈邮箱">
                <el-input
                  v-model="formData.feedbackEmail"
                  placeholder="请输入反馈邮箱地址"
                  clearable
                />
              </el-form-item>

              <el-form-item label="抄送邮箱">
                <el-input
                  v-model="formData.ccEmails"
                  placeholder="多个邮箱用分号分隔"
                  clearable
                />
              </el-form-item>

              <el-form-item label="邮件模版">
                <el-select v-model="formData.emailTemplate" placeholder="选择邮件模版">
                  <el-option label="标准反馈模版" value="standard" />
                  <el-option label="紧急问题模版" value="urgent" />
                  <el-option label="功能建议模版" value="suggestion" />
                </el-select>
              </el-form-item>

              <el-form-item label="自动回复">
                <el-switch
                  v-model="formData.autoReply"
                  active-text="启用"
                  inactive-text="禁用"
                  active-color="#409eff"
                  inactive-color="#dcdfe6"
                />
              </el-form-item>

              <el-form-item label="回复内容" v-if="formData.autoReply">
                <el-input
                  v-model="formData.autoReplyContent"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入自动回复内容"
                  maxlength="300"
                  show-word-limit
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Message } from '@element-plus/icons-vue'
import DialogComp from '@/components/common/dialog-comp.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formData = reactive({
  // 表单反馈配置
  formFeedbackEnabled: true,
  feedbackCategories: ['bug', 'feature', 'performance'],
  requiredFields: ['contact', 'department'],
  autoAssign: true,
  
  // 邮箱反馈配置
  emailFeedbackEnabled: true,
  feedbackEmail: '',
  ccEmails: '',
  emailTemplate: 'standard',
  autoReply: true,
  autoReplyContent: '感谢您的反馈！我们已收到您的问题，将在2个工作日内回复。'
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  // 验证配置
  if (formData.emailFeedbackEnabled) {
    if (!formData.feedbackEmail.trim()) {
      ElMessage.warning('请输入反馈邮箱地址')
      return
    }
    
    // 简单的邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.feedbackEmail)) {
      ElMessage.warning('请输入有效的邮箱地址')
      return
    }

    if (formData.autoReply && !formData.autoReplyContent.trim()) {
      ElMessage.warning('请输入自动回复内容')
      return
    }
  }

  if (!formData.formFeedbackEnabled && !formData.emailFeedbackEnabled) {
    ElMessage.warning('请至少启用一种反馈渠道')
    return
  }

  visible.value = false
  emit('confirm', {
    type: 'feedbackChannel',
    data: { ...formData }
  })
  
  ElMessage.success('反馈渠道设置保存成功')
}
</script>

<style lang="scss" scoped>
.feedback-channel-content {
  padding: 20px;

  .feedback-channels {
    .channel-card {
      margin-bottom: 24px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }

      .channel-header {
        display: flex;
        align-items: center;
        padding: 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #e4e7ed;

        .channel-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          flex-shrink: 0;

          .el-icon {
            font-size: 20px;
            color: white;
          }

          &.form-icon {
            background: linear-gradient(135deg, #409eff, #66b3ff);
          }

          &.email-icon {
            background: linear-gradient(135deg, #67c23a, #85ce61);
          }
        }

        .channel-info {
          flex: 1;

          .channel-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 4px 0;
          }

          .channel-desc {
            font-size: 14px;
            color: #606266;
            margin: 0;
            line-height: 1.4;
          }
        }

        .channel-toggle {
          flex-shrink: 0;
        }
      }

      .channel-config {
        padding: 20px;
        background: white;

        .el-form-item {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .el-checkbox-group {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;

          .el-checkbox {
            margin: 0;
          }
        }
      }
    }
  }
}

.dialog-footer {
  margin-top: 24px;
  text-align: right;
  
  .el-button {
    margin-left: 12px;
  }
}
</style>
