<!-- 报表任务在线客服设置弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="报表任务在线客服设置"
    width="600px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="online-service-content">
      <!-- 客服状态设置 -->
      <div class="service-status-section">
        <div class="section-header">
          <h3 class="section-title">客服状态</h3>
          <p class="section-desc">启用后，用户可以通过系统内表单提交问题反馈</p>
        </div>
        
        <div class="status-toggle">
          <el-switch
            v-model="formData.serviceEnabled"
            size="large"
            :active-text="formData.serviceEnabled ? '已启用' : '已禁用'"
            active-color="#409eff"
            inactive-color="#dcdfe6"
          />
        </div>
      </div>

      <!-- 工作时间设置 -->
      <div class="work-time-section" v-if="formData.serviceEnabled">
        <div class="section-header">
          <h3 class="section-title">工作时间设置</h3>
          <p class="section-desc">设置客服团队的工作时间</p>
        </div>

        <div class="time-config">
          <!-- 工作日时间 -->
          <div class="time-row">
            <div class="time-label">工作日 (周一至周五)</div>
            <div class="time-inputs">
              <el-time-select
                v-model="formData.workdayStart"
                start="00:00"
                step="00:30"
                end="23:30"
                placeholder="开始时间"
              />
              <span class="time-separator">至</span>
              <el-time-select
                v-model="formData.workdayEnd"
                start="00:00"
                step="00:30"
                end="23:30"
                placeholder="结束时间"
              />
            </div>
          </div>

          <!-- 周末时间 -->
          <div class="time-row">
            <div class="time-label">周末 (周六至周日)</div>
            <div class="time-inputs">
              <el-time-select
                v-model="formData.weekendStart"
                start="00:00"
                step="00:30"
                end="23:30"
                placeholder="开始时间"
              />
              <span class="time-separator">至</span>
              <el-time-select
                v-model="formData.weekendEnd"
                start="00:00"
                step="00:30"
                end="23:30"
                placeholder="结束时间"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 自动回复设置 -->
      <div class="auto-reply-section" v-if="formData.serviceEnabled">
        <div class="section-header">
          <h3 class="section-title">自动回复设置</h3>
          <p class="section-desc">设置非工作时间的自动回复内容</p>
        </div>

        <el-form :model="formData" label-width="120px" label-position="right">
          <el-form-item label="启用自动回复">
            <el-switch
              v-model="formData.autoReplyEnabled"
              active-color="#409eff"
              inactive-color="#dcdfe6"
            />
          </el-form-item>

          <el-form-item label="回复内容" v-if="formData.autoReplyEnabled">
            <el-input
              v-model="formData.autoReplyContent"
              type="textarea"
              :rows="4"
              placeholder="请输入自动回复内容"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="联系方式" v-if="formData.autoReplyEnabled">
            <el-input
              v-model="formData.contactInfo"
              placeholder="请输入紧急联系方式"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formData = reactive({
  serviceEnabled: true,
  workdayStart: '09:00',
  workdayEnd: '18:00',
  weekendStart: '10:00',
  weekendEnd: '16:00',
  autoReplyEnabled: true,
  autoReplyContent: '您好！当前为非工作时间，我们会在工作时间内尽快回复您的问题。如有紧急情况，请联系值班人员。',
  contactInfo: '400-123-4567'
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  // 验证工作时间设置
  if (formData.serviceEnabled) {
    if (!formData.workdayStart || !formData.workdayEnd) {
      ElMessage.warning('请设置完整的工作日时间')
      return
    }
    
    if (!formData.weekendStart || !formData.weekendEnd) {
      ElMessage.warning('请设置完整的周末时间')
      return
    }

    if (formData.autoReplyEnabled && !formData.autoReplyContent.trim()) {
      ElMessage.warning('请输入自动回复内容')
      return
    }
  }

  visible.value = false
  emit('confirm', {
    type: 'onlineService',
    data: { ...formData }
  })
  
  ElMessage.success('在线客服设置保存成功')
}
</script>

<style lang="scss" scoped>
.online-service-content {
  padding: 20px;

  .section-header {
    margin-bottom: 16px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 4px 0;
    }

    .section-desc {
      font-size: 14px;
      color: #606266;
      margin: 0;
      line-height: 1.4;
    }
  }

  .service-status-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e4e7ed;

    .status-toggle {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .work-time-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e4e7ed;

    .time-config {
      .time-row {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .time-label {
          width: 140px;
          font-size: 14px;
          color: #303133;
          flex-shrink: 0;
        }

        .time-inputs {
          display: flex;
          align-items: center;
          gap: 12px;

          .time-separator {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }

  .auto-reply-section {
    .el-form-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.dialog-footer {
  margin-top: 24px;
  text-align: right;
  
  .el-button {
    margin-left: 12px;
  }
}
</style>
