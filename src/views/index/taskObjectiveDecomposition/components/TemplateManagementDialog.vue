<!-- 模板管理弹窗 -->
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 弹窗状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 新增模板弹窗状态
const addTemplateVisible = ref(false)
const loading = ref(false)

// 表格数据（更真实的数据）
const tableData = ref([
  {
    id: 1,
    templateName: '户籍人口信息登记模板',
    templateType: '业务表子任务',
    belongingModule: '民生服务',
    dataUpdatePeriod: '每日',
    createTime: '2024-01-15 09:30:25',
    creator: '张三',
    status: '启用',
    description: '用于户籍人口基础信息的登记和管理'
  },
  {
    id: 2,
    templateName: '失业人员就业援助登记表',
    templateType: '临时报表子任务',
    belongingModule: '经济发展',
    dataUpdatePeriod: '每周',
    createTime: '2024-01-16 14:20:15',
    creator: '李四',
    status: '启用',
    description: '记录失业人员基本信息及就业援助情况'
  },
  {
    id: 3,
    templateName: '第七次全国人口普查表',
    templateType: '业务表任务',
    belongingModule: '民生服务',
    dataUpdatePeriod: '每月',
    createTime: '2024-01-17 10:45:30',
    creator: '王五',
    status: '启用',
    description: '全国人口普查标准登记表格'
  },
  {
    id: 4,
    templateName: '老年人健康档案管理表',
    templateType: '临时报表任务',
    belongingModule: '民生服务',
    dataUpdatePeriod: '每半月',
    createTime: '2024-01-18 16:15:45',
    creator: '赵六',
    status: '启用',
    description: '老年人健康状况跟踪记录表'
  },
  {
    id: 5,
    templateName: '农村土地承包经营权登记表',
    templateType: '业务表子任务',
    belongingModule: '经济发展',
    dataUpdatePeriod: '每季度',
    createTime: '2024-01-19 11:30:20',
    creator: '孙七',
    status: '启用',
    description: '农村土地承包经营权确权登记'
  },
  {
    id: 6,
    templateName: '党员发展对象考察表',
    templateType: '临时报表子任务',
    belongingModule: '党的建设',
    dataUpdatePeriod: '每月',
    createTime: '2024-01-20 08:45:10',
    creator: '周八',
    status: '启用',
    description: '党员发展对象培养考察记录'
  },
  {
    id: 7,
    templateName: '社会治安综合治理月报表',
    templateType: '业务表任务',
    belongingModule: '平安法治',
    dataUpdatePeriod: '每月',
    createTime: '2024-01-21 13:20:35',
    creator: '吴九',
    status: '启用',
    description: '社会治安状况统计分析报表'
  },
  {
    id: 8,
    templateName: '低保户动态管理登记表',
    templateType: '临时报表任务',
    belongingModule: '民生服务',
    dataUpdatePeriod: '每半月',
    createTime: '2024-01-22 15:40:25',
    creator: '郑十',
    status: '禁用',
    description: '城乡低保户信息动态管理'
  },
  {
    id: 9,
    templateName: '基层党组织活动记录表',
    templateType: '业务表子任务',
    belongingModule: '党的建设',
    dataUpdatePeriod: '每周',
    createTime: '2024-01-23 09:15:50',
    creator: '钱一',
    status: '启用',
    description: '基层党组织各类活动开展情况记录'
  },
  {
    id: 10,
    templateName: '企业复工复产情况统计表',
    templateType: '临时报表任务',
    belongingModule: '经济发展',
    dataUpdatePeriod: '每日',
    createTime: '2024-01-24 17:25:40',
    creator: '陈二',
    status: '启用',
    description: '企业复工复产进度跟踪统计'
  },
  {
    id: 11,
    templateName: '矛盾纠纷排查调处记录表',
    templateType: '业务表任务',
    belongingModule: '平安法治',
    dataUpdatePeriod: '每周',
    createTime: '2024-01-25 12:10:15',
    creator: '刘三',
    status: '启用',
    description: '社会矛盾纠纷排查和调处情况记录'
  },
  {
    id: 12,
    templateName: '乡村振兴项目进度表',
    templateType: '业务表子任务',
    belongingModule: '经济发展',
    dataUpdatePeriod: '每月',
    createTime: '2024-01-26 14:35:30',
    creator: '杨四',
    status: '启用',
    description: '乡村振兴重点项目实施进度跟踪'
  }
])

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: computed(() => tableData.value.length)
})

// 新增模板表单数据
const templateForm = reactive({
  templateName: '',
  templateType: '',
  belongingModule: '',
  dataUpdatePeriod: ''
})

// 模板类型选项
const templateTypeOptions = [
  { value: '业务表子任务', label: '业务表子任务' },
  { value: '临时报表子任务', label: '临时报表子任务' },
  { value: '业务表任务', label: '业务表任务' }
]

// 所属板块选项
const moduleOptions = [
  { value: '党的建设', label: '党的建设' },
  { value: '经济发展', label: '经济发展' },
  { value: '民生服务', label: '民生服务' },
  { value: '平安法治', label: '平安法治' }
]

// 数据更新周期选项
const updatePeriodOptions = [
  { value: '每日', label: '每日' },
  { value: '每半月', label: '每半月' },
  { value: '每月', label: '每月' },
  { value: '每季度', label: '每季度' },
  { value: '每半年', label: '每半年' }
]

// 分页数据
const paginatedData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return tableData.value.slice(start, end)
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleAddTemplate = () => {
  addTemplateVisible.value = true
  resetTemplateForm()
}

const resetTemplateForm = () => {
  templateForm.templateName = ''
  templateForm.templateType = ''
  templateForm.belongingModule = ''
  templateForm.dataUpdatePeriod = ''
}

const handleTemplateCancel = () => {
  addTemplateVisible.value = false
  resetTemplateForm()
}

const handleTemplateConfirm = async () => {
  // 验证表单
  if (!templateForm.templateName || !templateForm.templateType ||
      !templateForm.belongingModule || !templateForm.dataUpdatePeriod) {
    ElMessage.warning('请填写完整的模板信息')
    return
  }

  loading.value = true
  try {
    // 模拟接口请求延迟
    await new Promise(resolve => setTimeout(resolve, 1200))

    // 添加新模板
    const newTemplate = {
      id: Math.max(...tableData.value.map(item => item.id)) + 1,
      templateName: templateForm.templateName,
      templateType: templateForm.templateType,
      belongingModule: templateForm.belongingModule,
      dataUpdatePeriod: templateForm.dataUpdatePeriod,
      createTime: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-'),
      creator: '当前用户',
      status: '启用',
      description: '新创建的模板'
    }
    tableData.value.unshift(newTemplate)

    addTemplateVisible.value = false
    ElMessage.success('模板添加成功')
    resetTemplateForm()
  } catch (error) {
    ElMessage.error('添加失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板"${row.templateName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true
    try {
      // 模拟接口请求延迟
      await new Promise(resolve => setTimeout(resolve, 800))

      const index = tableData.value.findIndex(item => item.id === row.id)
      if (index > -1) {
        tableData.value.splice(index, 1)
        ElMessage.success('删除成功')
      }
    } catch (error) {
      ElMessage.error('删除失败，请重试')
    } finally {
      loading.value = false
    }
  } catch {
    // 用户取消删除
  }
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<template>
  <el-dialog
    v-model="visible"
    title="模板管理"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="template-management-content" v-loading="loading">
      <!-- 操作按钮区域 -->
      <div class="action-bar">
        <el-button type="primary" @click="handleAddTemplate">
          新增模板
        </el-button>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <el-table
          :data="paginatedData"
          style="width: 100%"
          border
          stripe
        >
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="templateName" label="模板名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="templateType" label="模板类型" width="140" align="center" />
          <el-table-column prop="belongingModule" label="所属板块" width="120" align="center" />
          <el-table-column prop="dataUpdatePeriod" label="数据更新周期" width="120" align="center" />
          <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
          <el-table-column prop="creator" label="创建人" width="100" align="center" />
          <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === '启用' ? 'success' : 'danger'">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template #default="{ row }">
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleClose">确定</el-button>
      </div>
    </template>

    <!-- 新增模板弹窗 -->
    <el-dialog
      v-model="addTemplateVisible"
      title="模板管理"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="add-template-content">
        <div class="form-item">
          <div class="form-label">模板名称</div>
          <el-input
            v-model="templateForm.templateName"
            placeholder="请输入"
            style="width: 100%"
          />
        </div>

        <div class="form-item">
          <div class="form-label">模板类型</div>
          <div class="form-description">业务表任务、临时报表任务、业务表子任务</div>
          <el-select
            v-model="templateForm.templateType"
            placeholder="请选择模板类型"
            style="width: 100%; margin-top: 8px;"
          >
            <el-option
              v-for="option in templateTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <div class="form-item">
          <div class="form-label">所属板块</div>
          <div class="form-description">党的建设、经济发展、民生服务、平安法治</div>
          <el-select
            v-model="templateForm.belongingModule"
            placeholder="请选择所属板块"
            style="width: 100%; margin-top: 8px;"
          >
            <el-option
              v-for="option in moduleOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <div class="form-item">
          <div class="form-label">数据更新周期</div>
          <div class="form-description">每日、每半月、每月、每季度、每半年</div>
          <el-select
            v-model="templateForm.dataUpdatePeriod"
            placeholder="请选择数据更新周期"
            style="width: 100%; margin-top: 8px;"
          >
            <el-option
              v-for="option in updatePeriodOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleTemplateCancel">取消</el-button>
          <el-button type="primary" @click="handleTemplateConfirm" :loading="loading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<style scoped lang="scss">
.template-management-content {
  .action-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
  }

  .table-container {
    .pagination-wrapper {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
}

.add-template-content {
  .form-item {
    margin-bottom: 24px;

    .form-label {
      font-size: 14px;
      color: #303133;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .form-description {
      font-size: 13px;
      color: #909399;
      line-height: 1.5;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
