<!-- 业务表任务帮助文档创建弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="业务表任务帮助文档创建"
    width="900px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="business-task-help-content">
      <!-- Markdown编辑器 -->
      <div class="editor-container">
        <v-md-editor
          v-model="content"
          height="550px"
          :disabled-menus="[]"
          :toolbar="toolbar"
          @upload-image="handleUploadImage"
          placeholder="请输入业务表任务帮助文档内容..."
        />
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const content = ref('')

// 工具栏配置
const toolbar = {
  undo: true, // 撤销
  redo: true, // 重做
  clear: true, // 清空
  h: true, // 标题
  bold: true, // 粗体
  italic: true, // 斜体
  strikethrough: true, // 删除线
  quote: true, // 引用
  ul: true, // 无序列表
  ol: true, // 有序列表
  table: true, // 表格
  hr: true, // 分割线
  link: true, // 链接
  image: true, // 图片
  code: true, // 代码块
  save: true // 保存
}

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  if (!content.value.trim()) {
    ElMessage.warning('请输入帮助文档内容')
    return
  }

  visible.value = false
  emit('confirm', {
    type: 'businessTaskHelp',
    content: content.value
  })
  
  // 重置内容
  content.value = ''
  ElMessage.success('业务表任务帮助文档创建成功')
}

// 图片上传处理
const handleUploadImage = (event: any, insertImage: Function, files: File[]) => {
  // 这里可以实现图片上传逻辑
  // 暂时使用本地预览
  const file = files[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string
      insertImage({
        url: imageUrl,
        desc: file.name,
        width: 'auto',
        height: 'auto'
      })
    }
    reader.readAsDataURL(file)
  }
}
</script>

<style lang="scss" scoped>
.business-task-help-content {
  padding: 0;

  .editor-container {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;

    :deep(.v-md-editor) {
      box-shadow: none;
      border: none;
    }

    :deep(.v-md-editor__toolbar) {
      border-bottom: 1px solid #e4e7ed;
      background-color: #fafafa;
      padding: 8px 12px;
    }

    :deep(.v-md-editor__toolbar-item) {
      margin-right: 8px;
    }

    :deep(.v-md-editor__editor) {
      background-color: #fff;
    }

    :deep(.v-md-editor__editor-wrapper) {
      padding: 16px;
    }

    :deep(.v-md-editor__preview) {
      background-color: #fff;
      padding: 16px;
    }
  }
}

.dialog-footer {
  margin-top: 24px;
  text-align: right;

  .el-button {
    margin-left: 12px;
  }
}
</style>
