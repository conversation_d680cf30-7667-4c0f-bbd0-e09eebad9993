<!-- 版本管理弹窗 -->
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

interface Props {
  modelValue: boolean
  task?: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 弹窗状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 当前激活的标签页
const activeTab = ref('list')
const loading = ref(false)

// 版本列表数据
const versionList = ref([
  {
    id: 1,
    versionNumber: '1.0',
    versionName: '创建未特殊评审',
    versionDescription: '创建一个未特殊评审的评审后下发',
    taskType: '业务承任务',
    createTime: '2025-05-23'
  },
  {
    id: 2,
    versionNumber: '2.0',
    versionName: '表格修改1',
    versionDescription: '对表格内容进行修改',
    taskType: '业务表子任务',
    createTime: '2025-05-27'
  },
  {
    id: 3,
    versionNumber: '2.1',
    versionName: '表格修改2',
    versionDescription: '对表格内容进行修改',
    taskType: '临时报表子任务',
    createTime: '2025-05-27'
  },
  {
    id: 4,
    versionNumber: '2.2',
    versionName: '表格修改3',
    versionDescription: '对表格内容进行修改',
    taskType: '业务承任务',
    createTime: '2025-05-28'
  }
])

// 新版本表单数据
const newVersionForm = reactive({
  versionNumber: '',
  versionName: '',
  versionDescription: '',
  taskType: ''
})

// 任务类型选项
const taskTypeOptions = [
  { label: '业务承任务', value: '业务承任务' },
  { label: '临时报表子任务', value: '临时报表子任务' },
  { label: '业务表子任务', value: '业务表子任务' }
]

// 表单验证规则
const formRules = {
  versionNumber: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  versionName: [
    { required: true, message: '请输入版本名称', trigger: 'blur' }
  ],
  versionDescription: [
    { required: true, message: '请输入版本描述', trigger: 'blur' }
  ],
  taskType: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ]
}

const formRef = ref()

// 表格列配置
const columns = [
  { prop: 'versionNumber', label: '版本号', width: 100 },
  { prop: 'versionName', label: '版本名称', width: 150 },
  { prop: 'versionDescription', label: '版本描述', minWidth: 200 },
  { prop: 'taskType', label: '任务类型', width: 150 },
  { prop: 'createTime', label: '创建时间', width: 120 }
]

// 重置表单
const resetForm = () => {
  Object.assign(newVersionForm, {
    versionNumber: '',
    versionName: '',
    versionDescription: '',
    taskType: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 确认创建版本
const handleCreateVersion = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 添加到版本列表
    const newVersion = {
      id: versionList.value.length + 1,
      ...newVersionForm,
      createTime: new Date().toISOString().slice(0, 10)
    }
    versionList.value.unshift(newVersion)
    
    ElMessage.success('版本创建成功')
    resetForm()
    activeTab.value = 'list' // 切换到版本列表
    
  } catch (error) {
    console.error('创建版本失败:', error)
  } finally {
    loading.value = false
  }
}

// 取消创建
const handleCancel = () => {
  resetForm()
  visible.value = false
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  if (tabName === 'create') {
    resetForm()
  }
}

// 弹窗关闭时重置
const handleClose = () => {
  activeTab.value = 'list'
  resetForm()
}

onMounted(() => {
  // 初始化数据
})
</script>

<template>
  <el-dialog
    v-model="visible"
    title="版本管理"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- 版本列表标签页 -->
      <el-tab-pane label="版本列表" name="list">
        <div class="version-list">
          <el-table
            :data="versionList"
            style="width: 100%"
            height="400"
            border
          >
            <el-table-column
              v-for="column in columns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              :min-width="column.minWidth"
              show-overflow-tooltip
            />
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 版本创建标签页 -->
      <el-tab-pane label="版本创建" name="create">
        <div class="version-create">
          <el-form
            ref="formRef"
            :model="newVersionForm"
            :rules="formRules"
            label-width="100px"
            style="max-width: 500px; margin: 0 auto;"
          >
            <el-form-item label="版本号" prop="versionNumber" required>
              <el-input
                v-model="newVersionForm.versionNumber"
                placeholder="请输入版本号"
                clearable
              />
            </el-form-item>

            <el-form-item label="版本名称" prop="versionName" required>
              <el-input
                v-model="newVersionForm.versionName"
                placeholder="请输入版本名称"
                clearable
              />
            </el-form-item>

            <el-form-item label="版本描述" prop="versionDescription" required>
              <el-input
                v-model="newVersionForm.versionDescription"
                type="textarea"
                :rows="3"
                placeholder="请输入版本描述"
              />
            </el-form-item>

            <el-form-item label="任务类型" prop="taskType" required>
              <el-select
                v-model="newVersionForm.taskType"
                placeholder="请选择任务类型"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="option in taskTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          v-if="activeTab === 'create'"
          type="primary"
          :loading="loading"
          @click="handleCreateVersion"
        >
          确认
        </el-button>
        <el-button
          v-else
          type="primary"
          @click="visible = false"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.version-list {
  .el-table {
    border-radius: 4px;
  }
}

.version-create {
  padding: 20px 0;
  
  .el-form {
    .el-form-item {
      margin-bottom: 24px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__content) {
  padding: 0;
}
</style>
