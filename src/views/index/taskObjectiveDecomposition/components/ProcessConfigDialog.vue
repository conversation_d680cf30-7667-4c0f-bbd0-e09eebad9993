<!-- 流程配置弹窗 -->
<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', configData: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 弹窗状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 流程配置选项
const processOptions = [
  { value: 'multiLevel', label: '默认多级审核流程' },
  { value: 'department', label: '默认本部门审核' },
  { value: 'leader', label: '本部门分管领导审核' }
]

// 表单数据
const formData = reactive({
  temporaryReportTaskProcess: '', // 临时报表任务流程配置
  businessTableSubTaskProcess: '', // 业务表子任务流程配置
  temporaryReportSubTaskProcess: '' // 临时报表子任务流程配置
})

// 方法
const handleCancel = () => {
  visible.value = false
  resetForm()
}

const handleConfirm = async () => {
  // 验证是否所有字段都已选择
  if (!formData.temporaryReportTaskProcess ||
      !formData.businessTableSubTaskProcess ||
      !formData.temporaryReportSubTaskProcess) {
    ElMessage.warning('请完成所有流程配置选择')
    return
  }

  loading.value = true
  try {
    // 模拟接口请求延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    emit('confirm', { ...formData })
    visible.value = false
    ElMessage.success('流程配置保存成功')
    resetForm()
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  formData.temporaryReportTaskProcess = ''
  formData.businessTableSubTaskProcess = ''
  formData.temporaryReportSubTaskProcess = ''
}

// 获取选项标签
const getOptionLabel = (value: string) => {
  const option = processOptions.find(opt => opt.value === value)
  return option ? option.label : value
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="流程配置"
    width="600px"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <div class="process-config-content" v-loading="loading">
      <!-- 临时报表任务流程配置 -->
      <div class="config-item">
        <div class="config-label">临时报表任务流程配置</div>
        <el-select
          v-model="formData.temporaryReportTaskProcess"
          placeholder="默认多级审核流程"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="option in processOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </div>

      <!-- 业务表子任务流程配置 -->
      <div class="config-item">
        <div class="config-label">业务表子任务流程配置</div>
        <el-select
          v-model="formData.businessTableSubTaskProcess"
          placeholder="默认多级审核流程"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="option in processOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </div>

      <!-- 临时报表子任务流程配置 -->
      <div class="config-item">
        <div class="config-label">临时报表子任务流程配置</div>
        <el-select
          v-model="formData.temporaryReportSubTaskProcess"
          placeholder="默认多级审核流程"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="option in processOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </div>

      <!-- 说明文字 -->
      <div class="config-note">
        <span class="note-text">三个都是下拉单选：默认多级审核流程、默认本部门审核、本部门分管领导审核</span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.process-config-content {
  padding: 20px 0;

  .config-item {
    margin-bottom: 24px;

    .config-label {
      font-size: 14px;
      color: #303133;
      margin-bottom: 8px;
      font-weight: 500;
    }

    &:last-of-type {
      margin-bottom: 16px;
    }
  }

  .config-note {
    padding: 12px 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border-left: 4px solid #e6a23c;

    .note-text {
      font-size: 13px;
      color: #e6a23c;
      line-height: 1.5;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
