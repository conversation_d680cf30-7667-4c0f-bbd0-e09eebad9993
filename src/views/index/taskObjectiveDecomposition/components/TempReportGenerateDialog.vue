<!-- 临时报表任务帮助文档生成弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="临时报表任务帮助文档生成"
    width="600px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="temp-report-generate-content">
      <!-- 生成选项 -->
      <div class="generate-options">
        <h3 class="section-title">选择生成方式</h3>
        
        <el-radio-group v-model="generateType" class="generate-type-group">
          <el-radio value="auto" size="large">
            <div class="radio-content">
              <div class="radio-title">智能生成</div>
              <div class="radio-desc">基于现有数据和配置自动生成标准化文档</div>
            </div>
          </el-radio>
          
          <el-radio value="template" size="large">
            <div class="radio-content">
              <div class="radio-title">模版生成</div>
              <div class="radio-desc">使用预设模版快速生成结构化文档</div>
            </div>
          </el-radio>
          
          <el-radio value="custom" size="large">
            <div class="radio-content">
              <div class="radio-title">自定义生成</div>
              <div class="radio-desc">根据特定需求定制生成内容和格式</div>
            </div>
          </el-radio>
        </el-radio-group>
      </div>

      <!-- 生成参数配置 -->
      <div class="generate-config" v-if="generateType">
        <h3 class="section-title">生成参数</h3>
        
        <el-form :model="configForm" label-width="120px" label-position="right">
          <el-form-item label="文档标题">
            <el-input 
              v-model="configForm.title" 
              placeholder="请输入文档标题"
              clearable
            />
          </el-form-item>
          
          <el-form-item label="包含内容">
            <el-checkbox-group v-model="configForm.includeContent">
              <el-checkbox value="overview">概述说明</el-checkbox>
              <el-checkbox value="steps">操作步骤</el-checkbox>
              <el-checkbox value="examples">示例说明</el-checkbox>
              <el-checkbox value="faq">常见问题</el-checkbox>
              <el-checkbox value="notes">注意事项</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          
          <el-form-item label="详细程度">
            <el-radio-group v-model="configForm.detailLevel">
              <el-radio value="simple">简洁版</el-radio>
              <el-radio value="standard">标准版</el-radio>
              <el-radio value="detailed">详细版</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="输出格式">
            <el-select v-model="configForm.outputFormat" placeholder="选择输出格式">
              <el-option label="Markdown" value="markdown" />
              <el-option label="HTML" value="html" />
              <el-option label="纯文本" value="text" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 生成进度 -->
      <div class="generate-progress" v-if="isGenerating">
        <el-progress 
          :percentage="generateProgress" 
          :status="generateProgress === 100 ? 'success' : undefined"
        />
        <p class="progress-text">{{ progressText }}</p>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleGenerate"
          :loading="isGenerating"
          :disabled="!generateType"
        >
          {{ isGenerating ? '生成中...' : '开始生成' }}
        </el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const generateType = ref('')
const isGenerating = ref(false)
const generateProgress = ref(0)
const progressText = ref('')

const configForm = reactive({
  title: '临时报表任务帮助文档',
  includeContent: ['overview', 'steps', 'examples'],
  detailLevel: 'standard',
  outputFormat: 'markdown'
})

// 方法
const handleClose = () => {
  visible.value = false
  resetForm()
}

const handleCancel = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  generateType.value = ''
  isGenerating.value = false
  generateProgress.value = 0
  progressText.value = ''
  configForm.title = '临时报表任务帮助文档'
  configForm.includeContent = ['overview', 'steps', 'examples']
  configForm.detailLevel = 'standard'
  configForm.outputFormat = 'markdown'
}

const handleGenerate = async () => {
  if (!generateType.value) {
    ElMessage.warning('请选择生成方式')
    return
  }

  if (!configForm.title.trim()) {
    ElMessage.warning('请输入文档标题')
    return
  }

  isGenerating.value = true
  generateProgress.value = 0
  
  // 模拟生成过程
  const steps = [
    { progress: 20, text: '正在分析数据结构...' },
    { progress: 40, text: '正在生成文档框架...' },
    { progress: 60, text: '正在填充内容详情...' },
    { progress: 80, text: '正在优化文档格式...' },
    { progress: 100, text: '文档生成完成！' }
  ]

  for (const step of steps) {
    await new Promise(resolve => setTimeout(resolve, 800))
    generateProgress.value = step.progress
    progressText.value = step.text
  }

  // 生成完成
  setTimeout(() => {
    visible.value = false
    emit('confirm', {
      type: 'tempReportGenerate',
      generateType: generateType.value,
      config: { ...configForm }
    })
    
    ElMessage.success('临时报表任务帮助文档生成成功')
    resetForm()
  }, 1000)
}
</script>

<style lang="scss" scoped>
.temp-report-generate-content {
  padding: 20px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
  }

  .generate-options {
    margin-bottom: 24px;

    .generate-type-group {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .el-radio {
        margin: 0;
        padding: 16px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          background-color: #f0f9ff;
        }

        &.is-checked {
          border-color: #409eff;
          background-color: #f0f9ff;
        }

        .radio-content {
          margin-left: 8px;

          .radio-title {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .radio-desc {
            font-size: 12px;
            color: #606266;
            line-height: 1.4;
          }
        }
      }
    }
  }

  .generate-config {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .el-form-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .el-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .el-checkbox {
        margin: 0;
      }
    }
  }

  .generate-progress {
    text-align: center;
    padding: 20px;

    .progress-text {
      margin-top: 12px;
      font-size: 14px;
      color: #606266;
    }
  }
}

.dialog-footer {
  margin-top: 24px;
  text-align: right;
  
  .el-button {
    margin-left: 12px;
  }
}
</style>
