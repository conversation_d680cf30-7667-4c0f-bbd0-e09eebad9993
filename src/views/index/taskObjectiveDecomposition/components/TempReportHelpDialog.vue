<!-- 临时报表任务帮助文档模版编辑弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="临时报表任务帮助文档模版编辑"
    width="900px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="temp-report-help-content">
      <!-- Markdown编辑器 -->
      <div class="editor-container">
        <v-md-editor
          v-model="content"
          height="550px"
          :disabled-menus="[]"
          :toolbar="toolbar"
          @upload-image="handleUploadImage"
          placeholder="请编辑临时报表任务帮助文档模版..."
        />
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const content = ref('')

// 工具栏配置
const toolbar = {
  undo: true, // 撤销
  redo: true, // 重做
  clear: true, // 清空
  h: true, // 标题
  bold: true, // 粗体
  italic: true, // 斜体
  strikethrough: true, // 删除线
  quote: true, // 引用
  ul: true, // 无序列表
  ol: true, // 有序列表
  table: true, // 表格
  hr: true, // 分割线
  link: true, // 链接
  image: true, // 图片
  code: true, // 代码块
  save: true // 保存
}

// 预设的模版内容
const defaultTemplate = `# 1. 概述

临时报表任务帮助文档旨在指导用户如何创建、配置和导出临时报表。通过本指南，您将了解报表创建的完整流程，包括数据选择、样式设置和导出选项等功能。

> ## 注意事项
> 
> 临时报表仅保存7天，如需长期保存，请使用"导出"功能将报表保存到本地或添加到收藏夹。

# 2. 报表创建

创建新报表是使用临时报表功能的第一步。您可以从空白报表开始，或使用系统提供的模版快速创建。

## 2.1 选择数据源

在创建报表前，请确保您有权限访问所需的数据源。系统支持以下数据源类型：

- 业务数据库
- 外部API接口
- 文件导入（Excel、CSV等）
- 实时数据流

## 2.2 配置报表参数

根据业务需求配置以下参数：

- **报表名称**：为报表设置一个清晰的名称
- **数据范围**：选择需要包含的数据时间范围
- **筛选条件**：设置数据筛选规则
- **分组方式**：选择数据分组和聚合方式

# 3. 样式设置

## 3.1 表格样式

- 表头样式：字体、颜色、对齐方式
- 数据行样式：交替行颜色、边框设置
- 列宽调整：自动调整或手动设置

## 3.2 图表配置

支持多种图表类型：

- 柱状图
- 折线图  
- 饼图
- 散点图

# 4. 导出和分享

完成报表创建后，您可以：

- 导出为Excel格式
- 导出为PDF格式
- 生成分享链接
- 设置定时发送

---

*如需更多帮助，请联系系统管理员或查看详细用户手册。*`

// 监听弹窗显示状态，设置默认内容
watch(visible, (newVal) => {
  if (newVal && !content.value) {
    content.value = defaultTemplate
  }
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  if (!content.value.trim()) {
    ElMessage.warning('请输入帮助文档内容')
    return
  }

  visible.value = false
  emit('confirm', {
    type: 'tempReportHelp',
    content: content.value
  })
  
  ElMessage.success('临时报表任务帮助文档模版保存成功')
}

// 图片上传处理
const handleUploadImage = (event: any, insertImage: Function, files: File[]) => {
  // 这里可以实现图片上传逻辑
  // 暂时使用本地预览
  const file = files[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string
      insertImage({
        url: imageUrl,
        desc: file.name,
        width: 'auto',
        height: 'auto'
      })
    }
    reader.readAsDataURL(file)
  }
}
</script>

<style lang="scss" scoped>
.temp-report-help-content {
  padding: 0;

  .editor-container {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;

    :deep(.v-md-editor) {
      box-shadow: none;
      border: none;
    }

    :deep(.v-md-editor__toolbar) {
      border-bottom: 1px solid #e4e7ed;
      background-color: #fafafa;
      padding: 8px 12px;
    }

    :deep(.v-md-editor__toolbar-item) {
      margin-right: 8px;
    }

    :deep(.v-md-editor__editor) {
      background-color: #fff;
    }

    :deep(.v-md-editor__editor-wrapper) {
      padding: 16px;
    }

    :deep(.v-md-editor__preview) {
      background-color: #fff;
      padding: 16px;
    }
  }
}

.dialog-footer {
  margin-top: 24px;
  text-align: right;

  .el-button {
    margin-left: 12px;
  }
}
</style>
