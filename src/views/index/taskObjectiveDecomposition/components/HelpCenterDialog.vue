<!-- 帮助中心主弹窗组件 -->
<template>
  <DialogComp
    v-model="visible"
    title="帮助中心"
    width="1000px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="help-center-content">
      <!-- 功能卡片网格 -->
      <div class="help-cards-grid">
        <!-- 业务表任务帮助文档创建 -->
        <div class="help-card">
          <div class="card-icon blue">
            <el-icon><Document /></el-icon>
          </div>
          <div class="card-content">
            <h3 class="card-title">业务表任务帮助文档创建</h3>
            <p class="card-description">
              创建详细的业务表任务帮助文档，指导用户完成各类业务报表任务的配置与执行。
            </p>
            <el-button type="primary" class="card-button" @click="handleBusinessTaskHelp">
              开始创建
              <el-icon class="ml-1"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 临时报表任务帮助文档模版编辑 -->
        <div class="help-card">
          <div class="card-icon blue">
            <el-icon><EditPen /></el-icon>
          </div>
          <div class="card-content">
            <h3 class="card-title">临时报表任务帮助文档模版编辑</h3>
            <p class="card-description">
              编辑和管理临时报表任务的帮助文档模版，提高文档创建效率和一致性。
            </p>
            <el-button type="primary" class="card-button" @click="handleTempReportHelp">
              编辑模版
              <el-icon class="ml-1"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 业务表子任务帮助文档创建 -->
        <div class="help-card">
          <div class="card-icon green">
            <el-icon><List /></el-icon>
          </div>
          <div class="card-content">
            <h3 class="card-title">业务表子任务帮助文档创建</h3>
            <p class="card-description">
              为业务表的子任务创建专门的帮助文档，提供详细的操作指导和最佳实践。
            </p>
            <el-button type="primary" class="card-button" @click="handleBusinessSubTaskHelp">
              创建子任务文档
              <el-icon class="ml-1"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 临时报表任务帮助文档生成 -->
        <div class="help-card">
          <div class="card-icon orange">
            <el-icon><MagicStick /></el-icon>
          </div>
          <div class="card-content">
            <h3 class="card-title">临时报表任务帮助文档生成</h3>
            <p class="card-description">
              基于现有数据和配置自动生成临时报表任务的帮助文档，快速创建标准化的需求。
            </p>
            <el-button type="primary" class="card-button" @click="handleTempReportGenerate">
              自动生成
              <el-icon class="ml-1"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 报表任务在线客服设置 -->
        <div class="help-card">
          <div class="card-icon purple">
            <el-icon><Headset /></el-icon>
          </div>
          <div class="card-content">
            <h3 class="card-title">报表任务在线客服设置</h3>
            <p class="card-description">
              配置报表任务相关的在线客服系统，设置客服人员、工作时间和自动回复内容。
            </p>
            <el-button type="primary" class="card-button" @click="handleOnlineService">
              配置客服
              <el-icon class="ml-1"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 报表任务问题反馈渠道设置 -->
        <div class="help-card">
          <div class="card-icon red">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <div class="card-content">
            <h3 class="card-title">报表任务问题反馈渠道设置</h3>
            <p class="card-description">
              设置报表任务相关的问题反馈渠道，包括表单反馈和邮箱反馈等多种方式。
            </p>
            <el-button type="primary" class="card-button" @click="handleFeedbackChannel">
              设置反馈渠道
              <el-icon class="ml-1"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>

    <!-- 子弹窗组件 -->
    <BusinessTaskHelpDialog
      v-model="businessTaskHelpVisible"
      @confirm="handleSubDialogConfirm"
    />

    <TempReportHelpDialog
      v-model="tempReportHelpVisible"
      @confirm="handleSubDialogConfirm"
    />

    <BusinessSubTaskHelpDialog
      v-model="businessSubTaskHelpVisible"
      @confirm="handleSubDialogConfirm"
    />

    <TempReportGenerateDialog
      v-model="tempReportGenerateVisible"
      @confirm="handleSubDialogConfirm"
    />

    <OnlineServiceDialog
      v-model="onlineServiceVisible"
      @confirm="handleSubDialogConfirm"
    />

    <FeedbackChannelDialog
      v-model="feedbackChannelVisible"
      @confirm="handleSubDialogConfirm"
    />
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Document,
  EditPen,
  List,
  MagicStick,
  Headset,
  ChatDotRound,
  ArrowRight
} from '@element-plus/icons-vue'
import DialogComp from '@/components/common/dialog-comp.vue'
import BusinessTaskHelpDialog from '@/views/index/taskObjectiveDecomposition/components/BusinessTaskHelpDialog.vue'
import TempReportHelpDialog from '@/views/index/taskObjectiveDecomposition/components/TempReportHelpDialog.vue'
import BusinessSubTaskHelpDialog from '@/views/index/taskObjectiveDecomposition/components/BusinessSubTaskHelpDialog.vue'
import TempReportGenerateDialog from '@/views/index/taskObjectiveDecomposition/components/TempReportGenerateDialog.vue'
import OnlineServiceDialog from '@/views/index/taskObjectiveDecomposition/components/OnlineServiceDialog.vue'
import FeedbackChannelDialog from '@/views/index/taskObjectiveDecomposition/components/FeedbackChannelDialog.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 子弹窗显示状态
const businessTaskHelpVisible = ref(false)
const tempReportHelpVisible = ref(false)
const businessSubTaskHelpVisible = ref(false)
const tempReportGenerateVisible = ref(false)
const onlineServiceVisible = ref(false)
const feedbackChannelVisible = ref(false)

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  visible.value = false
  emit('confirm', {})
}

// 各个功能卡片的点击处理
const handleBusinessTaskHelp = () => {
  businessTaskHelpVisible.value = true
}

const handleTempReportHelp = () => {
  tempReportHelpVisible.value = true
}

const handleBusinessSubTaskHelp = () => {
  businessSubTaskHelpVisible.value = true
}

const handleTempReportGenerate = () => {
  tempReportGenerateVisible.value = true
}

const handleOnlineService = () => {
  onlineServiceVisible.value = true
}

const handleFeedbackChannel = () => {
  feedbackChannelVisible.value = true
}

// 子弹窗确认处理
const handleSubDialogConfirm = (data: any) => {
  console.log('子弹窗确认:', data)
}
</script>

<style lang="scss" scoped>
.help-center-content {
  padding: 20px;

  .help-cards-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    max-width: 960px;
    margin: 0 auto;

    .help-card {
      display: flex;
      padding: 24px;
      background: #ffffff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        transform: translateY(-2px);
      }

      .card-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        flex-shrink: 0;

        .el-icon {
          font-size: 24px;
          color: white;
        }

        &.blue {
          background: linear-gradient(135deg, #409eff, #66b3ff);
        }

        &.green {
          background: linear-gradient(135deg, #67c23a, #85ce61);
        }

        &.orange {
          background: linear-gradient(135deg, #e6a23c, #f0c78a);
        }

        &.purple {
          background: linear-gradient(135deg, #9c88ff, #b8a9ff);
        }

        &.red {
          background: linear-gradient(135deg, #f56c6c, #f89898);
        }
      }

      .card-content {
        flex: 1;
        display: flex;
        flex-direction: column;

        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 8px 0;
          line-height: 1.4;
        }

        .card-description {
          font-size: 14px;
          color: #606266;
          line-height: 1.6;
          margin: 0 0 16px 0;
          flex: 1;
        }

        .card-button {
          align-self: flex-start;
          font-size: 14px;

          .ml-1 {
            margin-left: 4px;
          }
        }
      }
    }
  }
}

.dialog-footer {
  margin-top: 24px;
  text-align: right;
  
  .el-button {
    margin-left: 12px;
  }
}
</style>
