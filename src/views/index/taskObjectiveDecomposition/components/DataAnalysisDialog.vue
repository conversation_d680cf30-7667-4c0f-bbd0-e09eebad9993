<!-- 数据分析弹窗组件 -->
<script setup lang="ts" name="DataAnalysisDialog">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, List, Grid } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

// 响应式状态
const loading = ref(false)
const activeTab = ref('taskDataAnalysis')
const timeFilter = ref('月')
const isMobile = ref(window.innerWidth <= 768)

// 分页相关
const tempReportCurrentPage = ref(1)
const businessSubTaskCurrentPage = ref(1)
const tempReportSubTaskCurrentPage = ref(1)

// 新增的小图表引用
const totalTasksMiniChartRef = ref()
const businessTasksMiniChartRef = ref()
const tempReportTasksMiniChartRef = ref()

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 标签页配置已在模板中直接定义

// 模拟数据
const taskAnalysisData = ref({
  totalTasks: 1284,
  businessTasks: 846,
  tempReportTasks: 438,
  totalTasksGrowth: 12.5,
  businessTasksGrowth: 8.2,
  tempReportTasksGrowth: -3.2
})

// 临时报表任务数据
const tempReportTaskData = ref([
  {
    id: 1,
    reportName: 'Q2销售业务分析',
    format: 'XLSX格式',
    creator: '王芳',
    createTime: '2023-06-15 09:32',
    status: '已完成'
  },
  {
    id: 2,
    reportName: '客户满意度调查',
    format: 'PDF格式',
    creator: '张伟',
    createTime: '2023-06-14 14:25',
    status: '进行中'
  },
  {
    id: 3,
    reportName: '产品利润率分析',
    format: 'XLSX格式',
    creator: '刘娜',
    createTime: '2023-06-12 16:48',
    status: '已失败'
  }
])

// 业务表子任务数据
const businessSubTaskData = ref([
  {
    id: 1,
    subTaskName: '区域销售数据汇总',
    mainTask: '销售数据月度汇总',
    assignee: '李明',
    priority: '高',
    status: '已完成',
    duration: '02:45:12'
  },
  {
    id: 2,
    subTaskName: '新用户行为分析',
    mainTask: '用户行为分析',
    assignee: '王芳',
    priority: '中',
    status: '进行中',
    duration: '01:12:45'
  },
  {
    id: 3,
    subTaskName: '库存预警信息检查',
    mainTask: '库存状态监控',
    assignee: '张伟',
    priority: '低',
    status: '已失败',
    duration: '00:32:18'
  }
])

// 临时报表子任务分析数据
const tempReportSubTaskAnalysisData = ref([
  {
    id: 1,
    subTaskName: '地区销售数据提取',
    reportName: 'Q2销售业务分析',
    dataVolume: '128,542 条',
    processTime: '01:12:36',
    status: '成功',
    successRate: '100%'
  },
  {
    id: 2,
    subTaskName: '客户评分统计',
    reportName: '客户满意度调查',
    dataVolume: '8,452 条',
    processTime: '00:24:18',
    status: '成功',
    successRate: '100%'
  },
  {
    id: 3,
    subTaskName: '产品成本计算',
    reportName: '产品利润率分析',
    dataVolume: '45,872 条',
    processTime: '00:48:32',
    status: '失败',
    successRate: '0%'
  }
])

// 方法
const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

const handleConfirm = () => {
  ElMessage.success('确认成功')
  handleClose()
}

const handleCancel = () => {
  handleClose()
}

// 图表相关
const trendChartRef = ref<HTMLElement>()
const trendChart = ref<echarts.ECharts>()
const durationChartRef = ref<HTMLElement>()
const durationChart = ref<echarts.ECharts>()
const successRateChartRef = ref<HTMLElement>()
const successRateChart = ref<echarts.ECharts>()

// 初始化小图表
const initMiniChart = (chartRef: any, data: number[], color: string) => {
  if (!chartRef.value) return

  const chart = echarts.init(chartRef.value)

  const option = {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    xAxis: {
      type: 'category',
      show: false,
      data: ['1', '2', '3', '4', '5', '6', '7']
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        type: 'line',
        data: data,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: color,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: color + '40' },
              { offset: 1, color: color + '10' }
            ]
          }
        }
      }
    ]
  }

  chart.setOption(option)
  return chart
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return

  trendChart.value = echarts.init(trendChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['业务表任务', '临时报表任务'],
      top: 10
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLine: {
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      axisLabel: {
        color: '#606266'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      axisLabel: {
        color: '#606266'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '业务表任务',
        type: 'line',
        smooth: true,
        data: [120, 132, 101, 134, 90, 230, 210, 220, 180, 200, 190, 250],
        lineStyle: {
          color: '#409eff'
        },
        itemStyle: {
          color: '#409eff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(64, 158, 255, 0.3)'
            }, {
              offset: 1, color: 'rgba(64, 158, 255, 0.05)'
            }]
          }
        }
      },
      {
        name: '临时报表任务',
        type: 'line',
        smooth: true,
        data: [60, 70, 50, 80, 45, 110, 100, 120, 90, 95, 85, 130],
        lineStyle: {
          color: '#67c23a'
        },
        itemStyle: {
          color: '#67c23a'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(103, 194, 58, 0.3)'
            }, {
              offset: 1, color: 'rgba(103, 194, 58, 0.05)'
            }]
          }
        }
      }
    ]
  }

  trendChart.value.setOption(option)
}

// 初始化处理时长分布图表
const initDurationChart = () => {
  if (!durationChartRef.value) return

  durationChart.value = echarts.init(durationChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['<30分钟', '30分钟-1小时', '1-2小时', '2-4小时', '>4小时'],
      axisLine: {
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      axisLabel: {
        color: '#606266',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      axisLabel: {
        color: '#606266'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [{
      data: [1200, 900, 800, 350, 120],
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#409eff'
          }, {
            offset: 1, color: '#66b1ff'
          }]
        }
      }
    }]
  }

  durationChart.value.setOption(option)
}

// 初始化成功率趋势图表
const initSuccessRateChart = () => {
  if (!successRateChartRef.value) return

  successRateChart.value = echarts.init(successRateChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}%'
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
      axisLine: {
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      axisLabel: {
        color: '#606266',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLine: {
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      axisLabel: {
        color: '#606266',
        formatter: '{value}%'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [{
      data: [85, 88, 92, 89, 94, 96],
      type: 'line',
      smooth: true,
      lineStyle: {
        color: '#67c23a',
        width: 3
      },
      itemStyle: {
        color: '#67c23a'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(103, 194, 58, 0.3)'
          }, {
            offset: 1, color: 'rgba(103, 194, 58, 0.05)'
          }]
        }
      }
    }]
  }

  successRateChart.value.setOption(option)
}

// 监听弹窗显示状态，初始化图表
watch(dialogVisible, (visible) => {
  if (visible) {
    nextTick(() => {
      initTrendChart()
      initDurationChart()
      initSuccessRateChart()
    })
  }
})

// 窗口大小监听
const handleResize = () => {
  isMobile.value = window.innerWidth <= 768
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize)

  // 延迟初始化图表，确保DOM已渲染
  nextTick(() => {
    // 初始化小图表
    initMiniChart(totalTasksMiniChartRef, [90, 95, 88, 92, 98, 105, 110], '#409eff')
    initMiniChart(businessTasksMiniChartRef, [85, 88, 82, 90, 95, 100, 105], '#67c23a')
    initMiniChart(tempReportTasksMiniChartRef, [75, 78, 72, 80, 85, 88, 90], '#9c27b0')

    // 初始化主图表
    initTrendChart()
    initDurationChart()
    initSuccessRateChart()
  })
})

// 清理事件监听
const cleanup = () => {
  window.removeEventListener('resize', handleResize)
}

// 分页处理函数
const handleTempReportPageChange = (page: number) => {
  tempReportCurrentPage.value = page
  // 这里可以添加数据加载逻辑
  console.log('临时报表页面变更:', page)
}

const handleBusinessSubTaskPageChange = (page: number) => {
  businessSubTaskCurrentPage.value = page
  // 这里可以添加数据加载逻辑
  console.log('业务子任务页面变更:', page)
}

const handleTempReportSubTaskPageChange = (page: number) => {
  tempReportSubTaskCurrentPage.value = page
  // 这里可以添加数据加载逻辑
  console.log('临时报表子任务页面变更:', page)
}

// 监听组件卸载
watch(dialogVisible, (visible) => {
  if (!visible) {
    cleanup()
  }
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="数据分析"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
    class="data-analysis-dialog"
    :style="{ '--el-dialog-width': isMobile ? '95%' : '1200px' }"
  >
    <div class="data-analysis-content" v-loading="loading" element-loading-text="正在加载数据...">
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" class="analysis-tabs">
        <!-- 任务数据分析标签页 -->
        <el-tab-pane label="任务数据分析" name="taskDataAnalysis">
          <div class="tab-content">
            <!-- 统计卡片区域 -->
            <div class="stats-cards-new">
              <div class="stat-card-new">
                <div class="stat-header">
                  <div class="stat-title">总任务数</div>
                  <div class="stat-icon blue">
                    <el-icon><List /></el-icon>
                  </div>
                </div>
                <div class="stat-value-large">{{ taskAnalysisData.totalTasks.toLocaleString() }}</div>
                <div class="stat-growth positive">
                  ↑ {{ taskAnalysisData.totalTasksGrowth }}% vs 上月
                </div>
                <div class="mini-chart">
                  <div ref="totalTasksMiniChartRef" class="mini-chart-content"></div>
                </div>
              </div>

              <div class="stat-card-new">
                <div class="stat-header">
                  <div class="stat-title">业务表任务</div>
                  <div class="stat-icon green">
                    <el-icon><Grid /></el-icon>
                  </div>
                </div>
                <div class="stat-value-large">{{ taskAnalysisData.businessTasks.toLocaleString() }}</div>
                <div class="stat-growth positive">
                  ↑ {{ taskAnalysisData.businessTasksGrowth }}% vs 上月
                </div>
                <div class="mini-chart">
                  <div ref="businessTasksMiniChartRef" class="mini-chart-content"></div>
                </div>
              </div>

              <div class="stat-card-new">
                <div class="stat-header">
                  <div class="stat-title">临时报表任务</div>
                  <div class="stat-icon purple">
                    <el-icon><Document /></el-icon>
                  </div>
                </div>
                <div class="stat-value-large">{{ taskAnalysisData.tempReportTasks.toLocaleString() }}</div>
                <div class="stat-growth negative">
                  ↓ {{ Math.abs(taskAnalysisData.tempReportTasksGrowth) }}% vs 上月
                </div>
                <div class="mini-chart">
                  <div ref="tempReportTasksMiniChartRef" class="mini-chart-content"></div>
                </div>
              </div>
            </div>

            <!-- 大图表区域 -->
            <div class="main-chart-section">
              <div class="chart-header">
                <h3>任务完成趋势</h3>
                <div class="time-filter">
                  <el-radio-group v-model="timeFilter" size="small">
                    <el-radio-button label="日">日</el-radio-button>
                    <el-radio-button label="周">周</el-radio-button>
                    <el-radio-button label="月">月</el-radio-button>
                    <el-radio-button label="年">年</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              <div ref="trendChartRef" class="main-chart-content"></div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 临时报表任务数据报告标签页 -->
        <el-tab-pane label="临时报表任务数据报告" name="tempReportTaskData">
          <div class="tab-content">
            <h3 class="tab-title">临时报表子任务数据分析报告</h3>

            <!-- 图表区域 -->
            <div class="charts-grid">
              <div class="chart-container">
                <h4>子任务处理时长分布</h4>
                <div ref="durationChartRef" class="chart-content"></div>
              </div>

              <div class="chart-container">
                <h4>子任务成功率趋势</h4>
                <div ref="successRateChartRef" class="chart-content"></div>
              </div>
            </div>
            
            <!-- 数据表格区域 -->
            <div class="table-section">
              <el-table :data="tempReportTaskData" style="width: 100%" height="300">
                <el-table-column prop="reportName" label="报表名称" min-width="150">
                  <template #default="{ row }">
                    <div class="report-name">
                      <el-icon style="margin-right: 8px; color: #909399;">
                        <Document />
                      </el-icon>
                      {{ row.reportName }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="creator" label="创建人" width="100" />
                <el-table-column prop="createTime" label="创建时间" width="160" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag
                      :type="row.status === '已完成' ? 'success' : row.status === '进行中' ? 'warning' : 'danger'"
                    >
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页 -->
              <div class="pagination-wrapper">
                <span class="pagination-info">显示 1 到 3 条，共 438 条记录</span>
                <el-pagination
                  v-model:current-page="tempReportCurrentPage"
                  :page-size="10"
                  :total="438"
                  layout="prev, pager, next"
                  :pager-count="5"
                  @current-change="handleTempReportPageChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 业务表子任务数据报告标签页 -->
        <el-tab-pane label="业务表子任务数据报告" name="businessSubTaskData">
          <div class="tab-content">
            <div class="business-report">
              <h3>业务表子任务数据报告</h3>
              <div class="progress-overview">
                <div class="progress-item">
                  <span class="progress-label">总子任务数</span>
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: 100%"></div>
                  </div>
                  <span class="progress-value">3,842</span>
                </div>
                <div class="progress-item">
                  <span class="progress-label">已完成子任务</span>
                  <div class="progress-bar">
                    <div class="progress-fill completed" style="width: 92%"></div>
                  </div>
                  <span class="progress-value">3,526</span>
                </div>
                <div class="progress-item">
                  <span class="progress-label">进行中子任务</span>
                  <div class="progress-bar">
                    <div class="progress-fill in-progress" style="width: 6%"></div>
                  </div>
                  <span class="progress-value">248</span>
                </div>
                <div class="progress-item">
                  <span class="progress-label">失败子任务</span>
                  <div class="progress-bar">
                    <div class="progress-fill failed" style="width: 2%"></div>
                  </div>
                  <span class="progress-value">68</span>
                </div>
              </div>

              <!-- 子任务详细数据表格 -->
              <div class="subtask-table">
                <h4>子任务详细信息</h4>
                <el-table :data="businessSubTaskData" style="width: 100%" height="250">
                  <el-table-column prop="subTaskName" label="子任务名称" min-width="150" />
                  <el-table-column prop="mainTask" label="主任务" min-width="120" />
                  <el-table-column prop="assignee" label="负责人" width="100" />
                  <el-table-column prop="priority" label="优先级" width="80">
                    <template #default="{ row }">
                      <el-tag
                        :type="row.priority === '高' ? 'danger' : row.priority === '中' ? 'warning' : 'info'"
                        size="small"
                      >
                        {{ row.priority }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag
                        :type="row.status === '已完成' ? 'success' : row.status === '进行中' ? 'warning' : 'danger'"
                        size="small"
                      >
                        {{ row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="duration" label="耗时" width="100" />
                </el-table>

                <!-- 分页 -->
                <div class="pagination-wrapper">
                  <span class="pagination-info">显示 1 到 3 条，共 3,842 条记录</span>
                  <el-pagination
                    v-model:current-page="businessSubTaskCurrentPage"
                    :page-size="10"
                    :total="3842"
                    layout="prev, pager, next"
                    :pager-count="5"
                    @current-change="handleBusinessSubTaskPageChange"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 临时报表子任务数据分析报告标签页 -->
        <el-tab-pane label="临时报表子任务数据分析报告" name="tempReportSubTaskAnalysis">
          <div class="tab-content">
            <h3 class="tab-title">临时报表任务数据报告</h3>

            <!-- 统计卡片区域 -->
            <div class="report-stats-cards">
              <div class="stat-card-item">
                <div class="stat-bg total">
                  <div class="stat-number">438</div>
                  <div class="stat-label">总临时报表任务</div>
                </div>
              </div>

              <div class="stat-card-item">
                <div class="stat-bg completed">
                  <div class="stat-number">392</div>
                  <div class="stat-label">已完成</div>
                </div>
              </div>

              <div class="stat-card-item">
                <div class="stat-bg in-progress">
                  <div class="stat-number">31</div>
                  <div class="stat-label">进行中</div>
                </div>
              </div>

              <div class="stat-card-item">
                <div class="stat-bg failed">
                  <div class="stat-number">15</div>
                  <div class="stat-label">已失败</div>
                </div>
              </div>
            </div>

            <!-- 详细数据表格 -->
            <div class="detail-table">
              <h4>子任务详细分析数据</h4>
              <el-table :data="tempReportSubTaskAnalysisData" style="width: 100%" height="250">
                <el-table-column prop="subTaskName" label="子任务名称" min-width="150" />
                <el-table-column prop="reportName" label="所属报表" min-width="120" />
                <el-table-column prop="dataVolume" label="数据量" width="100" />
                <el-table-column prop="processTime" label="处理时间" width="100" />
                <el-table-column prop="status" label="状态" width="80">
                  <template #default="{ row }">
                    <el-tag
                      :type="row.status === '成功' ? 'success' : 'danger'"
                      size="small"
                    >
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="successRate" label="成功率" width="80">
                  <template #default="{ row }">
                    <div class="success-rate" :class="{ 'success': row.successRate === '100%', 'failed': row.successRate === '0%' }">
                      {{ row.successRate }}
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页 -->
              <div class="pagination-wrapper">
                <span class="pagination-info">显示 1 到 3 条，共 1,582 条记录</span>
                <el-pagination
                  v-model:current-page="tempReportSubTaskCurrentPage"
                  :page-size="10"
                  :total="1582"
                  layout="prev, pager, next"
                  :pager-count="5"
                  @current-change="handleTempReportSubTaskPageChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.data-analysis-dialog {
  .data-analysis-content {
    min-height: 600px;
  }

  .analysis-tabs {
    .tab-content {
      padding: 20px 0;

      .tab-title {
        margin: 0 0 20px 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  // 统计卡片样式
  .stats-cards {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 15px;
    }

    .stat-card {
      flex: 1;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 15px;

      .stat-icon {
        font-size: 32px;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        border-radius: 50%;
      }

      .stat-info {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #606266;
          margin-bottom: 4px;
        }

        .stat-growth {
          font-size: 12px;

          &.positive {
            color: #67c23a;
          }

          &.negative {
            color: #f56c6c;
          }
        }
      }
    }
  }

  // 新的统计卡片样式
  .stats-cards-new {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;

    .stat-card-new {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #ebeef5;

      .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .stat-title {
          font-size: 14px;
          color: #606266;
          font-weight: 500;
        }

        .stat-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;

          &.blue {
            background: #409eff;
          }

          &.green {
            background: #67c23a;
          }

          &.purple {
            background: #9c27b0;
          }
        }
      }

      .stat-value-large {
        font-size: 32px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 8px;
      }

      .stat-growth {
        font-size: 12px;
        margin-bottom: 16px;

        &.positive {
          color: #67c23a;
        }

        &.negative {
          color: #f56c6c;
        }
      }

      .mini-chart {
        height: 60px;

        .mini-chart-content {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  // 主图表区域
  .main-chart-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #ebeef5;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        font-size: 16px;
        color: #303133;
      }

      .time-filter {
        .el-radio-group {
          .el-radio-button {
            .el-radio-button__inner {
              padding: 8px 16px;
              font-size: 12px;
            }
          }
        }
      }
    }

    .main-chart-content {
      height: 400px;
      width: 100%;
    }
  }

  // 图表区域样式
  .chart-section {
    .chart-container {
      background: white;
      border-radius: 8px;
      padding: 20px;
      border: 1px solid #ebeef5;

      h3 {
        margin: 0 0 20px 0;
        font-size: 16px;
        color: #303133;
      }

      .chart-content {
        height: 300px;
        width: 100%;

        &.small {
          height: 200px;
        }
      }

      .chart-placeholder {
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 4px;
        color: #909399;
        font-size: 16px;

        &.small {
          height: 200px;
        }
      }
    }
  }

  // 报告摘要样式
  .report-summary {
    margin-bottom: 30px;

    h3 {
      margin: 0 0 20px 0;
      font-size: 18px;
      color: #303133;
    }

    .summary-stats {
      display: flex;
      gap: 30px;
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;

      .summary-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;

        .summary-label {
          font-size: 14px;
          color: #606266;
        }

        .summary-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;

          &.completed {
            color: #67c23a;
          }

          &.in-progress {
            color: #e6a23c;
          }

          &.failed {
            color: #f56c6c;
          }
        }
      }
    }
  }

  // 进度概览样式
  .progress-overview {
    .progress-item {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 15px;

      .progress-label {
        width: 120px;
        font-size: 14px;
        color: #606266;
      }

      .progress-bar {
        flex: 1;
        height: 8px;
        background: #f0f0f0;
        border-radius: 4px;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background: #409eff;
          transition: width 0.3s ease;

          &.completed {
            background: #67c23a;
          }

          &.in-progress {
            background: #e6a23c;
          }

          &.failed {
            background: #f56c6c;
          }
        }
      }

      .progress-value {
        width: 60px;
        text-align: right;
        font-weight: bold;
        color: #303133;
      }
    }
  }

  // 子任务表格样式
  .subtask-table {
    margin-top: 30px;

    h4 {
      margin: 0 0 15px 0;
      font-size: 16px;
      color: #303133;
    }

    .pagination-info {
      margin-top: 16px;
      text-align: right;
      color: #909399;
      font-size: 14px;
    }
  }

  // 分页样式
  .pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;

    .pagination-info {
      color: #909399;
      font-size: 14px;
    }

    .el-pagination {
      .el-pager {
        .number {
          min-width: 32px;
          height: 32px;
          line-height: 32px;
          font-size: 14px;
        }
      }

      .btn-prev,
      .btn-next {
        min-width: 32px;
        height: 32px;
        line-height: 32px;
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 12px;
      align-items: center;
    }
  }

  // 分析图表样式
  .analysis-charts {
    .chart-row {
      display: flex;
      gap: 20px;
      margin-bottom: 30px;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 15px;
      }

      .chart-item {
        flex: 1;
        background: white;
        border: 1px solid #ebeef5;
        border-radius: 8px;
        padding: 20px;

        @media (max-width: 768px) {
          padding: 15px;
        }

        h4 {
          margin: 0 0 15px 0;
          font-size: 14px;
          color: #303133;
        }
      }
    }
  }

  // 表格样式
  .table-section {
    .report-name {
      display: flex;
      align-items: center;
    }

    .pagination-info {
      margin-top: 16px;
      text-align: right;
      color: #909399;
      font-size: 14px;
    }

    .table-placeholder {
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;
      border-radius: 4px;
      color: #909399;
      font-size: 16px;
      border: 1px solid #ebeef5;
    }
  }

  .detail-table {
    h4 {
      margin: 0 0 15px 0;
      font-size: 16px;
      color: #303133;
    }

    .success-rate {
      font-weight: bold;

      &.success {
        color: #67c23a;
      }

      &.failed {
        color: #f56c6c;
      }
    }

    .pagination-info {
      margin-top: 16px;
      text-align: right;
      color: #909399;
      font-size: 14px;
    }

    .table-placeholder {
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;
      border-radius: 4px;
      color: #909399;
      font-size: 16px;
      border: 1px solid #ebeef5;
    }
  }

  // 第四个标签页的统计卡片样式
  .report-stats-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;

    .stat-card-item {
      .stat-bg {
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        color: white;

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.completed {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.in-progress {
          background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
          color: #333;
        }

        &.failed {
          background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
          color: #333;
        }

        .stat-number {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 14px;
          opacity: 0.9;
        }
      }
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }
  }
}
</style>
