<!-- 业务表任务接口管理弹窗 -->
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 弹窗状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 创建接口弹窗状态
const createInterfaceVisible = ref(false)
const editInterfaceVisible = ref(false)
const loading = ref(false)

// 查询表单
const queryForm = reactive({
  interfaceName: '',
  interfaceType: '',
  belongingBusinessTable: '',
  status: ''
})

// 原始表格数据（更真实的数据）
const originalTableData = ref([
  {
    id: 1,
    interfaceName: '/api/household/create',
    interfaceType: '创建',
    belongingBusinessTable: '户籍改造业务表',
    status: '启用',
    createTime: '2024-01-15 10:30:25',
    updateTime: '2024-01-20 14:22:18',
    creator: '张三',
    description: '创建户籍信息接口'
  },
  {
    id: 2,
    interfaceName: '/api/household/query',
    interfaceType: '查询',
    belongingBusinessTable: '户籍改造业务表',
    status: '启用',
    createTime: '2024-01-15 10:32:15',
    updateTime: '2024-01-18 09:15:30',
    creator: '李四',
    description: '查询户籍信息接口'
  },
  {
    id: 3,
    interfaceName: '/api/household/update',
    interfaceType: '修改',
    belongingBusinessTable: '户籍改造业务表',
    status: '启用',
    createTime: '2024-01-15 10:35:42',
    updateTime: '2024-01-19 16:45:12',
    creator: '王五',
    description: '更新户籍信息接口'
  },
  {
    id: 4,
    interfaceName: '/api/household/delete',
    interfaceType: '删除',
    belongingBusinessTable: '户籍改造业务表',
    status: '禁用',
    createTime: '2024-01-15 10:38:20',
    updateTime: '2024-01-22 11:20:45',
    creator: '赵六',
    description: '删除户籍信息接口'
  },
  {
    id: 5,
    interfaceName: '/api/unemployment/register',
    interfaceType: '创建',
    belongingBusinessTable: '失业人员登记表',
    status: '启用',
    createTime: '2024-01-16 09:15:30',
    updateTime: '2024-01-21 13:40:22',
    creator: '孙七',
    description: '失业人员登记接口'
  },
  {
    id: 6,
    interfaceName: '/api/unemployment/list',
    interfaceType: '查询',
    belongingBusinessTable: '失业人员登记表',
    status: '启用',
    createTime: '2024-01-16 09:18:45',
    updateTime: '2024-01-20 15:30:18',
    creator: '周八',
    description: '失业人员查询接口'
  },
  {
    id: 7,
    interfaceName: '/api/unemployment/modify',
    interfaceType: '修改',
    belongingBusinessTable: '失业人员登记表',
    status: '启用',
    createTime: '2024-01-16 09:22:10',
    updateTime: '2024-01-19 10:25:35',
    creator: '吴九',
    description: '失业人员信息修改接口'
  },
  {
    id: 8,
    interfaceName: '/api/census/population/add',
    interfaceType: '创建',
    belongingBusinessTable: '人口普查登记表',
    status: '启用',
    createTime: '2024-01-17 14:20:15',
    updateTime: '2024-01-23 09:10:40',
    creator: '郑十',
    description: '人口普查数据录入接口'
  },
  {
    id: 9,
    interfaceName: '/api/census/population/search',
    interfaceType: '查询',
    belongingBusinessTable: '人口普查登记表',
    status: '启用',
    createTime: '2024-01-17 14:25:30',
    updateTime: '2024-01-22 16:55:20',
    creator: '钱一',
    description: '人口普查数据查询接口'
  },
  {
    id: 10,
    interfaceName: '/api/elderly/info/create',
    interfaceType: '创建',
    belongingBusinessTable: '老年人登记表',
    status: '启用',
    createTime: '2024-01-18 11:40:25',
    updateTime: '2024-01-24 14:15:50',
    creator: '陈二',
    description: '老年人信息登记接口'
  },
  {
    id: 11,
    interfaceName: '/api/farmland/area/register',
    interfaceType: '创建',
    belongingBusinessTable: '耕地面积业务表',
    status: '禁用',
    createTime: '2024-01-19 08:30:15',
    updateTime: '2024-01-25 10:20:30',
    creator: '刘三',
    description: '耕地面积登记接口'
  },
  {
    id: 12,
    interfaceName: '/api/farmland/area/query',
    interfaceType: '查询',
    belongingBusinessTable: '耕地面积业务表',
    status: '启用',
    createTime: '2024-01-19 08:35:40',
    updateTime: '2024-01-23 15:45:25',
    creator: '杨四',
    description: '耕地面积查询接口'
  }
])

// 过滤后的表格数据
const tableData = ref([...originalTableData.value])

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: computed(() => tableData.value.length)
})

// 创建/编辑接口表单数据
const interfaceForm = reactive({
  id: null,
  interfaceName: '',
  requestMethod: '',
  interfaceType: '',
  belongingBusinessTable: '',
  interfacePath: '',
  requestParams: '',
  responseExample: '',
  status: '',
  description: ''
})

// 当前编辑的接口
const currentEditInterface = ref(null)

// 请求方法选项
const requestMethodOptions = [
  { value: 'GET', label: 'GET' },
  { value: 'POST', label: 'POST' },
  { value: 'PUT', label: 'PUT' },
  { value: 'DELETE', label: 'DELETE' }
]

// 接口类型选项
const interfaceTypeOptions = [
  { value: '创建', label: '创建' },
  { value: '查询', label: '查询' },
  { value: '修改', label: '修改' },
  { value: '删除', label: '删除' }
]

// 所属业务表选项
const businessTableOptions = [
  { value: '户籍改造业务表', label: '户籍改造业务表' },
  { value: '失业人员登记表', label: '失业人员登记表' },
  { value: '人口普查登记表', label: '人口普查登记表' },
  { value: '老年人登记表', label: '老年人登记表' },
  { value: '耕地面积业务表', label: '耕地面积业务表' }
]

// 接口状态选项
const statusOptions = [
  { value: '启用', label: '启用' },
  { value: '禁用', label: '禁用' }
]

// 分页数据
const paginatedData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return tableData.value.slice(start, end)
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleQuery = async () => {
  loading.value = true
  try {
    // 模拟接口请求延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    // 执行查询过滤
    let filteredData = [...originalTableData.value]

    if (queryForm.interfaceName) {
      filteredData = filteredData.filter(item =>
        item.interfaceName.toLowerCase().includes(queryForm.interfaceName.toLowerCase())
      )
    }

    if (queryForm.interfaceType) {
      filteredData = filteredData.filter(item =>
        item.interfaceType === queryForm.interfaceType
      )
    }

    if (queryForm.belongingBusinessTable) {
      filteredData = filteredData.filter(item =>
        item.belongingBusinessTable === queryForm.belongingBusinessTable
      )
    }

    if (queryForm.status) {
      filteredData = filteredData.filter(item =>
        item.status === queryForm.status
      )
    }

    tableData.value = filteredData
    pagination.currentPage = 1

    ElMessage.success(`查询完成，共找到 ${filteredData.length} 条记录`)
  } catch (error) {
    ElMessage.error('查询失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  queryForm.interfaceName = ''
  queryForm.interfaceType = ''
  queryForm.belongingBusinessTable = ''
  queryForm.status = ''
  tableData.value = [...originalTableData.value]
  pagination.currentPage = 1
  ElMessage.info('查询条件已重置')
}

const handleCreateInterface = () => {
  createInterfaceVisible.value = true
  resetInterfaceForm()
}

const resetInterfaceForm = () => {
  interfaceForm.id = null
  interfaceForm.interfaceName = ''
  interfaceForm.requestMethod = ''
  interfaceForm.interfaceType = ''
  interfaceForm.belongingBusinessTable = ''
  interfaceForm.interfacePath = ''
  interfaceForm.requestParams = ''
  interfaceForm.responseExample = ''
  interfaceForm.status = ''
  interfaceForm.description = ''
  currentEditInterface.value = null
}

const handleInterfaceCancel = () => {
  createInterfaceVisible.value = false
  resetInterfaceForm()
}

const handleInterfaceConfirm = async () => {
  // 验证表单
  if (!interfaceForm.interfaceName || !interfaceForm.requestMethod ||
      !interfaceForm.interfaceType || !interfaceForm.belongingBusinessTable ||
      !interfaceForm.interfacePath || !interfaceForm.status) {
    ElMessage.warning('请填写完整的接口信息')
    return
  }

  loading.value = true
  try {
    // 模拟接口请求延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (interfaceForm.id) {
      // 编辑模式
      const index = originalTableData.value.findIndex(item => item.id === interfaceForm.id)
      if (index > -1) {
        originalTableData.value[index] = {
          ...originalTableData.value[index],
          interfaceName: interfaceForm.interfaceName,
          interfaceType: interfaceForm.interfaceType,
          belongingBusinessTable: interfaceForm.belongingBusinessTable,
          status: interfaceForm.status,
          description: interfaceForm.description,
          updateTime: new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }).replace(/\//g, '-')
        }

        // 更新显示数据
        const displayIndex = tableData.value.findIndex(item => item.id === interfaceForm.id)
        if (displayIndex > -1) {
          tableData.value[displayIndex] = originalTableData.value[index]
        }

        editInterfaceVisible.value = false
        ElMessage.success('接口更新成功')
      }
    } else {
      // 创建模式
      const newInterface = {
        id: Math.max(...originalTableData.value.map(item => item.id)) + 1,
        interfaceName: interfaceForm.interfaceName,
        interfaceType: interfaceForm.interfaceType,
        belongingBusinessTable: interfaceForm.belongingBusinessTable,
        status: interfaceForm.status,
        description: interfaceForm.description,
        createTime: new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }).replace(/\//g, '-'),
        updateTime: new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }).replace(/\//g, '-'),
        creator: '当前用户'
      }

      originalTableData.value.unshift(newInterface)
      tableData.value.unshift(newInterface)

      createInterfaceVisible.value = false
      ElMessage.success('接口创建成功')
    }

    resetInterfaceForm()
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleEdit = (row: any) => {
  currentEditInterface.value = row
  interfaceForm.id = row.id
  interfaceForm.interfaceName = row.interfaceName
  interfaceForm.interfaceType = row.interfaceType
  interfaceForm.belongingBusinessTable = row.belongingBusinessTable
  interfaceForm.status = row.status
  interfaceForm.description = row.description || ''
  interfaceForm.interfacePath = row.interfaceName // 使用接口名称作为路径
  interfaceForm.requestMethod = row.interfaceType === '查询' ? 'GET' : 'POST'
  interfaceForm.requestParams = '{\n  "page": 1,\n  "size": 10\n}'
  interfaceForm.responseExample = '{\n  "code": 200,\n  "message": "success",\n  "data": {}\n}'

  editInterfaceVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除接口"${row.interfaceName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<template>
  <el-dialog
    v-model="visible"
    title="业务表任务接口管理"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="interface-management-content" v-loading="loading">
      <!-- 查询表单区域 -->
      <div class="query-form">
        <el-form :model="queryForm" inline>
          <el-form-item label="接口名称">
            <el-input
              v-model="queryForm.interfaceName"
              placeholder="请输入接口名称"
              style="width: 200px"
              clearable
            />
          </el-form-item>
          <el-form-item label="接口类型">
            <el-select
              v-model="queryForm.interfaceType"
              placeholder="请选择接口类型"
              style="width: 150px"
              clearable
            >
              <el-option
                v-for="option in interfaceTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="所属业务表">
            <el-select
              v-model="queryForm.belongingBusinessTable"
              placeholder="请选择业务表"
              style="width: 180px"
              clearable
            >
              <el-option
                v-for="option in businessTableOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="queryForm.status"
              placeholder="请选择状态"
              style="width: 120px"
              clearable
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" :loading="loading">
              查询
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-bar">
        <el-button type="primary" @click="handleCreateInterface">
          创建接口
        </el-button>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <el-table
          :data="paginatedData"
          style="width: 100%"
          border
          stripe
        >
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="interfaceName" label="接口名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="interfaceType" label="接口类型" width="100" align="center" />
          <el-table-column prop="belongingBusinessTable" label="所属业务表" width="150" align="center" />
          <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
          <el-table-column prop="creator" label="创建人" width="100" align="center" />
          <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === '启用' ? 'success' : 'danger'">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleClose">确定</el-button>
      </div>
    </template>

    <!-- 创建接口弹窗 -->
    <el-dialog
      v-model="createInterfaceVisible"
      title="创建接口"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="create-interface-content">
        <div class="form-item">
          <div class="form-label">
            <span class="required">*</span>接口名称
          </div>
          <el-input
            v-model="interfaceForm.interfaceName"
            placeholder="请输入接口路径，如：/api/user/create"
            style="width: 100%"
          />
        </div>

        <div class="form-item">
          <div class="form-label">
            <span class="required">*</span>请求方法
          </div>
          <div class="form-description">GET、POST、PUT、DELETE</div>
          <el-select
            v-model="interfaceForm.requestMethod"
            placeholder="请选择请求方法"
            style="width: 100%; margin-top: 8px;"
          >
            <el-option
              v-for="option in requestMethodOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <div class="form-item">
          <div class="form-label">
            <span class="required">*</span>接口类型
          </div>
          <div class="form-description">创建、查询、修改、删除</div>
          <el-select
            v-model="interfaceForm.interfaceType"
            placeholder="请选择接口类型"
            style="width: 100%; margin-top: 8px;"
          >
            <el-option
              v-for="option in interfaceTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <div class="form-item">
          <div class="form-label">
            <span class="required">*</span>所属业务表
          </div>
          <div class="form-description">户籍改造业务表、失业人员登记表、人口普查登记表、老年人登记表、耕地面积业务表</div>
          <el-select
            v-model="interfaceForm.belongingBusinessTable"
            placeholder="请选择所属业务表"
            style="width: 100%; margin-top: 8px;"
          >
            <el-option
              v-for="option in businessTableOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <div class="form-item">
          <div class="form-label">接口描述</div>
          <el-input
            v-model="interfaceForm.description"
            placeholder="请输入接口描述"
            style="width: 100%"
          />
        </div>

        <div class="form-item">
          <div class="form-label">
            <span class="required">*</span>接口状态
          </div>
          <div class="form-description">启用、禁用</div>
          <el-select
            v-model="interfaceForm.status"
            placeholder="请选择接口状态"
            style="width: 100%; margin-top: 8px;"
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleInterfaceCancel">取消</el-button>
          <el-button type="primary" @click="handleInterfaceConfirm" :loading="loading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑接口弹窗 -->
    <el-dialog
      v-model="editInterfaceVisible"
      title="编辑接口"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="create-interface-content">
        <div class="form-item">
          <div class="form-label">
            <span class="required">*</span>接口名称
          </div>
          <el-input
            v-model="interfaceForm.interfaceName"
            placeholder="请输入接口路径，如：/api/user/create"
            style="width: 100%"
          />
        </div>

        <div class="form-item">
          <div class="form-label">
            <span class="required">*</span>接口类型
          </div>
          <div class="form-description">创建、查询、修改、删除</div>
          <el-select
            v-model="interfaceForm.interfaceType"
            placeholder="请选择接口类型"
            style="width: 100%; margin-top: 8px;"
          >
            <el-option
              v-for="option in interfaceTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <div class="form-item">
          <div class="form-label">
            <span class="required">*</span>所属业务表
          </div>
          <div class="form-description">户籍改造业务表、失业人员登记表、人口普查登记表、老年人登记表、耕地面积业务表</div>
          <el-select
            v-model="interfaceForm.belongingBusinessTable"
            placeholder="请选择所属业务表"
            style="width: 100%; margin-top: 8px;"
          >
            <el-option
              v-for="option in businessTableOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <div class="form-item">
          <div class="form-label">接口描述</div>
          <el-input
            v-model="interfaceForm.description"
            placeholder="请输入接口描述"
            style="width: 100%"
          />
        </div>

        <div class="form-item">
          <div class="form-label">
            <span class="required">*</span>接口状态
          </div>
          <div class="form-description">启用、禁用</div>
          <el-select
            v-model="interfaceForm.status"
            placeholder="请选择接口状态"
            style="width: 100%; margin-top: 8px;"
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editInterfaceVisible = false; resetInterfaceForm()">取消</el-button>
          <el-button type="primary" @click="handleInterfaceConfirm" :loading="loading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<style scoped lang="scss">
.interface-management-content {
  .query-form {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 16px;

    .el-form-item {
      margin-bottom: 16px;
    }
  }

  .action-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
    gap: 12px;
  }

  .table-container {
    .pagination-wrapper {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
}

.create-interface-content {
  .form-item {
    margin-bottom: 24px;

    .form-label {
      font-size: 14px;
      color: #303133;
      margin-bottom: 8px;
      font-weight: 500;

      .required {
        color: #f56c6c;
        margin-right: 4px;
      }
    }

    .form-description {
      font-size: 13px;
      color: #909399;
      line-height: 1.5;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
