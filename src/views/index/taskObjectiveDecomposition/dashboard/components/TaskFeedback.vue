<!-- 任务反馈Tab组件 -->
<script setup lang="ts" name="TaskFeedback">
import { ref, computed, onMounted } from 'vue'
import BaseTableComp from '@/components/common/basetable-comp.vue'
import { useDashboardStore } from '@/stores/dashboardStore'

// Store
const dashboardStore = useDashboardStore()

// 页面状态
const searchKeyword = ref('')

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 统计数据
const statisticsData = computed(() => [
  { label: '业务责任务', count: 14, color: '#409eff' },
  { label: '临时报表任务', count: 17, color: '#67c23a' },
  { label: '业务责子任务', count: 31, color: '#e6a23c' },
  { label: '临时报表子任务统计', count: 41, color: '#f56c6c' }
])

// 表格列配置
const tableColumns = [
  { field: 'feedbackType', title: '反馈类型', width: 120, align: 'center' },
  { field: 'feedbackContent', title: '反馈内容', minWidth: 400 },
  { field: 'feedbackAccount', title: '反馈账号', width: 180, align: 'center' },
  { field: 'feedbackTime', title: '反馈时间', width: 180, align: 'center' }
]

// 计算属性
const filteredData = computed(() => {
  let data = dashboardStore.taskFeedbacks
  if (searchKeyword.value) {
    data = data.filter(item => 
      item.feedbackContent.includes(searchKeyword.value) ||
      item.feedbackAccount.includes(searchKeyword.value) ||
      item.feedbackType.includes(searchKeyword.value)
    )
  }
  pagination.value.total = data.length
  return data
})

const paginatedData = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredData.value.slice(start, end)
})

// 方法
const handleSearch = () => {
  pagination.value.currentPage = 1
}

const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
}

const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
}

const getFeedbackTypeClass = (feedbackType: string) => {
  switch (feedbackType) {
    case '问题反馈': return 'warning'
    case '改进建议': return 'success'
    case '功能需求': return 'primary'
    case '其他': return 'info'
    default: return 'info'
  }
}

// 生命周期
onMounted(() => {
  console.log('任务反馈组件已加载')
})
</script>

<template>
  <div class="task-feedback">
    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <div
        v-for="(item, index) in statisticsData"
        :key="index"
        class="stat-card"
        :style="{ borderTopColor: item.color }"
      >
        <div class="stat-label">{{ item.label }}</div>
        <div class="stat-count">{{ item.count }}</div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <BaseTableComp
        :data="paginatedData"
        :colData="tableColumns"
        :loading="dashboardStore.loading"
        :checkbox="false"
        :visibleHeader="false"
        :visibleSetting="false"
        :visibleIndex="true"
        :currentPage="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :total="pagination.total"
        :auto-height="false"
        :height="500"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.task-feedback {
  .statistics-cards {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .stat-card {
      flex: 1;
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-top: 4px solid #409eff;
      text-align: center;

      .stat-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .stat-count {
        font-size: 28px;
        font-weight: bold;
        color: #333;
      }
    }
  }

  .table-section {
    background: white;
    border-radius: 6px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .task-feedback {
    .search-section {
      padding: 12px;

      .search-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;

        .el-input {
          width: 100% !important;
        }
      }
    }
  }
}
</style>
