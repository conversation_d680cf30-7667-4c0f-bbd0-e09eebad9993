<!-- 审核管理Tab组件 -->
<script setup lang="ts" name="AuditManagement">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import BaseTableComp from '@/components/common/basetable-comp.vue'
import { useDashboardStore } from '@/stores/dashboardStore'
import UserPermissionChart from './UserPermissionChart.vue'
import UserRoleChart from './UserRoleChart.vue'
import GenericChart from './GenericChart.vue'

// Store
const dashboardStore = useDashboardStore()

// 页面状态
const searchKeyword = ref('')

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格列配置
const tableColumns = [
  { field: 'auditCategory', title: '审核分类', width: 120, align: 'center' },
  { field: 'applicationContent', title: '申请内容', minWidth: 200 },
  { field: 'applicationDepartment', title: '申请部门', width: 180 },
  { field: 'applicant', title: '申请人', width: 100, align: 'center' },
  { field: 'applicationTime', title: '申请时间', width: 150, align: 'center' },
  { field: 'status', title: '状态', width: 100, align: 'center', slot: true }
]

// 表格操作按钮
const tableButtons = [
  { type: 'success' as const, code: 'approve', title: '通过', icon: '', verify: 'true', more: false, showBtn: 'true' },
  { type: 'danger' as const, code: 'reject', title: '拒绝', icon: '', verify: 'true', more: false, showBtn: 'true' }
]

// 计算属性
const filteredData = computed(() => {
  let data = dashboardStore.auditRecords
  if (searchKeyword.value) {
    data = data.filter(item =>
      item.applicationContent.includes(searchKeyword.value) ||
      item.applicant.includes(searchKeyword.value) ||
      item.applicationDepartment.includes(searchKeyword.value)
    )
  }
  pagination.value.total = data.length
  return data
})

const paginatedData = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredData.value.slice(start, end)
})

// 方法
// 搜索防抖
let searchTimer: number | null = null
const handleSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = window.setTimeout(() => {
    pagination.value.currentPage = 1
    console.log('搜索关键词:', searchKeyword.value)
  }, 300)
}

const handleTableButton = (button: any, row: any) => {
  if (button.code === 'approve') {
    handleApprove(row)
  } else if (button.code === 'reject') {
    handleReject(row)
  }
}

const handleApprove = (row: any) => {
  dashboardStore.updateAuditStatus(row.id, 'approved')
  ElMessage.success(`已通过 ${row.applicant} 的申请`)
}

const handleReject = (row: any) => {
  dashboardStore.updateAuditStatus(row.id, 'rejected')
  ElMessage.warning(`已拒绝 ${row.applicant} 的申请`)
}

const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
}

const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
}

const handleRefresh = async () => {
  try {
    await dashboardStore.refreshData()
    if (dashboardStore.error) {
      ElMessage.error(dashboardStore.error)
    } else {
      ElMessage.success('数据刷新成功')
    }
  } catch (error) {
    ElMessage.error('数据刷新失败，请稍后重试')
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'approved': return 'success'
    case 'rejected': return 'danger'
    case 'pending': return 'warning'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'approved': return '已通过'
    case 'rejected': return '已拒绝'
    case 'pending': return '待审核'
    default: return '未知'
  }
}

// 生命周期
onMounted(async () => {
  try {
    await dashboardStore.initialize()
    console.log('审核管理组件已加载')
  } catch (error) {
    console.error('组件初始化失败:', error)
  }
})

onUnmounted(() => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
})
</script>

<template>
  <div class="audit-management" v-loading="dashboardStore.loading" element-loading-text="正在加载数据...">
    <!-- 错误提示 -->
    <div v-if="dashboardStore.error" class="error-section">
      <el-alert
        :title="dashboardStore.error"
        type="error"
        :closable="true"
        @close="dashboardStore.clearError"
        show-icon
      />
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-controls">
        <div class="search-left">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入申请内容、申请人或申请部门"
            style="width: 300px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </div>
        <div class="search-right">
          <el-button
            @click="handleRefresh"
            :loading="dashboardStore.loading"
            :disabled="dashboardStore.loading"
          >
            {{ dashboardStore.loading ? '正在刷新...' : '刷新数据' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <BaseTableComp
        :data="paginatedData"
        :colData="tableColumns"
        :buttons="tableButtons"
        :loading="dashboardStore.loading"
        :checkbox="false"
        :visibleHeader="false"
        :visibleSetting="false"
        :visibleIndex="true"
        :currentPage="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :total="pagination.total"
        :auto-height="false"
        :height="400"
        @clickButton="(data: any) => handleTableButton(data.btn, data.scope)"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #status="{ rowData }">
          <el-tag :type="getStatusType(rowData.status)">
            {{ getStatusText(rowData.status) }}
          </el-tag>
        </template>
      </BaseTableComp>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-section">
      <div class="charts-title">
        <h3>数据分析图表</h3>
      </div>
      <div class="charts-grid">
        <!-- 用户权限分类统计 -->
        <div class="chart-item">
          <UserPermissionChart />
        </div>

        <!-- 用户角色分类统计 -->
        <div class="chart-item">
          <UserRoleChart />
        </div>

        <!-- 部门审核进度分析 -->
        <div class="chart-item">
          <GenericChart chart-id="departmentAuditProgress" />
        </div>

        <!-- 成员审核进度分析 -->
        <div class="chart-item">
          <GenericChart chart-id="memberAuditProgress" />
        </div>

        <!-- 任务数据审核进度分析 -->
        <div class="chart-item">
          <GenericChart chart-id="taskDataAuditProgress" />
        </div>

        <!-- 子任务数据审核进度分析 -->
        <div class="chart-item">
          <GenericChart chart-id="subtaskDataAuditProgress" />
        </div>

        <!-- 任务关系审核进度分析 -->
        <div class="chart-item">
          <GenericChart chart-id="taskRelationAuditProgress" />
        </div>

        <!-- 子任务属性审核进度分析 -->
        <div class="chart-item">
          <GenericChart chart-id="subtaskAttributeAuditProgress" />
        </div>

        <!-- 可视化布局审核进度分析 -->
        <div class="chart-item">
          <GenericChart chart-id="visualLayoutAuditProgress" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.audit-management {
  .error-section {
    margin-bottom: 20px;
  }

  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .search-controls {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;

      .search-left {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .search-right {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .table-section {
    margin-bottom: 30px;
    background: white;
    border-radius: 6px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .charts-section {
    .charts-title {
      margin-bottom: 20px;
      
      h3 {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }
    }

    .charts-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;

      .chart-item {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        min-height: 300px;

        .chart-placeholder {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .placeholder-content {
            text-align: center;

            .placeholder-icon {
              font-size: 48px;
              margin-bottom: 12px;
            }

            .placeholder-text {
              font-size: 16px;
              font-weight: 600;
              color: #303133;
              margin-bottom: 8px;
            }

            .placeholder-desc {
              font-size: 14px;
              color: #909399;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .audit-management {
    .charts-section {
      .charts-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

@media (max-width: 768px) {
  .audit-management {
    .search-section {
      padding: 12px;

      .search-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;

        .el-input {
          width: 100% !important;
        }
      }
    }

    .charts-section {
      .charts-grid {
        grid-template-columns: 1fr;
        gap: 16px;

        .chart-item {
          padding: 16px;
          min-height: 250px;
        }
      }
    }
  }
}
</style>
