<!-- 通用图表组件 -->
<script setup lang="ts" name="Generic<PERSON>hart">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { useDashboardStore } from '@/stores/dashboardStore'

interface Props {
  chartId: string
  title?: string
}

const props = defineProps<Props>()

// Store
const dashboardStore = useDashboardStore()

// 图表引用
const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 图表数据
const chartData = computed(() => {
  return dashboardStore.getChartData(props.chartId)
})

// 图表配置
const chartOption = computed(() => {
  if (!chartData.value?.data) return null

  const data = chartData.value.data
  const chartType = chartData.value.type
  const title = props.title || chartData.value.title

  // 基础配置
  const baseOption = {
    title: {
      text: title,
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: chartType === 'pie' ? 'item' : 'axis',
      formatter: chartType === 'pie' 
        ? '{a} <br/>{b}: {c} ({d}%)'
        : '{b}: {c}'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    }
  }

  // 根据图表类型生成配置
  switch (chartType) {
    case 'pie':
      return {
        ...baseOption,
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle',
          textStyle: {
            fontSize: 12,
            color: '#606266'
          }
        },
        series: [
          {
            name: title,
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 8,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 16,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data.map((item: any, index: number) => ({
              value: item.value,
              name: item.name,
              itemStyle: {
                color: getColor(index)
              }
            }))
          }
        ]
      }

    case 'bar':
      return {
        ...baseOption,
        xAxis: {
          type: 'category',
          data: data.map((item: any) => item.name),
          axisTick: {
            alignWithLabel: true
          },
          axisLabel: {
            fontSize: 12,
            color: '#606266'
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 12,
            color: '#606266'
          },
          splitLine: {
            lineStyle: {
              color: '#e4e7ed'
            }
          }
        },
        series: [
          {
            name: title,
            type: 'bar',
            barWidth: '60%',
            data: data.map((item: any, index: number) => ({
              value: item.value,
              itemStyle: {
                color: getColor(index)
              }
            })),
            itemStyle: {
              borderRadius: [4, 4, 0, 0]
            }
          }
        ]
      }

    case 'line':
      return {
        ...baseOption,
        xAxis: {
          type: 'category',
          data: data.map((item: any) => item.name),
          axisLabel: {
            fontSize: 12,
            color: '#606266'
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 12,
            color: '#606266'
          },
          splitLine: {
            lineStyle: {
              color: '#e4e7ed'
            }
          }
        },
        series: [
          {
            name: title,
            type: 'line',
            data: data.map((item: any) => item.value),
            smooth: true,
            lineStyle: {
              color: '#5470c6',
              width: 3
            },
            itemStyle: {
              color: '#5470c6'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(84, 112, 198, 0.3)' },
                  { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
                ]
              }
            }
          }
        ]
      }

    case 'radar':
      return {
        ...baseOption,
        radar: {
          indicator: data.map((item: any) => ({
            name: item.name,
            max: 100
          })),
          center: ['50%', '55%'],
          radius: '60%'
        },
        series: [
          {
            name: title,
            type: 'radar',
            data: [
              {
                value: data.map((item: any) => item.value),
                name: title,
                itemStyle: {
                  color: '#5470c6'
                },
                areaStyle: {
                  color: 'rgba(84, 112, 198, 0.3)'
                }
              }
            ]
          }
        ]
      }

    default:
      return baseOption
  }
})

// 颜色配置
const getColor = (index: number) => {
  const colors = [
    '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
    '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f'
  ]
  return colors[index % colors.length]
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

// 更新图表
const updateChart = () => {
  if (!chartInstance || !chartOption.value) return

  chartInstance.setOption(chartOption.value, true)
}

// 调整图表大小（防抖优化）
let resizeTimer: number | null = null
const resizeChart = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = window.setTimeout(() => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }, 100)
}

// 生命周期
onMounted(() => {
  initChart()
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})

// 监听数据变化
watch(
  () => chartData.value,
  () => {
    // 使用 nextTick 确保 DOM 更新完成后再更新图表
    nextTick(() => {
      updateChart()
    })
  },
  { deep: true }
)
</script>

<template>
  <div class="generic-chart">
    <div
      ref="chartRef"
      class="chart-container"
      v-loading="chartData?.loading || false"
      element-loading-text="正在加载图表数据..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(255, 255, 255, 0.8)"
    ></div>
  </div>
</template>

<style scoped lang="scss">
.generic-chart {
  width: 100%;
  height: 100%;

  .chart-container {
    width: 100%;
    height: 300px;
    min-height: 300px;
  }
}
</style>
