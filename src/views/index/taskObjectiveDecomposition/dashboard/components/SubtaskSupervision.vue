<!-- 子任务督办Tab组件 -->
<script setup lang="ts" name="SubtaskSupervision">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import BaseTableComp from '@/components/common/basetable-comp.vue'
import { useDashboardStore } from '@/stores/dashboardStore'

// Store
const dashboardStore = useDashboardStore()

// 页面状态
const searchKeyword = ref('')

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格列配置
const tableColumns = [
  { field: 'subtaskName', title: '子任务名称', minWidth: 200 },
  { field: 'taskType', title: '任务类型', width: 150, align: 'center' },
  { field: 'supervisionDepartment', title: '督办部门', width: 150 },
  { field: 'supervisor', title: '督办人', width: 100, align: 'center' },
  { field: 'deadline', title: '截止时间', width: 150, align: 'center' },
  { field: 'status', title: '督办状态', width: 100, align: 'center', slot: true }
]

// 表格操作按钮
const tableButtons = [
  { 
    type: 'success' as const, 
    code: 'receive', 
    title: '接收', 
    icon: '', 
    verify: 'true', 
    more: false, 
    showBtn: 'true' 
  }
]

// 计算属性
const filteredData = computed(() => {
  let data = dashboardStore.subtaskSupervisions
  if (searchKeyword.value) {
    data = data.filter(item => 
      item.subtaskName.includes(searchKeyword.value) ||
      item.taskType.includes(searchKeyword.value) ||
      item.supervisionDepartment.includes(searchKeyword.value) ||
      item.supervisor.includes(searchKeyword.value)
    )
  }
  pagination.value.total = data.length
  return data
})

const paginatedData = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredData.value.slice(start, end)
})

// 方法
const handleSearch = () => {
  pagination.value.currentPage = 1
}

const handleTableButton = (button: any, row: any) => {
  if (button.code === 'receive') {
    handleReceive(row)
  }
}

const handleReceive = (row: any) => {
  if (row.status === 'inProgress') {
    dashboardStore.updateSupervisionStatus(row.id, 'completed')
    ElMessage.success(`已接收督办任务：${row.subtaskName}`)
  } else {
    ElMessage.warning('只有进行中的督办任务可以接收')
  }
}

const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
}

const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'inProgress': return 'warning'
    case 'pending': return 'info'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'inProgress': return '进行中'
    case 'pending': return '待接收'
    default: return '未知'
  }
}

// 判断按钮是否可用
const isButtonEnabled = (button: any, row: any) => {
  if (button.code === 'receive') {
    return row.status === 'inProgress'
  }
  return true
}

// 生命周期
onMounted(() => {
  console.log('子任务督办组件已加载')
})
</script>

<template>
  <div class="subtask-supervision">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-controls">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入子任务名称、任务类型、督办部门或督办人"
          style="width: 350px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <BaseTableComp
        :data="paginatedData"
        :colData="tableColumns"
        :buttons="tableButtons"
        :loading="dashboardStore.loading"
        :checkbox="false"
        :visibleHeader="false"
        :visibleSetting="false"
        :visibleIndex="true"
        :currentPage="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :total="pagination.total"
        :auto-height="false"
        :height="500"
        @clickButton="(data: any) => handleTableButton(data.btn, data.scope)"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #status="{ rowData }">
          <el-tag :type="getStatusType(rowData.status)">
            {{ getStatusText(rowData.status) }}
          </el-tag>
        </template>
      </BaseTableComp>
    </div>
  </div>
</template>

<style scoped lang="scss">
.subtask-supervision {
  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .search-controls {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .table-section {
    background: white;
    border-radius: 6px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .subtask-supervision {
    .search-section {
      padding: 12px;

      .search-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;

        .el-input {
          width: 100% !important;
        }
      }
    }
  }
}
</style>
