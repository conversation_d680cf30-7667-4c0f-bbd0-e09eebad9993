<!-- 子任务消息通知Tab组件 -->
<script setup lang="ts" name="SubtaskMessageNotification">
import { ref, computed, onMounted } from 'vue'
import BaseTableComp from '@/components/common/basetable-comp.vue'
import { useDashboardStore } from '@/stores/dashboardStore'

// Store
const dashboardStore = useDashboardStore()

// 页面状态
const searchKeyword = ref('')

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格列配置
const tableColumns = [
  { field: 'notificationContent', title: '通知内容', minWidth: 400 },
  { field: 'receiveTime', title: '接收时间', width: 150, align: 'center' }
]

// 计算属性
const filteredData = computed(() => {
  let data = dashboardStore.subtaskMessageNotifications
  if (searchKeyword.value) {
    data = data.filter(item => 
      item.notificationContent.includes(searchKeyword.value)
    )
  }
  pagination.value.total = data.length
  return data
})

const paginatedData = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredData.value.slice(start, end)
})

// 方法
const handleSearch = () => {
  pagination.value.currentPage = 1
}

const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
}

const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
}

// 生命周期
onMounted(() => {
  console.log('子任务消息通知组件已加载')
})
</script>

<template>
  <div class="subtask-message-notification">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-controls">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入通知内容关键词"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <BaseTableComp
        :data="paginatedData"
        :colData="tableColumns"
        :loading="dashboardStore.loading"
        :checkbox="false"
        :visibleHeader="false"
        :visibleSetting="false"
        :visibleIndex="true"
        :currentPage="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :total="pagination.total"
        :auto-height="false"
        :height="500"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.subtask-message-notification {
  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .search-controls {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .table-section {
    background: white;
    border-radius: 6px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .subtask-message-notification {
    .search-section {
      padding: 12px;

      .search-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;

        .el-input {
          width: 100% !important;
        }
      }
    }
  }
}
</style>
