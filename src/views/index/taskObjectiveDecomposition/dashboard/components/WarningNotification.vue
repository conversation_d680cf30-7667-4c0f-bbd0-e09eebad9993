<!-- 预警通知Tab组件 -->
<script setup lang="ts" name="WarningNotification">
import { ref, computed, onMounted } from 'vue'
import BaseTableComp from '@/components/common/basetable-comp.vue'
import { useDashboardStore } from '@/stores/dashboardStore'

// Store
const dashboardStore = useDashboardStore()

// 页面状态
const searchKeyword = ref('')

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格列配置
const tableColumns = [
  { field: 'subtaskName', title: '子任务名称', minWidth: 200 },
  { field: 'taskType', title: '任务类型', width: 150, align: 'center' },
  { field: 'warningType', title: '预警类型', width: 120, align: 'center', slot: true },
  { field: 'triggerTime', title: '触发时间', width: 150, align: 'center' }
]

// 计算属性
const filteredData = computed(() => {
  let data = dashboardStore.warningNotifications
  if (searchKeyword.value) {
    data = data.filter(item => 
      item.subtaskName.includes(searchKeyword.value) ||
      item.taskType.includes(searchKeyword.value)
    )
  }
  pagination.value.total = data.length
  return data
})

const paginatedData = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredData.value.slice(start, end)
})

// 方法
const handleSearch = () => {
  pagination.value.currentPage = 1
}

const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
}

const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
}

const getWarningTypeClass = (warningType: string) => {
  switch (warningType) {
    case '即将超期': return 'warning'
    case '已超期': return 'danger'
    case '正常': return 'success'
    default: return 'info'
  }
}

// 生命周期
onMounted(() => {
  console.log('预警通知组件已加载')
})
</script>

<template>
  <div class="warning-notification">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-controls">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入子任务名称或任务类型"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <BaseTableComp
        :data="paginatedData"
        :colData="tableColumns"
        :loading="dashboardStore.loading"
        :checkbox="false"
        :visibleHeader="false"
        :visibleSetting="false"
        :visibleIndex="true"
        :currentPage="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :total="pagination.total"
        :auto-height="false"
        :height="500"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #warningType="{ rowData }">
          <el-tag :type="getWarningTypeClass(rowData.warningType)">
            {{ rowData.warningType }}
          </el-tag>
        </template>
      </BaseTableComp>
    </div>
  </div>
</template>

<style scoped lang="scss">
.warning-notification {
  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .search-controls {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .table-section {
    background: white;
    border-radius: 6px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .warning-notification {
    .search-section {
      padding: 12px;

      .search-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;

        .el-input {
          width: 100% !important;
        }
      }
    }
  }
}
</style>
