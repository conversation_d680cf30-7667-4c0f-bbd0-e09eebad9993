<!-- 已完成子任务Tab组件 -->
<script setup lang="ts" name="CompletedSubtasks">
import { ref, computed, onMounted } from 'vue'
import BaseTableComp from '@/components/common/basetable-comp.vue'
import { useDashboardStore } from '@/stores/dashboardStore'

// Store
const dashboardStore = useDashboardStore()

// 页面状态
const searchKeyword = ref('')

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格列配置
const tableColumns = [
  { field: 'subtaskName', title: '子任务名称', minWidth: 200 },
  { field: 'subtaskType', title: '子任务类型', width: 150, align: 'center' },
  { field: 'startTime', title: '开始时间', width: 150, align: 'center' },
  { field: 'completionTime', title: '完成时间', width: 150, align: 'center' },
  { field: 'duration', title: '耗时', width: 100, align: 'center', slot: true }
]

// 计算属性
const filteredData = computed(() => {
  let data = dashboardStore.completedSubtasks
  if (searchKeyword.value) {
    data = data.filter(item => 
      item.subtaskName.includes(searchKeyword.value) ||
      item.subtaskType.includes(searchKeyword.value)
    )
  }
  pagination.value.total = data.length
  return data
})

const paginatedData = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredData.value.slice(start, end)
})

// 方法
const handleSearch = () => {
  pagination.value.currentPage = 1
}

const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
}

const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
}

const getDurationColor = (duration: string) => {
  const days = parseInt(duration)
  if (days <= 1) return 'success'
  if (days <= 3) return 'warning'
  return 'danger'
}

// 生命周期
onMounted(() => {
  console.log('已完成子任务组件已加载')
})
</script>

<template>
  <div class="completed-subtasks">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-controls">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入子任务名称或子任务类型"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <BaseTableComp
        :data="paginatedData"
        :colData="tableColumns"
        :loading="dashboardStore.loading"
        :checkbox="false"
        :visibleHeader="false"
        :visibleSetting="false"
        :visibleIndex="true"
        :currentPage="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :total="pagination.total"
        :auto-height="false"
        :height="500"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #duration="{ rowData }">
          <el-tag :type="getDurationColor(rowData.duration)">
            {{ rowData.duration }}
          </el-tag>
        </template>
      </BaseTableComp>
    </div>
  </div>
</template>

<style scoped lang="scss">
.completed-subtasks {
  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .search-controls {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .table-section {
    background: white;
    border-radius: 6px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .completed-subtasks {
    .search-section {
      padding: 12px;

      .search-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;

        .el-input {
          width: 100% !important;
        }
      }
    }
  }
}
</style>
