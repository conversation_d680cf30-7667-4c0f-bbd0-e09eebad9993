<!-- 用户角色分类统计图表 -->
<script setup lang="ts" name="UserRoleChart">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import { useDashboardStore } from '@/stores/dashboardStore'

// Store
const dashboardStore = useDashboardStore()

// 图表引用
const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 图表数据
const chartData = computed(() => {
  return dashboardStore.getChartData('userRoleStats')
})

// 图表配置
const chartOption = computed(() => {
  if (!chartData.value?.data) return null

  const data = chartData.value.data
  const xAxisData = data.map((item: any) => item.name)
  const seriesData = data.map((item: any) => item.value)

  return {
    title: {
      text: '用户角色分类统计',
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        fontSize: 12,
        color: '#606266'
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 12,
        color: '#606266'
      },
      splitLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      }
    },
    series: [
      {
        name: '用户数量',
        type: 'bar',
        barWidth: '60%',
        data: seriesData.map((value: number, index: number) => ({
          value,
          itemStyle: {
            color: getColor(index)
          }
        })),
        itemStyle: {
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
})

// 颜色配置
const getColor = (index: number) => {
  const colors = [
    '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
    '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f'
  ]
  return colors[index % colors.length]
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

// 更新图表
const updateChart = () => {
  if (!chartInstance || !chartOption.value) return

  chartInstance.setOption(chartOption.value, true)
}

// 调整图表大小
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 生命周期
onMounted(() => {
  initChart()
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})

// 监听数据变化
watch(
  () => chartData.value,
  () => {
    updateChart()
  },
  { deep: true }
)
</script>

<template>
  <div class="user-role-chart">
    <div
      ref="chartRef"
      class="chart-container"
      v-loading="chartData?.loading || false"
      element-loading-text="正在加载图表数据..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(255, 255, 255, 0.8)"
    ></div>
  </div>
</template>

<style scoped lang="scss">
.user-role-chart {
  width: 100%;
  height: 100%;

  .chart-container {
    width: 100%;
    height: 300px;
    min-height: 300px;
  }
}
</style>
