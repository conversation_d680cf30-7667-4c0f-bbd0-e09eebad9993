<!-- 看板模式主页面 -->
<script setup lang="ts" name="TaskObjectiveDecompositionDashboard">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import AuditManagement from './components/AuditManagement.vue'
import WarningNotification from './components/WarningNotification.vue'
import SubtaskSupervision from './components/SubtaskSupervision.vue'
import SubtaskMessageNotification from './components/SubtaskMessageNotification.vue'
import SubtaskTodoItems from './components/SubtaskTodoItems.vue'
import CompletedSubtasks from './components/CompletedSubtasks.vue'
import TaskFeedback from './components/TaskFeedback.vue'
import HistoryRecord from './components/HistoryRecord.vue'

// 路由
const router = useRouter()

// 页面状态
const activeTab = ref('auditManagement')

// Tab配置
const tabList = [
  { key: 'auditManagement', label: '审核管理' },
  { key: 'warningNotification', label: '预警通知' },
  { key: 'subtaskSupervision', label: '子任务督办' },
  { key: 'subtaskMessageNotification', label: '子任务消息通知' },
  { key: 'subtaskTodoItems', label: '子任务待办事项' },
  { key: 'completedSubtasks', label: '已完成子任务' },
  { key: 'taskFeedback', label: '任务反馈' },
  { key: 'historyRecord', label: '历史记录' }
]

// 返回上一页
const handleBack = () => {
  router.back()
}

// Tab切换处理
const handleTabClick = (tab: any) => {
  activeTab.value = tab.name
  console.log('切换到Tab:', tab.name)
}

// 生命周期
onMounted(() => {
  console.log('看板模式页面已加载')
})
</script>

<route>
{
  meta: {
    title: '看板模式',
    ignoreLabel: false
  }
}
</route>

<template>
  <div class="dashboard-container">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-left">
        <el-button 
          type="text" 
          :icon="ArrowLeft" 
          @click="handleBack"
          class="back-button"
        >
          返回
        </el-button>
        <h1 class="page-title">看板模式</h1>
      </div>
    </div>

    <!-- Tab切换区域 -->
    <div class="tab-container">
      <el-tabs 
        v-model="activeTab" 
        @tab-click="handleTabClick"
        class="dashboard-tabs"
      >
        <el-tab-pane 
          v-for="tab in tabList" 
          :key="tab.key"
          :label="tab.label" 
          :name="tab.key"
        >
          <!-- Tab内容区域 -->
          <div class="tab-content">
            <!-- 审核管理Tab -->
            <div v-if="activeTab === 'auditManagement'" class="audit-management">
              <AuditManagement />
            </div>

            <!-- 预警通知Tab -->
            <div v-else-if="activeTab === 'warningNotification'" class="warning-notification">
              <WarningNotification />
            </div>

            <!-- 子任务督办Tab -->
            <div v-else-if="activeTab === 'subtaskSupervision'" class="subtask-supervision">
              <SubtaskSupervision />
            </div>

            <!-- 子任务消息通知Tab -->
            <div v-else-if="activeTab === 'subtaskMessageNotification'" class="subtask-message-notification">
              <SubtaskMessageNotification />
            </div>

            <!-- 子任务待办事项Tab -->
            <div v-else-if="activeTab === 'subtaskTodoItems'" class="subtask-todo-items">
              <SubtaskTodoItems />
            </div>

            <!-- 已完成子任务Tab -->
            <div v-else-if="activeTab === 'completedSubtasks'" class="completed-subtasks">
              <CompletedSubtasks />
            </div>

            <!-- 任务反馈Tab -->
            <div v-else-if="activeTab === 'taskFeedback'" class="task-feedback">
              <TaskFeedback />
            </div>

            <!-- 历史记录Tab -->
            <div v-else-if="activeTab === 'historyRecord'" class="history-record">
              <HistoryRecord />
            </div>

            <!-- 其他Tab占位内容 -->
            <div v-else class="tab-placeholder">
              <div class="placeholder-content">
                <h3>{{ tabList.find(t => t.key === activeTab)?.label }}</h3>
                <p>该功能正在开发中，敬请期待...</p>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style scoped lang="scss">
.dashboard-container {
  padding: 20px;
  min-height: 100vh;
  background-color: #f5f7fa;

  .dashboard-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-button {
        font-size: 14px;
        color: #409eff;
        
        &:hover {
          color: #66b1ff;
        }
      }

      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  .tab-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .dashboard-tabs {
      :deep(.el-tabs__header) {
        margin: 0;
        padding: 0 20px;
        background: #fafafa;
        border-bottom: 1px solid #e4e7ed;
      }

      :deep(.el-tabs__nav-wrap) {
        padding: 0;
      }

      :deep(.el-tabs__item) {
        padding: 0 20px;
        height: 50px;
        line-height: 50px;
        font-size: 14px;
        color: #606266;
        border-bottom: 2px solid transparent;

        &.is-active {
          color: #409eff;
          border-bottom-color: #409eff;
        }

        &:hover {
          color: #409eff;
        }
      }

      :deep(.el-tabs__content) {
        padding: 0;
      }

      :deep(.el-tab-pane) {
        min-height: 600px;
      }
    }

    .tab-content {
      padding: 20px;

      .audit-management,
      .tab-placeholder {
        .content-placeholder,
        .placeholder-content {
          text-align: center;
          padding: 60px 20px;

          h3 {
            margin: 0 0 16px 0;
            font-size: 20px;
            color: #303133;
          }

          p {
            margin: 0;
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;

    .dashboard-header {
      padding: 12px 16px;

      .header-left {
        gap: 12px;

        .page-title {
          font-size: 20px;
        }
      }
    }

    .tab-container {
      .dashboard-tabs {
        :deep(.el-tabs__item) {
          padding: 0 12px;
          font-size: 13px;
        }
      }

      .tab-content {
        padding: 16px;
      }
    }
  }
}
</style>
