import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 审核状态类型
export type AuditStatus = 'pending' | 'approved' | 'rejected'

// 审核记录接口
export interface AuditRecord {
  id: number
  auditCategory: string
  applicationContent: string
  applicationDepartment: string
  applicant: string
  applicationTime: string
  status: AuditStatus
}

// 预警通知接口
export interface WarningNotification {
  id: number
  subtaskName: string
  taskType: string
  warningType: string
  triggerTime: string
}

// 子任务督办接口
export interface SubtaskSupervision {
  id: number
  subtaskName: string
  taskType: string
  supervisionDepartment: string
  supervisor: string
  deadline: string
  status: 'pending' | 'inProgress' | 'completed'
}

// 子任务消息通知接口
export interface SubtaskMessageNotification {
  id: number
  notificationContent: string
  receiveTime: string
}

// 子任务待办事项接口
export interface SubtaskTodoItem {
  id: number
  subtaskName: string
  subtaskType: string
  itemName: string
  deadline: string
}

// 已完成子任务接口
export interface CompletedSubtask {
  id: number
  subtaskName: string
  subtaskType: string
  startTime: string
  completionTime: string
  duration: string
}

// 任务反馈接口
export interface TaskFeedback {
  id: number
  feedbackType: string
  feedbackContent: string
  feedbackAccount: string
  feedbackTime: string
}

// 历史记录接口
export interface HistoryRecord {
  id: number
  taskType: string
  taskName: string
  creator: string
  createTime: string
  taskStatus: string
}

// 图表数据接口
export interface ChartData {
  id: string
  title: string
  type: 'pie' | 'bar' | 'line' | 'radar'
  data: any[]
  loading: boolean
}

export const useDashboardStore = defineStore('dashboard', () => {
  // 状态
  const loading = ref(false)
  const error = ref<string | null>(null)
  const auditRecords = ref<AuditRecord[]>([])
  const chartDataList = ref<ChartData[]>([])

  // 新增Tab页面数据
  const warningNotifications = ref<WarningNotification[]>([])
  const subtaskSupervisions = ref<SubtaskSupervision[]>([])
  const subtaskMessageNotifications = ref<SubtaskMessageNotification[]>([])
  const subtaskTodoItems = ref<SubtaskTodoItem[]>([])
  const completedSubtasks = ref<CompletedSubtask[]>([])
  const taskFeedbacks = ref<TaskFeedback[]>([])
  const historyRecords = ref<HistoryRecord[]>([])

  // 初始化审核记录数据（模拟接口请求）
  const initAuditRecords = async () => {
    // 模拟表格数据加载延迟
    await new Promise(resolve => setTimeout(resolve, 800))
    auditRecords.value = [
      {
        id: 1,
        auditCategory: '用户权限审核',
        applicationContent: '业务数据新增权限申请',
        applicationDepartment: '海淀区七里河街道',
        applicant: '张明',
        applicationTime: '2025-05-11 9:20',
        status: 'pending'
      },
      {
        id: 2,
        auditCategory: '用户角色审核',
        applicationContent: '管理员角色数据维护权限申请',
        applicationDepartment: '海淀区七里河街道',
        applicant: '张明',
        applicationTime: '2025-05-10 9:20',
        status: 'approved'
      },
      {
        id: 3,
        auditCategory: '部门审核',
        applicationContent: '抗疫社区数据管理者权限申请',
        applicationDepartment: '七里河街道抗疫社区',
        applicant: '刘涛',
        applicationTime: '2025-05-10 9:20',
        status: 'rejected'
      },
      {
        id: 4,
        auditCategory: '成员审核',
        applicationContent: '刘涛数据管理权限申请',
        applicationDepartment: '七里河街道抗疫社区',
        applicant: '刘涛',
        applicationTime: '2025-05-10 9:20',
        status: 'pending'
      },
      {
        id: 5,
        auditCategory: '任务数据审核',
        applicationContent: '数据编辑申请',
        applicationDepartment: '七里河街道抗疫社区',
        applicant: '刘涛',
        applicationTime: '2025-05-10 9:20',
        status: 'approved'
      },
      {
        id: 6,
        auditCategory: '子任务数据审核',
        applicationContent: '数据编辑申请',
        applicationDepartment: '七里河街道抗疫社区',
        applicant: '刘涛',
        applicationTime: '2025-05-10 9:20',
        status: 'pending'
      },
      {
        id: 7,
        auditCategory: '任务关系审核',
        applicationContent: '任务关系更新申请',
        applicationDepartment: '海淀区七里河街道',
        applicant: '张明',
        applicationTime: '2025-05-10 9:20',
        status: 'approved'
      },
      {
        id: 8,
        auditCategory: '子任务属性审核',
        applicationContent: '子任务属性更新申请',
        applicationDepartment: '七里河街道抗疫社区',
        applicant: '刘涛',
        applicationTime: '2025-05-10 9:20',
        status: 'pending'
      },
      {
        id: 9,
        auditCategory: '可视化布局审核',
        applicationContent: '可视化布局更新申请',
        applicationDepartment: '海淀区七里河街道',
        applicant: '张明',
        applicationTime: '2025-05-10 9:20',
        status: 'approved'
      },
      {
        id: 10,
        auditCategory: '部门审核',
        applicationContent: '年一级社区数据管理者权限申请',
        applicationDepartment: '七里河街道一级社区',
        applicant: '林晓明',
        applicationTime: '2025-05-10 9:20',
        status: 'rejected'
      }
    ]
  }

  // 初始化预警通知数据
  const initWarningNotifications = () => {
    warningNotifications.value = [
      {
        id: 1,
        subtaskName: '永川区民政局填报流程',
        taskType: '临时报表子任务',
        warningType: '即将超期',
        triggerTime: '2025-05-11 9:20'
      },
      {
        id: 2,
        subtaskName: '系统填报流程',
        taskType: '业务报表子任务',
        warningType: '即将超期',
        triggerTime: '2025-05-10 9:20'
      }
    ]
  }

  // 初始化子任务督办数据
  const initSubtaskSupervisions = () => {
    subtaskSupervisions.value = [
      {
        id: 1,
        subtaskName: '永川区民政局填报流程',
        taskType: '临时报表子任务',
        supervisionDepartment: '永川区民政局',
        supervisor: '刘越',
        deadline: '2025-05-11 9:20',
        status: 'pending'
      },
      {
        id: 2,
        subtaskName: '系统填报流程',
        taskType: '业务报表子任务',
        supervisionDepartment: '永川区民政局',
        supervisor: '刘越',
        deadline: '2025-05-10 9:20',
        status: 'inProgress'
      }
    ]
  }

  // 初始化子任务消息通知数据
  const initSubtaskMessageNotifications = () => {
    subtaskMessageNotifications.value = [
      {
        id: 1,
        notificationContent: '业务表数据更新通知：销售数据表 BIZ-2023-0585 已完成自动更新，共更新 128 条记录。',
        receiveTime: '2025-05-11 9:20'
      },
      {
        id: 2,
        notificationContent: '任务审核通过通知：您提交的"产品库存表更新"任务已审核通过，审核人：王经理。',
        receiveTime: '2025-05-10 9:20'
      }
    ]
  }

  // 初始化子任务待办事项数据
  const initSubtaskTodoItems = () => {
    subtaskTodoItems.value = [
      {
        id: 1,
        subtaskName: '销售数据表',
        subtaskType: '临时报表子任务',
        itemName: '数据录入',
        deadline: '2025-05-11 9:20'
      },
      {
        id: 2,
        subtaskName: '产品库存表',
        subtaskType: '业务报表子任务',
        itemName: '数据审核',
        deadline: '2025-05-10 9:20'
      }
    ]
  }

  // 初始化已完成子任务数据
  const initCompletedSubtasks = () => {
    completedSubtasks.value = [
      {
        id: 1,
        subtaskName: '销售数据表',
        subtaskType: '临时报表子任务',
        startTime: '2025-05-11 9:20',
        completionTime: '2025-05-12 11:24',
        duration: '1天'
      },
      {
        id: 2,
        subtaskName: '产品库存表',
        subtaskType: '业务报表子任务',
        startTime: '2025-05-10 9:20',
        completionTime: '2025-05-13 15:20',
        duration: '2天'
      }
    ]
  }

  // 初始化任务反馈数据
  const initTaskFeedbacks = () => {
    taskFeedbacks.value = [
      {
        id: 1,
        feedbackType: '业务责任务',
        feedbackContent: '第一次运营数据收集任务已完成，数据质量良好，能不能把提交的门槛设置成一个可修改的功能，方便清洁，查看数据收集任务已完成的数据',
        feedbackAccount: '杨军-ykz-***********',
        feedbackTime: '2025-07-18 10:12:01'
      },
      {
        id: 2,
        feedbackType: '业务责任务',
        feedbackContent: 'xxxxxxxxxxxxxxxxxxxxxxx',
        feedbackAccount: '张三-ykz-***********',
        feedbackTime: '2025-07-18 10:12:01'
      },
      {
        id: 3,
        feedbackType: '临时报表任务',
        feedbackContent: 'xxxxxxxxxxxxxxxxxxxxxxx',
        feedbackAccount: '张三-ykz-***********',
        feedbackTime: '2025-07-18 10:12:01'
      },
      {
        id: 4,
        feedbackType: '临时报表任务',
        feedbackContent: 'xxxxxxxxxxxxxxxxxxxxxxx',
        feedbackAccount: '张三-ykz-***********',
        feedbackTime: '2025-07-18 10:12:01'
      },
      {
        id: 5,
        feedbackType: '临时报表任务',
        feedbackContent: 'xxxxxxxxxxxxxxxxxxxxxxx',
        feedbackAccount: '张三-ykz-***********',
        feedbackTime: '2025-07-18 10:12:01'
      },
      {
        id: 6,
        feedbackType: '业务责子任务',
        feedbackContent: 'xxxxxxxxxxxxxxxxxxxxxxx',
        feedbackAccount: '张三-ykz-***********',
        feedbackTime: '2025-07-18 10:12:01'
      },
      {
        id: 7,
        feedbackType: '业务责子任务',
        feedbackContent: 'xxxxxxxxxxxxxxxxxxxxxxx',
        feedbackAccount: '张三-ykz-***********',
        feedbackTime: '2025-07-18 10:12:01'
      },
      {
        id: 8,
        feedbackType: '业务责子任务',
        feedbackContent: 'xxxxxxxxxxxxxxxxxxxxxxx',
        feedbackAccount: '张三-ykz-***********',
        feedbackTime: '2025-07-18 10:12:01'
      },
      {
        id: 9,
        feedbackType: '临时报表子任务',
        feedbackContent: 'xxxxxxxxxxxxxxxxxxxxxxx',
        feedbackAccount: '张三-ykz-***********',
        feedbackTime: '2025-07-18 10:12:01'
      },
      {
        id: 10,
        feedbackType: '临时报表子任务',
        feedbackContent: 'xxxxxxxxxxxxxxxxxxxxxxx',
        feedbackAccount: '张三-ykz-***********',
        feedbackTime: '2025-07-18 10:12:01'
      }
    ]
  }

  // 初始化历史记录数据
  const initHistoryRecords = () => {
    historyRecords.value = [
      {
        id: 1,
        taskType: '业务责任务',
        taskName: 'cq测试下发 不要动',
        creator: '杨超东',
        createTime: '2025-03-10 09:19:51',
        taskStatus: '进行中'
      },
      {
        id: 2,
        taskType: '业务责任务',
        taskName: '移动端测试998',
        creator: '杨超东',
        createTime: '2025-03-10 08:43:21',
        taskStatus: '进行中'
      },
      {
        id: 3,
        taskType: '临时报表任务',
        taskName: 'cq测试下发',
        creator: '杨超东',
        createTime: '2025-03-10 08:40:44',
        taskStatus: '进行中'
      },
      {
        id: 4,
        taskType: '临时报表任务',
        taskName: 'cq内部2',
        creator: '杨超东',
        createTime: '2025-03-10 08:31:04',
        taskStatus: '进行中'
      },
      {
        id: 5,
        taskType: '临时报表任务',
        taskName: '刘志豪测试任务内部',
        creator: '杨超东',
        createTime: '2025-03-10 08:22:12',
        taskStatus: '进行中'
      },
      {
        id: 6,
        taskType: '业务责子任务',
        taskName: '移动端测试任务】多个不要删',
        creator: '杨超东',
        createTime: '2025-03-10 07:51:15',
        taskStatus: '进行中'
      },
      {
        id: 7,
        taskType: '业务责子任务',
        taskName: '0306临时报表任务',
        creator: '杨超东',
        createTime: '2025-03-06 08:23:53',
        taskStatus: '进行中'
      },
      {
        id: 8,
        taskType: '业务责子任务',
        taskName: '测试报表数据',
        creator: '杨超东',
        createTime: '2025-03-06 02:27:05',
        taskStatus: '进行中'
      },
      {
        id: 9,
        taskType: '临时报表子任务',
        taskName: '测试报表数据',
        creator: '杨超东',
        createTime: '2025-03-06 02:00:07',
        taskStatus: '待审核'
      },
      {
        id: 10,
        taskType: '临时报表子任务',
        taskName: 'cq 动态',
        creator: '杨超东',
        createTime: '2025-03-05 03:41:29',
        taskStatus: '进行中'
      }
    ]
  }

  // 初始化图表数据（分阶段加载）
  const initChartData = async () => {
    // 先设置所有图表为加载状态
    const chartConfigs = [
      {
        id: 'userPermissionStats',
        title: '用户权限分类统计',
        type: 'pie',
        data: [
          { name: '管理员', value: 30 },
          { name: '数据分析师', value: 160 },
          { name: '普通用户', value: 420 },
          { name: '系统管理员', value: 90 },
          { name: '数据录入', value: 280 }
        ],
        loading: false
      },
      {
        id: 'userRoleStats',
        title: '用户角色分类统计',
        type: 'bar',
        data: [
          { name: '超级管理员', value: 30 },
          { name: '数据分析师', value: 160 },
          { name: '普通用户', value: 420 }
        ],
        loading: false
      },
      {
        id: 'departmentAuditProgress',
        title: '部门审核进度分析',
        type: 'bar',
        data: [
          { name: '财务部', value: 800 },
          { name: '市民部', value: 600 },
          { name: '技术部', value: 400 }
        ],
        loading: false
      },
      {
        id: 'memberAuditProgress',
        title: '成员审核进度分析',
        type: 'line',
        data: [
          { name: '1月', value: 60 },
          { name: '2月', value: 70 },
          { name: '3月', value: 80 },
          { name: '4月', value: 75 },
          { name: '5月', value: 85 }
        ],
        loading: false
      },
      {
        id: 'taskDataAuditProgress',
        title: '任务数据审核进度分析',
        type: 'bar',
        data: [
          { name: '已完成', value: 120 },
          { name: '进行中', value: 80 },
          { name: '待开始', value: 45 }
        ],
        loading: false
      },
      {
        id: 'subtaskDataAuditProgress',
        title: '子任务数据审核进度分析',
        type: 'pie',
        data: [
          { name: '已审核', value: 65 },
          { name: '待审核', value: 35 }
        ],
        loading: false
      },
      {
        id: 'taskRelationAuditProgress',
        title: '任务关系审核进度分析',
        type: 'bar',
        data: [
          { name: '关系确认', value: 90 },
          { name: '关系待确认', value: 60 },
          { name: '关系异常', value: 20 }
        ],
        loading: false
      },
      {
        id: 'subtaskAttributeAuditProgress',
        title: '子任务属性审核进度分析',
        type: 'line',
        data: [
          { name: '属性完整', value: 85 },
          { name: '属性缺失', value: 15 }
        ],
        loading: false
      },
      {
        id: 'visualLayoutAuditProgress',
        title: '可视化布局审核进度分析',
        type: 'radar',
        data: [
          { name: '布局合理性', value: 80 },
          { name: '用户体验', value: 75 },
          { name: '性能表现', value: 90 },
          { name: '兼容性', value: 85 }
        ],
        loading: false
      }
    ]

    // 初始化所有图表为加载状态
    chartDataList.value = chartConfigs.map(config => ({
      ...config,
      loading: true
    }))

    // 分阶段加载图表数据
    for (let i = 0; i < chartConfigs.length; i++) {
      // 模拟每个图表的加载延迟
      await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300))

      // 更新对应图表的数据
      const chartIndex = chartDataList.value.findIndex(chart => chart.id === chartConfigs[i].id)
      if (chartIndex !== -1) {
        chartDataList.value[chartIndex] = {
          ...chartConfigs[i],
          loading: false
        }
      }
    }
  }

  // 计算属性
  const auditStatistics = computed(() => {
    const total = auditRecords.value.length
    const pending = auditRecords.value.filter(r => r.status === 'pending').length
    const approved = auditRecords.value.filter(r => r.status === 'approved').length
    const rejected = auditRecords.value.filter(r => r.status === 'rejected').length

    return {
      total,
      pending,
      approved,
      rejected,
      pendingRate: total > 0 ? ((pending / total) * 100).toFixed(1) : '0',
      approvedRate: total > 0 ? ((approved / total) * 100).toFixed(1) : '0',
      rejectedRate: total > 0 ? ((rejected / total) * 100).toFixed(1) : '0'
    }
  })

  // 方法
  const updateAuditStatus = (id: number, status: AuditStatus) => {
    const record = auditRecords.value.find(r => r.id === id)
    if (record) {
      record.status = status
    }
  }

  const refreshData = async () => {
    loading.value = true
    error.value = null
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟随机错误（10%概率）
      if (Math.random() < 0.1) {
        throw new Error('网络连接失败，请稍后重试')
      }

      await initAuditRecords()
      await initChartData()

      // 刷新其他Tab页面数据
      initWarningNotifications()
      initSubtaskSupervisions()
      initSubtaskMessageNotifications()
      initSubtaskTodoItems()
      initCompletedSubtasks()
      initTaskFeedbacks()
      initHistoryRecords()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '数据加载失败'
      console.error('数据刷新失败:', err)
    } finally {
      loading.value = false
    }
  }

  const getChartData = (chartId: string) => {
    return chartDataList.value.find(chart => chart.id === chartId)
  }

  // 初始化数据（模拟真实接口请求）
  const initialize = async () => {
    loading.value = true
    error.value = null

    try {
      // 模拟接口请求延迟（模拟真实的网络请求时间）
      await new Promise(resolve => setTimeout(resolve, 1500))

      // 模拟网络不稳定（5%概率失败）
      if (Math.random() < 0.05) {
        throw new Error('网络连接超时，请检查网络后重试')
      }

      await initAuditRecords()
      await initChartData()

      // 初始化其他Tab页面数据
      initWarningNotifications()
      initSubtaskSupervisions()
      initSubtaskMessageNotifications()
      initSubtaskTodoItems()
      initCompletedSubtasks()
      initTaskFeedbacks()
      initHistoryRecords()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '数据加载失败'
      console.error('数据初始化失败:', err)
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  // 更新督办状态
  const updateSupervisionStatus = (id: number, status: 'pending' | 'inProgress' | 'completed') => {
    const supervision = subtaskSupervisions.value.find(s => s.id === id)
    if (supervision) {
      supervision.status = status
    }
  }

  return {
    // 状态
    loading,
    error,
    auditRecords,
    chartDataList,
    warningNotifications,
    subtaskSupervisions,
    subtaskMessageNotifications,
    subtaskTodoItems,
    completedSubtasks,
    taskFeedbacks,
    historyRecords,

    // 计算属性
    auditStatistics,

    // 方法
    updateAuditStatus,
    updateSupervisionStatus,
    refreshData,
    getChartData,
    initialize,
    clearError
  }
})
