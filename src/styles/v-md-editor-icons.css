/* v-md-editor 工具栏图标修复 */

/* 基础图标样式 */
.v-md-editor__toolbar-item::before {
  font-family: 'v-md-editor-icon';
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  font-size: 16px;
}

/* 撤销图标 */
.v-md-editor__toolbar-item-undo::before {
  content: '↶';
}

/* 重做图标 */
.v-md-editor__toolbar-item-redo::before {
  content: '↷';
}

/* 清空图标 */
.v-md-editor__toolbar-item-clear::before {
  content: '🗑';
}

/* 标题图标 */
.v-md-editor__toolbar-item-h::before {
  content: 'H';
  font-weight: bold;
}

/* 粗体图标 */
.v-md-editor__toolbar-item-bold::before {
  content: 'B';
  font-weight: bold;
}

/* 斜体图标 */
.v-md-editor__toolbar-item-italic::before {
  content: 'I';
  font-style: italic;
}

/* 删除线图标 */
.v-md-editor__toolbar-item-strikethrough::before {
  content: 'S';
  text-decoration: line-through;
}

/* 引用图标 */
.v-md-editor__toolbar-item-quote::before {
  content: '"';
  font-size: 18px;
}

/* 无序列表图标 */
.v-md-editor__toolbar-item-ul::before {
  content: '•';
  font-size: 18px;
}

/* 有序列表图标 */
.v-md-editor__toolbar-item-ol::before {
  content: '1.';
}

/* 表格图标 */
.v-md-editor__toolbar-item-table::before {
  content: '⊞';
}

/* 分割线图标 */
.v-md-editor__toolbar-item-hr::before {
  content: '—';
}

/* 链接图标 */
.v-md-editor__toolbar-item-link::before {
  content: '🔗';
}

/* 图片图标 */
.v-md-editor__toolbar-item-image::before {
  content: '🖼';
}

/* 代码块图标 */
.v-md-editor__toolbar-item-code::before {
  content: '</>';
  font-family: monospace;
}

/* 保存图标 */
.v-md-editor__toolbar-item-save::before {
  content: '💾';
}

/* 预览图标 */
.v-md-icon-preview::before {
  content: '👁';
}

/* 目录导航图标 */
.v-md-icon-toc::before {
  content: '📋';
}

/* 同步滚动图标 */
.v-md-icon-sync::before {
  content: '🔄';
}

/* 全屏图标 */
.v-md-icon-fullscreen::before {
  content: '⛶';
}

/* 工具栏项目样式优化 */
.v-md-editor__toolbar-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.v-md-editor__toolbar-item:hover {
  background-color: #f5f5f5;
}

.v-md-editor__toolbar-item--active {
  background-color: #e6f7ff;
  color: #1890ff;
}

/* 工具栏分隔符 */
.v-md-editor__toolbar-divider {
  width: 1px;
  height: 20px;
  background-color: #e8e8e8;
  margin: 0 8px;
}
